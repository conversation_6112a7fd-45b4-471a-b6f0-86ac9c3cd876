<?php

namespace api\modules\v1\controllers;

use common\helpers\DataHelper;
use common\helpers\LeadHelper;
use Yii;
use common\services\StudentService;
use common\models\Course;
use common\models\City;
use common\models\College;
use common\models\Degree;
use common\models\DsaStateMapping;
use common\models\Exam;
use common\models\Specialization;
use common\models\State;
use common\models\Student;
use common\models\StudentActivity;
use common\services\LeadService;
use common\services\UserService;
use yii\helpers\Json;
use yii\web\Response;

class StudentLeadFromCaptureController extends \yii\web\Controller
{
    protected $studentService;

    public function __construct(
        $id,
        $module,
        StudentService $studentService,
        $config = []
    ) {
        $this->studentService = $studentService;
        parent::__construct($id, $module, $config);
    }

    public function actionIndex()
    {
        $key = md5('leads@data123');
        $authorization = Yii::$app->request->headers->get('authorization');
        $page = 1;
        if ($key == $authorization) {
            $data = json_decode(Yii::$app->request->post('leads_data'));
            $new_college_id = College::find()->select(['id'])->where(['old_id' => $data->college_id])->one();

            $lead_data['LeadForm'] = [
                'name' => $data->name,
                'email' => $data->email,
                'mobile' => $data->phone,
                'entity_id' => empty($new_college_id) ? '' : $new_college_id['id'],
                'utm_source' => $data->utm_source ?? '',
                'utm_medium' => $data->utm_medium ?? '',
                'utm_campaign' => $data->utm_campaign ?? '',
                'url' => $data->url ?? '',
                'interested_course' => $data->course ?? '', //hidden field
                'course_interested' => $data->course_interested ?? '', //dropdown
                'current_location' => empty($data->current_city) ? '' : $data->current_city,
                'current_state' => $data->current_state ?? '',
                'interested_location' => $data->city ?? '',
                'interested_state' => $data->state ?? '',
                'education_budget' => $data->education_budget ?? '',
                'scholarship' => $data->scholarship ?? '',
                'percentile' => $data->percentile ?? '',
                'scholarship_percentage' => $data->scholarship_percentage ?? '',
                'specialization_id' => $data->specialization_id ?? '',
                'stream' => $data->stream_id ?? '',
                'highest_qualification' => $data->highest_qualification ?? '',
                'user_ip_address' => $data->user_ip_address ?? '',
                'entity' => '',
                'exam_id' => $data->exam_id ?? ''
            ];

            $lead_data['user_type'] = Student::USER_TYPE_CLP;
            $lead_data['lead_entries'] = DataHelper::$leadSource['clp'];
            $lead_data['old_page'] = $page;

            $update_student_info = $this->studentService->updateStudent($lead_data, [], 'clp-preference');
            return $update_student_info;
        }
    }

    public function actionUnbounce()
    {
        $page = 1;
        $data = json_decode(Yii::$app->request->post('data_json'));
        $course_id = !empty($data->course[0]) && isset(LeadHelper::$unbounceCourseMapping[$data->course[0]]) ? LeadHelper::$unbounceCourseMapping[$data->course[0]] : '';
        if (empty($course_id)) {
            return [];
        }
        $courseData = (new LeadService)->getCourseData($course_id);
        $current_location_city = City::find()->select(['id', 'state_id'])->where(['name' => $data->city[0]])->one();
        $current_location_state = State::find()->select(['id'])->where(['name' => $data->state[0]])->one();
        $lead_data['LeadForm'] = [
            'name' => $data->name[0] ?? '',
            'email' => $data->email[0] ?? '',
            'mobile' => $data->phone_number[0] ?? '',
            'entity_id' => 0,
            'utm_source' => $data->utm_source[0] ?? '',
            'utm_medium' => $data->utm_medium[0] ?? '',
            'utm_campaign' => $data->utm_campaign[0] ?? '',
            'url' => $data->page_url[0] ?? '',
            'interested_course' => isset($courseData) && !empty($courseData['parent_id']) ? $courseData['parent_id'] : $course_id,
            'course_interested' => '',
            'current_location' => $current_location_city['id'] ?? null,
            'current_state' => empty($data->state[0]) ? ($current_location_city['state_id'] ?? '') : ($current_location_state['id'] ?? ''),
            'interested_location' => '',
            'interested_state' => '',
            'education_budget' => '',
            'scholarship' => '',
            'percentile' => '',
            'scholarship_percentage' => '',
            'specialization_id' => isset($courseData) && !empty($courseData['specialization_id']) ? $courseData['specialization_id'] : null,
            'stream' => '',
            'highest_qualification' => isset($courseData) && !empty($courseData['highest_qualification']) ? $courseData['highest_qualification'] : null,
            'user_ip_address' => $data->ip_address[0] ?? '',
            'entity' => ''
        ];
        $lead_data['user_type'] = Student::USER_TYPE_UNBOUNCE;
        $lead_data['lead_entries'] = DataHelper::$leadSource['unbounce'];
        $lead_data['old_page'] = $page;
        $update_student_info = $this->studentService->updateStudent($lead_data, []);
        return $update_student_info;
    }

    public function actionSponsorCollege()
    {
        $key = md5('leads@data123');
        $authorization = Yii::$app->request->headers->get('authorization');
        if ($key == $authorization) {
            $data = json_decode(Yii::$app->request->post('leads_data'));
            $couse_id = Course::find()->select(['id'])->where(['slug' => $data->gmu_course])->one();
            if ($data->current_location == 'Delhi NCR') {
                $data->current_location = 'Delhi';
            }
            // $interested_location = City::find()->select(['id'])->where(['name' => $data->city])->one();
            $current_location = City::find()->select(['id'])->where(['name' => $data->current_location])->one();
            $lead_data['LeadForm'] = [
                'name' => $data->full_name,
                'email' => $data->email_id,
                'mobile' => $data->mobile_num,
                'entity_id' => '',
                'utm_source' => $data->utm_source ?? '',
                'utm_medium' => '',
                'utm_campaign' => '',
                'url' => $data->url,
                'interested_course' => $couse_id->id ?? '',
                'course_interested' => '',
                'current_location' => $current_location->id ?? '',
                'current_state' => $data->current_location_state_id,
                'interested_location' => '',
                'interested_state' => '',
                'education_budget' => '',
                'scholarship' => '',
                'percentile' => '',
                'scholarship_percentage' => '',
                'specialization_id' => '',
                'stream' => '',
                'qualification' => $data->gmu_qualification,
                'user_ip_address' => '',
                'entity' => ''
            ];
            $lead_data['source'] = $data->click_source ?? '';
            $update_student_info = $this->studentService->updateStudent($lead_data, []);
            return $update_student_info;
        }
    }

    public function actionCollegeLeadCi()
    {
        $key = md5('leads@data123');
        $authorization = Yii::$app->request->headers->get('authorization');
        if ($key == $authorization) {
            $data = json_decode(Yii::$app->request->post('leads_data'));
            $couse_id = Course::find()->select(['id'])->where(['slug' => $data->gmu_course])->one();
            if ($data->current_location == 'Delhi NCR') {
                $data->current_location = 'Delhi';
            }
            // $interested_location = City::find()->select(['id'])->where(['name' => $data->city])->one();
            $current_location = City::find()->select(['id'])->where(['name' => $data->current_location])->one();
            $lead_data['LeadForm'] = [
                'name' => $data->full_name,
                'email' => $data->email_id,
                'mobile' => $data->mobile_num,
                'entity_id' => $data->gmu_clg_id,
                'utm_source' => $data->utm_source,
                'utm_medium' => '',
                'utm_campaign' => '',
                'url' => $data->url,
                'interested_course' => $couse_id->id ?? '',
                'course_interested' => $data->interested_course,
                'current_location' => $current_location->id ?? '',
                'current_state' => $data->current_location_state_id,
                'interested_location' => '',
                'interested_state' => '',
                'education_budget' => '',
                'scholarship' => '',
                'percentile' => '',
                'scholarship_percentage' => '',
                'specialization_id' => '',
                'stream' => '',
                'qualification' => $data->gmu_qualification,
                'user_ip_address' => '',
                'entity' => ''
            ];
            $lead_data['source'] = 'organic';
            $update_student_info = $this->studentService->updateStudent($lead_data, []);
        }
    }

    public function actionLeadLms()
    {
        $key = md5('leads@data123');
        $authorization = Yii::$app->request->headers->get('authorization');
        if ($key == $authorization) {
            $data = json_decode(Yii::$app->request->post('leads_data'));
            $data->mobile = (string)$data->mobile;
            if (isset($data->course) && !empty($data->course)) {
                $couse = Course::find()->select(['id', 'stream_id'])->where(['short_name' => $data->course])->one();
            }
            if (isset($data->current_city) && !empty($data->current_city)) {
                if ($data->current_city == 'Delhi NCR') {
                    $data->current_city = 'Delhi';
                }
                $current_location = City::find()->select(['id'])->where(['name' => $data->current_city])->one();
            }

            $lead_data['LeadForm'] = [
                'name' => $data->name,
                'email' => $data->email,
                'mobile' => $data->mobile,
                'entity_id' => '',
                'utm_source' => $data->utm_source ?? '',
                'utm_medium' => '',
                'utm_campaign' => '',
                'url' => $data->url ?? '',
                'interested_course' => $couse->id ?? '',
                'course_interested' => $data->course_interested ?? '',
                'current_location' => $current_location->id ?? '',
                'current_state' => $data->current_state ?? '',
                'interested_location' => '',
                'interested_state' => '',
                'education_budget' => '',
                'scholarship' => '',
                'percentile' => '',
                'scholarship_percentage' => '',
                'specialization_id' => '',
                'stream' => $couse->stream_id ?? '',
                'qualification' => $data->highest_qualification ?? '',
                'user_ip_address' => '',
                'entity' => ''
            ];
            $lead_data['source'] = 'organic';
            $update_student_info = $this->studentService->updateStudent($lead_data, []);
            return $update_student_info;
        }
    }

    public function actionGetSwipeCourseList($type = '', $term = '', $id = '')
    {
        //1->parent, 2->chil&parent_id=1, 3->ids=1,2,3
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');

        Yii::$app->response->format = Response::FORMAT_JSON;

        $response = [];

        switch ($type) {
            case 1:
                $response = self::getParentCourses($term);
                break;
            case 2:
                $response = self::getChildParentCourses($term, $id);
                break;
            case 3:
                $response = self::getCoursesBasedOnId($term, $id);
                break;
        }

        return Json::encode($response);
    }

    public function getParentCourses($term = '')
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');

        $courses = Course::find()
            ->select(['id', 'short_name'])
            ->where(['like', 'short_name', trim($term)])
            ->andWhere(['parent_id' => null])
            ->andWhere(['status' => Course::STATUS_ACTIVE])
            ->all();

        if (empty($courses)) {
            return [];
        }

        $items = [];
        foreach ($courses as $course) {
            $items[] = [
                'id' => $course['id'],
                'text' => $course['short_name'],
            ];
        }

        return $items;
    }

    public function getChildParentCourses($term = '', $id = '')
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');

        $courses = Course::find()
            ->select(['id', 'short_name'])
            ->where(['like', 'short_name', trim($term)])
            ->andWhere(['parent_id' => $id])
            ->andWhere(['status' => Course::STATUS_ACTIVE])
            ->all();

        if (empty($courses)) {
            return [];
        }

        $items = [];
        foreach ($courses as $course) {
            $items[] = [
                'id' => $course['id'],
                'text' => $course['short_name'],
            ];
        }

        return $items;
    }

    public function getCoursesBasedOnId($term = '', $id = '')
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');

        $explodeIds = explode(',', $id);

        $courses = Course::find()
            ->select(['id', 'short_name'])
            ->where(['like', 'short_name', trim($term)])
            ->andWhere(['in', 'id', $explodeIds])
            ->andWhere(['status' => Course::STATUS_ACTIVE])
            ->all();

        if (empty($courses)) {
            return [];
        }

        $items = [];
        foreach ($courses as $course) {
            $items[] = [
                'id' => $course['id'],
                'text' => $course['short_name'],
            ];
        }

        return $items;
    }

    public function actionGetSwipeCityList($term = '', $id = '')
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');

        Yii::$app->response->format = Response::FORMAT_JSON;

        $explodeIds = !empty($id) ? explode(',', $id) : '';

        $query = City::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', trim($term)])
            ->andWhere(['status' => City::STATUS_ACTIVE]);

        if (!empty($explodeIds)) {
            $query->andWhere(['in', 'id', $explodeIds]);
        }

        $cities = $query->all();

        if (empty($cities)) {
            return [];
        }

        $items = [];
        foreach ($cities as $city) {
            $items[] = [
                'id' => $city['id'],
                'text' => $city['name'],
            ];
        }

        return $items;
    }

    public function actionGetSwipeExamList($term = '', $id = '')
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');

        Yii::$app->response->format = Response::FORMAT_JSON;
        $items = [];

        $explodeIds = !empty($id) ? explode(',', $id) : '';

        $query = Exam::find()
            ->select(['id', 'display_name'])
            ->where(['like', 'display_name', trim($term)])
            ->andWhere(['status' => Exam::STATUS_ACTIVE]);

        if (!empty($explodeIds)) {
            $query->andWhere(['in', 'id', $explodeIds]);
        }
        $exams = $query->all();

        if (empty($exams)) {
            return [];
        }

        foreach ($exams as $exam) {
            $items[] = [
                'id' => $exam['id'],
                'text' => $exam['display_name'],
            ];
        }

        return $items;
    }


    public function actionGetSwipeCollegeList($term = '', $id = '')
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');

        $items = [];
        Yii::$app->response->format = Response::FORMAT_JSON;

        $explodeIds = !empty($id) ? explode(',', $id) : '';

        $query = College::find()
            ->select(['id', 'display_name'])
            ->where(['like', 'display_name', trim($term)])
            ->andWhere(['status' => College::STATUS_ACTIVE]);

        if (!empty($explodeIds)) {
            $query->andWhere(['in', 'id', $explodeIds]);
        }
        $query->limit(200);
        $colleges = $query->all();

        if (empty($colleges)) {
            return [];
        }

        foreach ($colleges as $college) {
            $items[] = [
                'id' => $college['id'],
                'text' => $college['display_name'],
            ];
        }
        return $items;
    }

    public function actionGetSwipeSpecializationList($term = '', $id = '')
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');

        $items = [];
        Yii::$app->response->format = Response::FORMAT_JSON;

        $explodeIds = !empty($id) ? explode(',', $id) : '';

        $query = Specialization::find()
            ->select(['id', 'display_name'])
            ->where(['like', 'display_name', trim($term)])
            ->andWhere(['status' => Specialization::STATUS_ACTIVE]);

        if (!empty($explodeIds)) {
            $query->andWhere(['in', 'id', $explodeIds]);
        }

        $specializations = $query->all();

        if (empty($specializations)) {
            return [];
        }

        foreach ($specializations as $specialization) {
            $items[] = [
                'id' => $specialization['id'],
                'text' => $specialization['display_name'],
            ];
        }
        return $items;
    }

    public function actionSubmitSwipePageLeads()
    {
        $page = 1;
        Yii::$app->response->format = 'json';
        $request = Yii::$app->request->post();
        // self::createFile($request);

        if (empty($request['course']) || (!isset($request['mobile']) || empty($request['mobile']))) {
            throw new \Exception('Course or Mobile is empty');
        }

        $courseData = !empty($request['course']) ? LeadService::getCourseData($request['course']) : [];
        $degreeId = empty($courseData) && empty($courseData['degree']) ? null : Degree::find()->select('id')->where(['like', 'slug', $courseData['degree']])->one();
        $locationData = !empty($request['city']) ? UserService::getCityId('', $request['city']) : null;
        // if (isset(DataHelper::$mapCLPHighestLevel[$courseData['highest_qualification']]) && !empty(DataHelper::$mapCLPHighestLevel[$courseData['highest_qualification']])) {
        //     $level = DataHelper::$mapCLPHighestLevel[$courseData['highest_qualification']];
        // } else {
        //     $level = '';
        // }

        $lead_data['LeadForm'] = [
            'name' => $request['name'] ?? '',
            'email' => $request['email']  ?? '',
            'mobile' => $request['mobile'] ?? '',
            'interested_course' => isset($courseData) && !empty($courseData['parent_id']) ? $courseData['parent_id'] : $courseData['id'],
            'child_course' => isset($request['course']) && !empty($request['course']) ? $request['course'] : null,
            'current_location' => !empty($locationData) && !empty($locationData['cityId']) ? $locationData['cityId'] : null,
            'current_state' => !empty($locationData) && !empty($locationData['stateId']) ? $locationData['stateId'] : null,
            'specialization_id' => !empty($requset['specialization']) && is_numeric($request['specialization']) ? $requset['specialization'] : (isset($courseData) && !empty($courseData['specialization_id']) ? $courseData['specialization_id'] : null),
            'stream' => isset($courseData) && !empty($courseData['stream_id']) ? $courseData['stream_id'] : null,
            'degree' => empty($degreeId) ? null : $degreeId['id'],
            // 'level' => $level,
            // 'level' => empty($degreeId) ? null : $degreeId['id'],
            'highest_qualification' => isset($courseData) && !empty($courseData['highest_qualification']) ? $courseData['highest_qualification'] : null,
            'entity' => null,
            'college' => !empty($request['college']) && is_numeric($request['college']) ? $request['college'] : null,
            'exam' => !empty($request['exam']) && is_numeric($request['exam']) ? $request['exam'] : null,
            'url' => $request['page_url'] ?? '',
            'utm_source' => $request['utm_source'] ?? '',
            'utm_medium' => $request['utm_medium'] ?? '',
            'utm_campaign' => $request['utm_campaign'] ?? '',
        ];

        $lead_data['user_type'] = Student::USER_SWIPE_PAGES;
        $lead_data['lead_entries'] = 'swipe-pages';
        $lead_data['old_page'] = $page;
        $update_student_info = $this->studentService->updateStudent($lead_data, [], 'swipe-pages');

        return ['success' => true, 'result' => $request];
    }

    public function createFile($request)
    {
        $txt = $request;
        $myfile = file_put_contents('/var/www/html/gmu-yii-backend/swipe_pages_log.txt', print_r($txt, true) . PHP_EOL, FILE_APPEND | LOCK_EX);
    }

    public function actionAdwordsLeadPush()
    {

        $key = 'j4jE5V9R5#l2';
        $authorization = Yii::$app->request->headers->get('authorization');
        Yii::$app->response->format = 'json';
        $request = Yii::$app->request->post();
        $page = 2;

        // if ($key === $authorization) {
        if (empty($request) || empty($request['user_column_data'])) {
            throw new \Exception('Request or user_column_data is empty');
        }

        foreach ($request['user_column_data'] as $formData) {
            $items['LeadForm'][$formData['column_id']] = $formData['string_value'];
        }

        $items['utm_source'] = $request['form_id'];
        $items['utm_campaign'] = $request['campaign_id'];
        $items['utm_term'] = $request['gcl_id'];

        $cityName = !empty($items['LeadForm']) && !empty($items['LeadForm']['CITY']) ? self::checkForValidCharacters($items['LeadForm']['CITY']) : null;
        $stateName = !empty($items['LeadForm']) && !empty($items['LeadForm']['REGION']) ? self::checkForValidCharacters($items['LeadForm']['REGION']) : null;
        $citylocationData = !empty($cityName) ? UserService::getCityId($cityName, '') : null;
        $statelocationData = !empty($stateName) ? UserService::getCityId('', '', '', $stateName) : null;
        $state = '';
        $stream = '';
        $level = '';

        if (empty($statelocationData)) {
            $stateId = DsaStateMapping::find()->select(['state_id'])
                ->where(['campaign_id' => $request['campaign_id']])
                ->andWhere(['status' => DsaStateMapping::STATUS_ACTIVE])
                ->one();

            $state = !empty($stateId) && !empty($stateId->state_id) ? $stateId->state_id : null;
        } else {
            $state = !empty($statelocationData) && !empty($statelocationData['id']) ? $statelocationData['id'] : null;
        }

        // $course = !empty($items['LeadForm']) && !empty($items['LeadForm']['which_course_are_you_interested_in?']) && isset(DataHelper::$googleAdsLeadCourseMapping[$items['LeadForm']['which_course_are_you_interested_in?']]) ? DataHelper::$googleAdsLeadCourseMapping[$items['LeadForm']['which_course_are_you_interested_in?']] : '';

        $getCampaignStreamLevel = DsaStateMapping::find()
            ->select(['stream_id', 'degree_id'])
            ->where(['campaign_id' => $request['campaign_id']])
            ->all();

        if (!empty($getCampaignStreamLevel)) {
            $stream = $getCampaignStreamLevel[0]->stream_id;

            // If there is a second entry in the result, determine level based on UG condition
            $level = isset($getCampaignStreamLevel[1])
                ? (!empty($items['LeadForm']['which_course_are_you_interested_in?'])
                    && $items['LeadForm']['which_course_are_you_interested_in?'] === 'UG' ? 1 : 2)
                : $getCampaignStreamLevel[0]->degree_id;
        } else {
            $course = isset($items['LeadForm']['which_course_are_you_interested_in?'], DataHelper::$googleAdsLeadCourseMapping[$items['LeadForm']['which_course_are_you_interested_in?']])
                ? DataHelper::$googleAdsLeadCourseMapping[$items['LeadForm']['which_course_are_you_interested_in?']]
                : '';
        }

        $lead_data['LeadForm'] = [
            'name' => $items['LeadForm']['FULL_NAME'] ?? '',
            'email' => $items['LeadForm']['EMAIL']  ?? '',
            'mobile' => $items['LeadForm']['PHONE_NUMBER'] ?? '',
            'current_location' => !empty($citylocationData) && !empty($citylocationData['cityId']) ? $citylocationData['cityId'] : null,
            'current_state' => !empty($state) ? $state : null,
            // 'stream' => !empty($course) && !empty($course['stream']) ? $course['stream'] : null,
            // 'degree' => !empty($course) && !empty($course['level']) ? $course['level'] : null,
            'stream' => !empty($stream) ? $stream : (!empty($course) && !empty($course['stream']) ? $course['stream'] : null),
            'degree' => !empty($level) ? $level : (!empty($course) && !empty($course['level']) ? $course['level'] : null),
            'entity' => null,
            'url' => $items['utm_campaign'] ?? '',
            'utm_source' => $items['utm_source'] ?? '',
            'utm_medium' => 'DS01',
            'utm_campaign' => $items['utm_campaign'] ?? '',
            'utm_term' => $items['utm_term'] ?? '',
        ];

        $lead_data['user_type'] = Student::USER_GOOGLE_ADS;
        $lead_data['lead_entries'] = 'google-ads-lead';
        $lead_data['old_page'] = $page;

        $update_student_info = $this->studentService->updateStudent($lead_data, [], 'google-ads-lead', $request);

        //send payload to the dsa log table
        if ($update_student_info !== false) {
            $logsData = LeadService::saveDsaWebHookLogs($request, $items['LeadForm']['PHONE_NUMBER'], $lead_data, $update_student_info);
        }

        return ['success' => true, 'result' => $request];
    }
    // }


    public function checkForValidCharacters($request)
    {
        $englishPattern = '/^[A-Za-z0-9\s]*$/';
        if (!preg_match($englishPattern, $request)) {
            return null;
        } else {
            return $request;
        }
    }

    public function actionSaveStudentInfoWhatsapp()
    {
        $text = 'whatsapp';
        Yii::$app->response->format = 'json';
        $request = Yii::$app->request->post();

        if (empty($request) || empty($request['session'])) {
            return ['success' => false, 'message' => 'request or session is empty'];
        }

        foreach ($request['session'] as $formData) {
            $items['LeadForm'][$formData['meta']['type']] = $formData['value'];
        }

        if (empty($items['LeadForm'] || $items['LeadForm']['phone'])) {
            throw new \Exception('Phone or data is empty');
        }

        $mobileNumber = substr($items['LeadForm']['phone'], -10);

        $studentModel = Student::findByPhone($mobileNumber);

        if (empty($studentModel)) {
            $studentNew = new Student;
            $studentNew->phone = $mobileNumber;
            $studentNew->name = 'Whatsapp';
            $studentNew->email = $mobileNumber . '@getmyuni.com';
            $studentNew->user_type = Student::USER_WHATSAPP;
            $studentNew->source = Student::SOURCE_ORGANIC;
            $studentNew->source_url = $text;
            $studentNew->is_mobile_verified = Student::IS_MOBILE_VERIFIED_YES;

            if ($studentNew->save()) {
                $studentActivity = StudentService::createStudentActivity($studentNew, $text);

                return ['success' => true, 'result' => $studentNew];
            } else {
                throw new \Exception('Validation failed: ' . json_encode($studentNew->getErrors()) . ' WhatsApp');
            }
        } else {
            $studentActivity = StudentService::createStudentActivity($studentModel, $text);
            return ['success' => true, 'result' => $studentModel];
        }
    }

    public function actionCheckStudentIdForLms()
    {

        Yii::$app->response->format = 'json';
        $request = Yii::$app->request->post();
        $mobileregex = '/^\d{10}/' ;

        if (!empty($request['mobile_no'])) {
            if (preg_match($mobileregex, $request['mobile_no'])) {
                $checkStudentID = Student::find()->where(['phone'=>$request['mobile_no']])->one();
                if (!empty($checkStudentID)) {
                    return ['success' => true, 'result' => $checkStudentID];
                }
            } else {
                return ['success' => true, 'result' => 'mobile no is not valid'];
            }
        } else {
            return ['success' => true, 'result' => 'mobile no  field is empty'];
        }
    }
}
