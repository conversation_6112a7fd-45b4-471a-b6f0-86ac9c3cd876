<?php

namespace api\modules\v1\controllers;

use api\models\GmuLead;
use common\models\Course;
use common\models\CourseContent;
use common\models\ExamContent;
use common\models\CollegeContent;
use common\models\Faq;
use common\models\CmsLogs;
use common\models\Article;
use common\models\User;
use common\models\Exam;
use common\models\NewsSubdomain;
use Yii;
use common\models\ArticleTranslationCld;
use common\models\ArticleTranslationCldLog;
use common\helpers\DataHelper;
use yii\helpers\Json;
use yii\web\Response;
use yii\helpers\Inflector;
use common\services\EmailService;
use common\models\BoardContent;
use yii\helpers\Url;
use yii\web\HttpException;
use yii\web\ConflictHttpException;
use yii\web\MethodNotAllowedHttpException;
use yii\web\ForbiddenHttpException;
use yii\filters\auth\HttpBasicAuth;

/**
 * Collegedekho API Controller
 *
 * <AUTHOR> <PERSON> <<EMAIL>>
 */

class CollegedekhoController extends ActiveController
{

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['authenticator'] = [
            'class' => HttpBasicAuth::className(),
            'except' => ['content-api-payload','article-translation-cld'],
        ];

        return $behaviors;
    }
    public $modelClass = GmuLead::class;

    public static $_templateID = [
        '1' => 'exam',
        '2' => 'course',
        '3' => 'article',
        '4' => 'news',
        '5' => 'board',
        '6' => 'college',
        '7' => 'career',
        '8' => 'scholarship'
    ];
   
    public static $_cmsUrl = 'https://backend.getmyuni.com/';
    public static $_cmsUrlPreview = 'https://backend.getmyuni.com/article/preview?id=';
    public static $_frotenddUrl = '';
    public static $_apiKey = '815fa7bff0848b6d2453997aec3d4fdc';
    public static $_cldUrl = 'contentlikho.s3.amazonaws.com';
    public static $_getMyuniUrl = 'contentlikho-media.getmyuni.com';
    
    


    public function actions()
    {
        $actions = parent::actions();
        $params = Yii::$app->request->getBodyParams();
        $params['vendor_id'] = 5;
        $params['click_source'] = 'college-dekho';
        Yii::$app->request->setBodyParams($params);

        // disable the "delete", "list" and "update" actions
        unset($actions['delete'], $actions['index'], $actions['update']);
        return $actions;
    }

    public function actionArticleTranslationCld()
    {
        $response = Yii::$app->request->post();
        $responseLog =  new ArticleTranslationCldLog();
        $responseLog->type = 'translation';
        $responseLog->response = json_encode($response);
        $responseLog->created_at =  date('Y-m-d H:i:s');
        $responseLog->updated_at = date('Y-m-d H:i:s');
        $responseLog->save();
        $translationData = json_decode(json_encode($response));
        
        foreach ($translationData->payload as $key => $data) {
            $transactionData = ArticleTranslationCld::find()
            ->where(['transaction_id' => $translationData->transaction_id])
            ->andWhere(['lang_code' => $translationData->payload->target_lang])
            ->one();
            if ($key == 'target_lang') {
                continue;
            }
            if ($translationData->transaction_status_code != 200) {
                $articleData = Article::find()->where(['id' => $transactionData->translation_article_id])
                ->one();
                $sendEmail = User::find()->select(['email','name'])->where(['id'=>$transactionData->translate_author_id])->one() ;
                $this->sendEmailForArticleTranslation('@backend/views/mail/article-translation/article-translation-failed', $sendEmail->email, $sendEmail->name, $articleData);

                continue;
            }
            
            if (!empty($transactionData)) {
                if (!empty($transactionData->translation_article_id)) {
                    $articleData = Article::find()->where(['id' => $transactionData->translation_article_id])->one();
                    
                    $faqData = Faq::find()->where(['entity_id' => $transactionData->translation_article_id])
                                ->andWhere(['entity'=>'articles'])
                                ->one();
                                
                    if (!empty($data)) {
                        $labelData = array_column($data, 'label');
                        $labelData = array_diff($labelData, ['title','description','meta_description','meta_title','h1']);
                        foreach ($data as $tranlation) {
                            if ($tranlation->label == 'title') {
                                $articleData->title =   $tranlation->output;
                            }
                            if ($tranlation->label == 'h1') {
                                $articleData->h1 =   $tranlation->output;
                            }
                            if ($tranlation->label == 'description') {
                                $articleData->description =   $tranlation->output;
                            }
                            if ($tranlation->label == 'meta_description') {
                                $articleData->meta_description =   $tranlation->output;
                            }
                            if ($tranlation->label == 'meta_title') {
                                $articleData->meta_title =   $tranlation->output;
                                break;
                            }
                        }
                        
                        $faqTranslateData = [];
                        if (!empty($labelData)) {
                            foreach ($labelData as $key => $dataLabel) {
                                $i=1;
                                foreach ($data as $keys => $tranlationFaq) {
                                    if ($key%2!=0 && $i>4) {
                                        if ($tranlationFaq->label == $dataLabel) {
                                            $faqTranslateData[] = [ 'question'=>$tranlationFaq->output,'answer'=>$data[$key+1]->output] ;
                                        }
                                    }
                                    $i++;
                                }
                            }
                        }
                        if (!empty($faqTranslateData)) {
                            if (!empty($faqData)) {
                                \Yii::$app->db->createCommand('UPDATE faq SET qnas=:qnas WHERE id=:id')
                                ->bindValue(':id', $faqData->id)
                                ->bindValue(':qnas', json_encode($faqTranslateData, JSON_UNESCAPED_UNICODE))
                                ->execute();
                            } else {
                                \Yii::$app->db->createCommand()->insert('faq', [
                                    'entity_id' =>$transactionData->translation_article_id,
                                    'entity' => 'articles',
                                    'page' => '',
                                    'status' => Faq::STATUS_INACTIVE,
                                    'qnas' => json_encode($faqTranslateData, JSON_UNESCAPED_UNICODE),
                                ])->execute();
                            }
                        }
                    }
                     \Yii::$app->db->createCommand('UPDATE article SET title=:title,h1=:h1,description=:description,meta_description=:meta_description,meta_title=:meta_title,author_id=:author_id WHERE id=:id')
                     ->bindValue(':id', $articleData->id)
                     ->bindValue(':title', $articleData->title)
                     ->bindValue(':h1', $articleData->h1)
                     ->bindValue(':meta_description', $articleData->meta_description)
                     ->bindValue(':meta_title', $articleData->meta_title)
                     ->bindValue(':description', $articleData->description)
                     ->bindValue(':author_id', $transactionData->translate_author_id)
                     //->bindValue(':translation_status', 1)
                     ->execute();
                     $transactionData->translation_status  = 1;
                     $transactionData->update();
                     $articleData->author_id = $transactionData->translate_author_id;
                     $sendEmail = User::find()->select(['email','name'])->where(['id'=>$transactionData->translate_author_id])->one() ;
                     $this->sendEmailForArticleTranslation('@backend/views/mail/article-translation/article-translation-success', $sendEmail->email, $sendEmail->name, $articleData);
                } else {
                    $articleData = Article::find()->where(['id' => $transactionData->article_id])
                        ->one();
                    $tranlationArticle = new Article();
                    $slugTitle = '';
                    if (!empty($data)) {
                        $labelData = array_column($data, 'label');
                        $labelData = array_diff($labelData, ['title','description','meta_description','meta_title','h1']);
                        foreach ($data as $tranlation) {
                            if ($tranlation->label == 'title') {
                                $tranlationArticle->title =   $tranlation->output;
                                $slugTitle = $tranlation->output;
                            }
                            if ($tranlation->label == 'h1') {
                                $tranlationArticle->h1 =   $tranlation->output;
                            }
                            if ($tranlation->label == 'description') {
                                $tranlationArticle->description =   $tranlation->output;
                            }
                            if ($tranlation->label == 'meta_description') {
                                $tranlationArticle->meta_description =   $tranlation->output;
                            }
                            if ($tranlation->label == 'meta_title') {
                                $tranlationArticle->meta_title =   $tranlation->output;
                            }
                        }
                    }
                    $tranlationArticle->entity = $articleData->entity;
                    $tranlationArticle->author_id =  $transactionData->translate_author_id;
                    $tranlationArticle->category_id = $articleData->category_id;
                    $tranlationArticle->stream_id = $articleData->stream_id ?? 1;
                    $tranlationArticle->country_slug = $articleData->country_slug;
                    $tranlationArticle->highest_qualification = $articleData->highest_qualification ?? 0;
                    $tranlationArticle->cover_image = $articleData->cover_image;
                    $tranlationArticle->audio = $articleData->audio;
                    $tranlationArticle->view_count = 0;
                    $tranlationArticle->lang_code = DataHelper::$languageCode[$translationData->payload->target_lang];
                    $tranlationArticle->scenario = Article::SCENARIO_IMPORTER;
                    $tranlationArticle->slug = $articleData->slug;
                   
                    if ($tranlationArticle->save()) {
                        $faqTranslateData = [];
                        if (!empty($labelData)) {
                            foreach ($labelData as $key => $dataLabel) {
                                $i=1;
                                foreach ($data as $keys => $tranlationFaq) {
                                    if ($key%2!=0 && $i>4) {
                                        if ($tranlationFaq->label == $dataLabel) {
                                            $faqTranslateData[] = [ 'question'=>$tranlationFaq->output,'answer'=>$data[$key+1]->output] ;
                                        }
                                    }
                                    $i++;
                                }
                            }
                        }
                        if (!empty($faqTranslateData)) {
                                \Yii::$app->db->createCommand()->insert('faq', [
                                    'entity_id' =>$tranlationArticle->id,
                                    'entity' => 'articles',
                                    'page' => '',
                                    'status' => Faq::STATUS_INACTIVE,
                                    'qnas' => json_encode($faqTranslateData, JSON_UNESCAPED_UNICODE),
                                ])->execute();
                        }
                        $transactionData->translation_article_id =  $tranlationArticle->id;
                        $transactionData->translation_status  = 1;
                        $transactionData->update();
                        $translationIds[]=  $tranlationArticle->id;
                        \Yii::$app->db->createCommand()->insert('article_translation', [
                            'article_id' =>$articleData->id,
                            'translation_tag_id' =>$tranlationArticle->id,
                        ])->execute();
                        $sendEmail = User::find()->select(['email','name'])->where(['id'=>$transactionData->translate_author_id])->one() ;
                        $this->sendEmailForArticleTranslation('@backend/views/mail/article-translation/article-translation-success', $sendEmail->email, $sendEmail->name, $tranlationArticle);
                    } else {
                        var_dump($tranlationArticle->getErrors());
                        exit();
                    }
                }
            }
        }
    }
  

    public function sendEmailForArticleTranslation($template, $email, $name, $data)
    {
        if (!empty($data)) {
            $emailService = new EmailService();
            //if (!empty($email)) {
                $emails[0] = '<EMAIL>';
                $emails[1] = $email;
                $emailService->sendEmail($emails, 'Article Translation Status', ['data'=>$data], $template, $name);
            //}
        }
    }
    /* Content API*/

    public function actionContentApiPayload()
    {
       
        Yii::$app->response->format = Response::FORMAT_JSON;
        if (Yii::$app->request->isPost) {
            $headAuth = Yii::$app->request->headers->get('x-api-key') ?? '';
            if ($headAuth!= self::$_apiKey) {
                throw new HttpException(401, 'Unauthorized');
            }
            $request = Yii::$app->request->post();
            if (!isset($request['id']) || empty($request['id'])) {
                throw new HttpException(400, 'please provide task id');
            }
            $logId =  $request['id'];
            $logData = new CmsLogs();
            $logData->log_id = $logId;
            $logData->payload = serialize($request);
            $logData->save();
                
            $templateID = $request['task_details']['template_id'];
            $data = [];
           
            switch ($templateID) {
                case 3:
                    $data = $this->saveArticleContent($request);
                    break;
                case 5:
                    $data = $this->saveBoardContent($request);
                    break;
                case 1:
                    $data = $this->saveExamContent($request);
                    break;
                case 2:
                    $this->defaultWorkingTemplateMessage();
                    break;
                case 4:
                    $data =  $this->saveNewsContent($request);
                    break;
                case 6:
                    $this->defaultWorkingTemplateMessage();
                    break;
                case 7:
                    $this->defaultWorkingTemplateMessage();
                    break;
                case 8:
                    $this->defaultWorkingTemplateMessage();
                    break;
                default:
                    $this->defaultMessage();
            }
           
            return $data;
        } else {
            throw new MethodNotAllowedHttpException();
        }
    }

    public function saveArticleContent($payloadData)
    {
       //echo  str_replace(self::$_cldUrl,self::$_getMyuniUrl,$payloadData['content']); die;
        $objectID = $payloadData['task_details']['object_id'] ?? '';
        $slug = $payloadData['task_details']['slug'] ?? '';
        if ($slug && $objectID) {
            $articleExits = Article::find()->where(['id' => $objectID])->andWhere(['slug' => $slug])->count();
            if ($articleExits) {
                $command = \Yii::$app->db->createCommand('UPDATE article SET description=:description WHERE id=:id')
                    ->bindValue(':id', $objectID)
                    ->bindValue(':description', str_replace(self::$_cldUrl, self::$_getMyuniUrl, $payloadData['content']))->execute();

                if (!empty($payloadData['faqs'])) {
                    $faqSave = $this->saveFaq($payloadData, 'articles', $objectID, null, null);
                    if (!empty($faqSave)) {
                        return [
                            'cms_url' => self::$_cmsUrl . 'article/update?id=' . $objectID,
                            'preview_url' => self::$_cmsUrlPreview . $objectID,
                            //'frontend_url' => self::$_frotenddUrl . 'articles/' . $payloadData['task_details']['slug'],
                            //'faq' => $faqSave['message']
                        ];
                    }
                }
                return [
                    'cms_url' => self::$_cmsUrl . 'article/update?id=' . $objectID,
                    'preview_url' => self::$_cmsUrlPreview . $objectID,
                   // 'frontend_url' => self::$_frotenddUrl . 'articles/' . $payloadData['task_details']['slug'],
                ];
            } else {
                throw new HttpException(400, 'Article not found with this object id');
            }
        } else {
           // throw new HttpException(400, 'Article Id and Article URL are mandatory');
            $lastID = 0;
            $command = \Yii::$app->db->createCommand();
            $slug = $payloadData['task_details']['slug'];
            if (empty($slug)) {
                $slug = Inflector::slug($payloadData['task_details']['object_name'], '-');
            }
            $article = Article::find()->where(['slug' => $slug])->exists();
            
            if ($article) {
                throw new ConflictHttpException('Slug already exists.');
            }
            
            $command->insert('article', [
                'entity' => Article::ENTITY_ARTICLE,
                'author_id' => 1770, //this is not null
                //'category_id' => null, //this is not null
                'title' => $payloadData['task_details']['object_name'],
                'display_name' => $payloadData['task_details']['object_name'],
                'description' =>  str_replace(self::$_cldUrl, self::$_getMyuniUrl, $payloadData['content']),
                'is_freelancer' => Article::IS_FREELANCER_YES,
                'slug' => $slug,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ])->execute();
            $lastID = \Yii::$app->db->getLastInsertID();
            if ($lastID) {
                if (!empty($payloadData['faqs'])) {
                    $faqSave = $this->saveFaq($payloadData, 'articles', $lastID, null, null);
                    if (!empty($faqSave)) {
                        return [
                            'cms_url' => self::$_cmsUrl . 'article/update?id=' . $lastID,
                            'preview_url' => self::$_cmsUrlPreview . $lastID,
                        ];
                    }
                }
                return [
                    'cms_url' => self::$_cmsUrl . 'article/update?id=' . $lastID,
                    'preview_url' => self::$_cmsUrlPreview . $lastID,
                ];
            } else {
                throw new HttpException(500, 'Error in saving Article');
            }
        }
    }

    protected function saveFaq($payloadData, $entity, $entity_id, $page, $pageSlug)
    {
       
        $query =  Faq::find()->select(['qnas', 'entity_id','id'])
                ->where(['entity_id' => $entity_id])
                ->andWhere(['entity' => $entity]);
        if ($entity=='board' || $entity=='exam') {
            $query->andWhere(['sub_page'=>$pageSlug])->andWhere(['page'=>$page]);
        }
        $faq =  $query->one();
        $faqData = [];
         $i=0;
        foreach ($payloadData['faqs'] as $key => $faqValue) {
            $faqData[$i]['question'] = strip_tags($faqValue['question']);
            $faqData[$i]['answer'] = strip_tags($faqValue['answer']);
            $i++;
        }
          
        if (!empty($faq)) {
            if (!empty($faq->qnas[0])) {
                  $faqQnaArr = $faq->qnas;
                foreach ($faqData as $faqs) {
                    array_push($faqQnaArr, $faqs);
                }
            } else {
                $faqQnaArr = $faqData;
            }
                
                $command = \Yii::$app->db->createCommand('UPDATE faq SET qnas=:qnas  WHERE id=:id')
             ->bindValue(':id', $faq->id)
             ->bindValue(':qnas', json_encode($faqQnaArr))
             ->execute();
        } else {
            $datasave =  [
                'entity' => $entity,
                'entity_id' => $entity_id,
                'qnas' => json_encode($faqData), //this is not null
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            if ($entity=='board' || $entity=='exam') {
                $datasave['page']=$page;
                $datasave['sub_page']=$pageSlug;
            }
            $command = \Yii::$app->db->createCommand();
            $command->insert('faq', $datasave)->execute();
        }
        
        

        return true;
    }

    public function saveBoardContent($payloadData)
    {
      
        $objectID = $payloadData['task_details']['object_id'] ?? '';
        $sub_page_id = $payloadData['task_details']['sub_page_id'] ?? '';
        $child_sub_page_id = $payloadData['task_details']['child_page_id'] ?? '';

        if (empty($objectID)) {
            throw new HttpException(400, 'Please provide board Id');
        }

        if (empty($sub_page_id)) {
            throw new HttpException(400, 'Please provide board sub page Id');
        }

        if (!empty($child_sub_page_id)) {
            $sub_page_id = $child_sub_page_id;
        }

        if ($objectID) {
            $boardContentExits = BoardContent::find()->where(['id' => $sub_page_id])->count();
            $boardContentData = BoardContent::find()->select(['page','page_slug'])->where(['id' => $sub_page_id])->one();
            if ($boardContentExits) {
                $command = \Yii::$app->db->createCommand('UPDATE board_content SET content=:content,is_freelancer=:is_freelancer WHERE id=:id')
                    ->bindValue(':id', $sub_page_id)
                    ->bindValue(':is_freelancer', BoardContent::IS_FREELANCER_YES)
                    ->bindValue(':content', str_replace(self::$_cldUrl, self::$_getMyuniUrl, $payloadData['content']))->execute();
                if (!empty($payloadData['faqs'])) {
                    $faqSave = $this->saveFaq($payloadData, 'board', $objectID, $boardContentData->page, $boardContentData->page_slug);
                    if (!empty($faqSave)) {
                        return [
                            'cms_url' => self::$_cmsUrl . 'board-content/update?id=' . $sub_page_id,
                            'preview_url' => self::$_cmsUrl . 'board-content/preview?id=' . $sub_page_id,
                         
                        ];
                    }
                }
                return [
                    'cms_url' => self::$_cmsUrl . 'board-content/update?id=' . $sub_page_id,
                    'preview_url' => self::$_cmsUrl . 'board-content/preview?id=' . $sub_page_id,
                ];
            } else {
                throw new HttpException(400, 'Board Content not found with this object id');
            }
        } else {
            throw new HttpException(400, 'Board Content  Id and Board URL are mandatory');
        }
    }
     

    public function saveExamContent($payloadData)
    {

        $objectID = $payloadData['task_details']['object_id'] ?? '';
        $sub_page_id = $payloadData['task_details']['sub_page_id'] ?? '';
        $child_sub_page_id = $payloadData['task_details']['child_page_id'] ?? '';

        if (empty($objectID)) {
            throw new HttpException(400, 'Please provide Exam Id');
        }

        if (empty($sub_page_id)) {
            throw new HttpException(400, 'Please provide Exam sub page Id');
        }

        if (!empty($child_sub_page_id)) {
            $sub_page_id = $child_sub_page_id;
        }

        if ($objectID) {
            $examontentExits = ExamContent::find()->where(['id' => $sub_page_id])->count();
            $examContentData = ExamContent::find()->select(['name','slug','exam_id'])->where(['id' => $sub_page_id])->one();
            $examData = Exam::find()->select(['slug'])->where(['id' => $examContentData->exam_id])->one();
            if ($examontentExits) {
                $command = \Yii::$app->db->createCommand('UPDATE exam_content SET content=:content,is_freelancer=:is_freelancer,published_at=:published_at,localize_year=:localize_year,status=:status WHERE id=:id')
                    ->bindValue(':id', $sub_page_id)
                    ->bindValue(':is_freelancer', ExamContent::IS_FREELANCER_YES)
                    ->bindValue(':content', str_replace(self::$_cldUrl, self::$_getMyuniUrl, $payloadData['content']))
                    ->bindValue(':localize_year', date('Y'))
                    ->bindValue(':status', ExamContent::STATUS_DRAFT)
                    ->bindValue(':published_at', null)->execute();
                if (!empty($payloadData['faqs'])) {
                    $faqSave = $this->saveFaq($payloadData, 'exam', $objectID, $examData->slug, $examContentData->slug);
                    if (!empty($faqSave)) {
                        return [
                            'cms_url' => self::$_cmsUrl . 'exam-content/update?id=' . $sub_page_id,
                            'preview_url' => self::$_cmsUrl . 'exam-content/preview?id=' . $sub_page_id,
                         
                        ];
                    }
                }
               
                return [
                    'cms_url' => self::$_cmsUrl . 'exam-content/update?id=' . $sub_page_id,
                    'preview_url' => self::$_cmsUrl . 'exam-content/preview?id=' . $sub_page_id,
                ];
            } else {
                throw new HttpException(400, 'Exam Content not found with this object id');
            }
        } else {
            throw new HttpException(400, 'Exam Content  Id and Exam URL are mandatory');
        }
    }

    
    // Check User Authentication
    protected function checkAuth(string $authorization): bool
    {
        
        return password_verify($authorization, '815fa7bff0848b6d2453997aec3d4fdc ') ? true : false;
    }

    public function defaultMessage()
    {
        throw new HttpException(400, 'please provide vaild template id');
    }

    public function defaultWorkingTemplateMessage()
    {
        throw new HttpException(400, 'Working on this template');
    }

    public function saveNewsContent($payloadData)
    {
        $objectID = $payloadData['task_details']['object_id'] ?? '';
        $slug = $payloadData['task_details']['slug'] ?? '';
        if ($slug && $objectID) {
            $newsxits = NewsSubdomain::find()->where(['id' => $objectID])->andWhere(['slug' => $slug])->count();
            if ($newsxits) {
                $command = \Yii::$app->db->createCommand('UPDATE news_content_subdomain SET description=:description WHERE news_id=:news_id')
                    ->bindValue(':news_id', $objectID)
                    ->bindValue(':description', str_replace(self::$_cldUrl, self::$_getMyuniUrl, $payloadData['content']))->execute();
                return [
                    'cms_url' => self::$_cmsUrl . 'news_subdomain/update?id=' . $objectID,
                    'preview_url' => self::$_cmsUrlPreview . $objectID,
                   // 'frontend_url' => self::$_frotenddUrl . 'articles/' . $payloadData['task_details']['slug'],
                ];
            } else {
                throw new HttpException(400, 'News not found with this object id');
            }
        } else {
           // throw new HttpException(400, 'News Id and News URL are mandatory');
            $lastID = 0;
            $command = \Yii::$app->db->createCommand();
            $slug = $payloadData['task_details']['slug'];
            if (empty($slug)) {
                $slug = Inflector::slug($payloadData['task_details']['object_name'], '-');
            }
            $news = NewsSubdomain::find()->where(['slug' => $slug])->exists();
            
            if ($news) {
                throw new ConflictHttpException('Slug already exists.');
            }
            
            $command->insert('news_subdomain', [
               // 'entity' => NewsSubdomain::ENTITY_NEWS,
              //  'author_id' => 1, //this is not null
                //'category_id' => null, //this is not null
                'name' => $payloadData['task_details']['object_name'],
                'display_name' => $payloadData['task_details']['object_name'],
               // 'description' =>  str_replace(self::$_cldUrl, self::$_getMyuniUrl, $payloadData['content']),
                'is_freelancer' => NewsSubdomain::IS_FREELANCER_YES,
                'slug' => $slug,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ])->execute();
            $lastID = \Yii::$app->db->getLastInsertID();
            $command->insert('news_content_subdomain', [
                'author_id' => 1, //this is not null
                'news_id' =>  $lastID, //this is not null
                'content' =>  str_replace(self::$_cldUrl, self::$_getMyuniUrl, $payloadData['content']),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ])->execute();
            if ($lastID) {
                return [
                    'cms_url' => self::$_cmsUrl . 'news_subdomain/update?id=' . $lastID,
                    'preview_url' => self::$_cmsUrlPreview . $lastID,
                ];
            } else {
                throw new HttpException(500, 'Error in saving news');
            }
        }
    }
}
