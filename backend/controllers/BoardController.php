<?php

namespace backend\controllers;

use Yii;
use common\models\Board;
use backend\models\BoardSearch;
use common\models\Article;
use common\models\Exam;
use common\models\News;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\web\Response;
use yii\web\UploadedFile;
use common\services\S3Service;
use common\helpers\DataHelper;
use common\models\BoardContent;
use common\models\ManageNavMenuOrder;

/**
 * BoardController implements the CRUD actions for Board model.
 */
class BoardController extends Controller
{
    /**
     * @inheritdoc
     */
    // public function behaviors()
    // {
    //     return [
    //         'verbs' => [
    //             'class' => VerbFilter::className(),
    //             'actions' => [
    //                 'delete' => ['POST'],
    //             ],
    //         ],
    //     ];
    // }

    /**
     * Lists all Board models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new BoardSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Board model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Board model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $examIds = [];
        $newsIds = [];
        $articleIds = [];
        $translationIds = [];

        $model = new Board();

        $exams = Exam::find()->asArray()->all();

        $postRequest = Yii::$app->request->post();

        if (!empty($postRequest['Board']['exams'])) {
            $examIds = $postRequest['Board']['exams'];
        }
        if (!empty($postRequest['Board']['news'])) {
            $newsIds = $postRequest['Board']['news'];
        }
        if (!empty($postRequest['Board']['article'])) {
            $articleIds = $postRequest['Board']['article'];
        }
        if (!empty($postRequest['Board']['translation'])) {
            $translationIds = $postRequest['Board']['translation'];
            $get_slug = Board::find()->select(['slug'])->where(['id' => $translationIds])->one();
            if ($model->lang_code != DataHelper::$languageCode['en']) {
                $model->slug = $get_slug->slug;
            }
        }

        if ($model->load($postRequest)) {
            $model->lang_code = empty($model->lang_code) ? DataHelper::$languageCode['en'] : $model->lang_code;

            if ($model->lang_code != DataHelper::$languageCode['en'] && empty($postRequest['Board']['translation'])) {
                Yii::$app->session->setFlash('error', 'Board tagging is mendatory for Languages other than english');
                return $this->render('/board/create', ['model' => $model, 'exams' => $exams ?? []]);
            }

            $check_slug_lang = Board::find()->select(['id'])->where(['slug' => $model->slug])->andWhere(['lang_code' => $model->lang_code])->one();
            if ($check_slug_lang) {
                Yii::$app->session->setFlash('error', 'Slug and language combination are exist');
                return $this->render('/board/create', ['model' => $model, 'exams' => $exams ?? []]);
            }

            if (isset($model->logo)) {
                $logo = UploadedFile::getInstance($model, 'logo');
                if ($logo) {
                    $logoName = $model->slug . '.' . $logo->extension;
                    // $saveLogo = $logo->saveAs(\Yii::getAlias('@boardLogoUploadPath') . '/' . $logoName);
                    $saveLogo =  (new S3Service())->uploadFile(DataHelper::s3Path($logoName, 'board_genral'), $logo->tempName);
                    if ($saveLogo) {
                        $model->logo = $logoName;
                    }
                }
            }

            $model->dates = json_decode($model->dates);

            if ($model->save()) {
                if (!empty($examIds)) {
                    $model->saveExams($examIds);
                }
                $model->saveArticle($articleIds);
                $model->saveNews($newsIds);
                $model->saveTranslation($translationIds);

                return $this->redirect(['view', 'id' => $model->id]);
            }
        } else {
            return $this->render('create', [
                'model' => $model,
                'exams' => $exams ?? [],
            ]);
        }
    }

    /**
     * Updates an existing Board model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $examIds = [];
        $newsIds = [];
        $articleIds = [];
        $translationIds = [];

        $model = $this->findModel($id);
        $oldImage = $model->logo;

        $postRequest = Yii::$app->request->post();

        if (!empty($postRequest['Board']['exams'])) {
            $examIds = $postRequest['Board']['exams'];
        }
        if (!empty($postRequest['Board']['news'])) {
            $newsIds = $postRequest['Board']['news'];
        }
        if (!empty($postRequest['Board']['article'])) {
            $articleIds = $postRequest['Board']['article'];
        }
        if (!empty($postRequest['Board']['translation'])) {
            $translationIds = $postRequest['Board']['translation'];
        }

        if ($model->load($postRequest)) {
            if (empty($model->logo)) {
                unset($oldImage);
            }
            $logo = UploadedFile::getInstance($model, 'logo');
            if ($logo) {
                $logoName = $model->slug . '.' . $logo->extension;
                if (!empty($oldImage) && file_exists(Yii::getAlias('@boardLogoUploadPath') . '/' . $oldImage)) {
                    unlink(Yii::getAlias('@boardLogoUploadPath') . '/' . $oldImage);
                }
                // $saveLogo = $logo->saveAs(\Yii::getAlias('@boardLogoUploadPath') . '/' . $logoName);
                $saveLogo =  (new S3Service())->uploadFile(DataHelper::s3Path($logoName, 'board_genral'), $logo->tempName);

                if ($saveLogo) {
                    $model->logo = $logoName;
                }
            }

            if ($model->save()) {
                $model->dates = json_decode($model->dates);
                if (!empty($examIds)) {
                    $model->saveExams($examIds);
                }
                $model->saveArticle($articleIds);
                $model->saveNews($newsIds);
                $model->saveTranslation($translationIds);

                return $this->redirect(['view', 'id' => $model->id]);
            }
        } else {
            return $this->render('update', [
                'model' => $model,
                'exams' => $model->getExams()->asArray()->all() ?? [],
            ]);
        }
    }

    /**
     * Deletes an existing Board model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    /* public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }*/

    /**
     * Finds the Board model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Board the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Board::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    /*Manage Menu Order*/
    public function actionManageMenuOrder()
    {
        $boardData = Yii::$app->request->get();
        $existingMenuOrder = ManageNavMenuOrder::find()
            ->select(['menu_order'])
            ->where(['entity' => DataHelper::$manageMenuOrder['board_content']])
            ->andWhere(['entity_id' => $boardData['boardId']])
            ->one();

        $data = [];
        $orderedIds = [];

        if (!empty($existingMenuOrder) && !empty(unserialize($existingMenuOrder->menu_order))) {
            $menuOrder = unserialize($existingMenuOrder->menu_order);
            foreach ($menuOrder as $val) {
                $contentId = explode('--', $val);
                $orderedIds[] = (int)$contentId[1];
            }

            // 1. Get content in saved order
            foreach ($orderedIds as $contentId) {
                $contentData = BoardContent::find()
                    ->select(['page_slug', 'page', 'id', 'board_id'])
                    ->where([
                        'board_id' => $boardData['boardId'],
                        'parent_id' => null,
                        'id' => $contentId,
                        'status' => BoardContent::STATUS_ACTIVE
                    ])
                    ->asArray()
                    ->one();

                if ($contentData) {
                    $data[] = $contentData;
                }
            }
        }

        // 2. Get any NEW content not in saved menu
        $newContents = BoardContent::find()
            ->select(['page_slug', 'page', 'id', 'board_id'])
            ->where(['board_id' => $boardData['boardId']])
            ->andWhere(['parent_id' => null])
            ->andWhere(['status' => BoardContent::STATUS_ACTIVE])
            ->andFilterWhere(['not in', 'id', $orderedIds]) // exclude already added
            ->asArray()
            ->all();

        // 3. Append new content at the end
        $data = array_merge($data, $newContents);

        return $this->render('manage-menu-order', [
            'data' => $data
        ]);
    }
}
