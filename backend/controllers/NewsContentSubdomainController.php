<?php

namespace backend\controllers;

use backend\models\NewsContentSubdomainSearch;
use Carbon\Carbon;
use Yii;
use common\models\NewsContentSubdomain;
use common\models\NewsSubdomain;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\helpers\DataHelper;

/**
 * NewsContentController implements the CRUD actions for NewsContent model.
 */
class NewsContentSubdomainController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all NewsContent models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new NewsContentSubdomainSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single NewsContent model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new NewsContent model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new NewsContentSubdomain();
        $news = NewsSubdomain::findOne(['id' => Yii::$app->request->get('news_id')]);
        $model->news_id = !empty($news) ? $news->id : '';

        $data = Yii::$app->request->post();
        /**** Check for cache and restricted urls in content ****/
        if (!empty($data['NewsContentSubdomain']['content'])) {
            $restrictedUrl = DataHelper::checkRestrictedUrl($data['NewsContentSubdomain']['content']);
        }
        if (!empty($restrictedUrl)) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        /*********************************************************/

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
                'news' => $news,
            ]);
        }
    }

    /**
     * Updates an existing NewsContent model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        $data = Yii::$app->request->post();
        /**** Check for cache and restricted urls in content ****/
        if (!empty($data['NewsContentSubdomain']['content'])) {
            $restrictedUrl = DataHelper::checkRestrictedUrl($data['NewsContentSubdomain']['content']);
        }
        if (!empty($restrictedUrl)) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        /*********************************************************/

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing NewsContent model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    /*public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }*/

    /**
     * Finds the NewsContent model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return NewsContent the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = NewsContentSubdomain::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
