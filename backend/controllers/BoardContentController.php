<?php

namespace backend\controllers;

use Yii;
use common\models\BoardContent;
use common\models\Board;
use backend\models\BoardContentSearch;
use yii\web\Controller;
use common\services\AzureService;
use common\services\S3Service;
use common\helpers\DataHelper;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;

/**
 * BoardContentController implements the CRUD actions for BoardContent model.
 */
class BoardContentController extends Controller
{

    protected $azureService;

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function __construct($id, $module, AzureService $azureService, $config = [])
    {
        $this->azureService = $azureService;
        parent::__construct($id, $module, $config);
    }

    /**
     * Lists all BoardContent models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new BoardContentSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single BoardContent model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new BoardContent model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new BoardContent();

        if ($model->load(Yii::$app->request->post())) {
            $data = Yii::$app->request->post();

            /**** Check for cache and restricted urls in content ****/
            if (!empty($data['BoardContent']['content'])) {
                $restrictedUrl = DataHelper::checkRestrictedUrl($data['BoardContent']['content']);
            }

            if (!empty($restrictedUrl)) {
                return $this->redirect(Yii::$app->request->referrer);
            }
            /*********************************************************/

            if (!empty($data['BoardContent']['drop_down_subject']) || !empty($data['BoardContent']['drop_down_supplementary'])) {
                $subPage = (empty($data['BoardContent']['drop_down_subject'])) ? $data['BoardContent']['drop_down_supplementary'] : $data['BoardContent']['drop_down_subject'];
                $existingContent = $this->checkExistingContent($data);
                $existingSubPageContent = $this->checkExistingSubPageContent($data, $subPage, $existingContent->id);
                if (!empty($existingSubPageContent)) {
                    Yii::$app->session->setFlash('error', 'Board and Sub Page combination already exist');
                    return $this->redirect('/board-content/index');
                }
                if ($existingContent->id) {
                    $model->parent_id = $existingContent->id;
                    $model->page = ucfirst($subPage);
                } else {
                    Yii::$app->session->setFlash('error', 'Board and Page combination does not exist');
                    return $this->redirect('/board-content/index');
                }
            } else {
                $checkISExist = BoardContent::find()->where(['board_id' => $data['BoardContent']['board_id']])
                    ->andWhere(['page' => $data['BoardContent']['page']])->andWhere(['parent_id' => null])
                    ->count();
                if ($checkISExist != 0) {
                    Yii::$app->session->setFlash('error', 'Board and Page combination already exists');
                    return $this->redirect('/board-content/index');
                }
                $model->parent_id = null;
            }
            if (isset($model->cover_image)) {
                $coverImage = UploadedFile::getInstance($model, 'cover_image');
                if ($coverImage) {
                    $coverImageName = $model->board->slug . '-' . $model->page_slug . '.' . $coverImage->extension;
                    // $saveImage = $this->azureService->upload($coverImage, 'assets/images/', $coverImageName);
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($coverImageName, 'board_schema'), $coverImage->tempName);
                    if ($saveImage) {
                        // $model->cover_image = 'schema_board/' . $coverImageName;
                        $model->cover_image = $coverImageName;
                    }
                }
            }
            $model->is_freelancer = $data['BoardContent']['is_freelancer'];
            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing BoardContent model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        $data = Yii::$app->request->post();

        /**** Check for cache and restricted urls in content ****/
        if (!empty($data['BoardContent']['content'])) {
            $restrictedUrl = DataHelper::checkRestrictedUrl($data['BoardContent']['content']);
        }
        if (!empty($restrictedUrl)) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        /*********************************************************/

        if ($model->load(Yii::$app->request->post())) {
            if (empty($model->cover_image)) {
                unset($model->cover_image);
            }

            $coverImage = UploadedFile::getInstance($model, 'cover_image');
            if ($coverImage) {
                $coverImageName = $model->board->slug . '-' . $model->page_slug . '.' . $coverImage->extension;
                // $saveImage = $this->azureService->upload($coverImage, 'assets/images/', $coverImageName);
                $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($coverImageName, 'board_schema'), $coverImage->tempName);
                if ($saveImage) {
                    // $model->cover_image = 'schema_board/' . $coverImageName;
                    $model->cover_image = $coverImageName;
                    $model->is_freelancer = $data['BoardContent']['is_freelancer'];
                    if ($model->save()) {
                        return $this->redirect(['view', 'id' => $model->id]);
                    } else {
                        return $this->render('update', [
                            'model' => $model,
                        ]);
                    }
                }
            } else {
                $model->is_freelancer = $data['BoardContent']['is_freelancer'];
                if ($model->save()) {
                    return $this->redirect(['view', 'id' => $model->id]);
                } else {
                    return $this->render('update', [
                        'model' => $model,
                    ]);
                }
            }
        } else {
            if (!empty($model->parent_id)) {
                $parentModel = $this->findModel($model->parent_id);
                $model->page_slug = $parentModel->page_slug;
            }
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing BoardContent model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     *
     *   public function actionDelete($id)
     *   {
     *       return false;
     *
     *       $this->findModel($id)->delete();
     *
     *       return $this->redirect(['index']);
     *   }
     */

    /**
     * Finds the BoardContent model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return BoardContent the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = BoardContent::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionCheckBoardPageCombination()
    {
        $data = Yii::$app->request->post();
        $allowed = ['Syllabus', 'Supplementary', 'Notes', 'Answer Key', 'Question Paper', 'Books', 'Deleted Syllabus'];
       
        if (in_array($data['page'], $allowed)) {
            $existingContent = BoardContent::find()->select('id')->where(['board_id' => $data['board']])->andWhere(['page' => $data['page']])->one();
            if (!empty($existingContent) && $existingContent->id) {
                return true;
            }
        }
        return false;
    }

    protected function checkExistingContent($data)
    {
        return BoardContent::find()->select('id')->where(['board_id' => $data['BoardContent']['board_id']])->andWhere(['page' => $data['BoardContent']['page']])->one();
    }

    protected function checkExistingSubPageContent($data, $subPage, $parent_id)
    {
        return BoardContent::find()->select('id')->where(['board_id' => $data['BoardContent']['board_id']])->andWhere(['page' => $subPage])->andWhere(['parent_id' => $parent_id])->one();
    }

    public function actionBoardListByLang()
    {
        $lang = Yii::$app->request->get('lang_code');
        $requestParamQuery = Yii::$app->request->get('q');

        $data = Board::find()
            ->where(['lang_code' => $lang])
            ->andWhere(['status' => Board::STATUS_ACTIVE])
            ->andWhere(['like', 'display_name', '%' . $requestParamQuery . '%', false])
            ->active()
            ->all();

        $data = array_map(function ($list) {
            return [
                'id' => $list['id'],
                'text' => $list['display_name']
            ];
        }, $data);

        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $out['results'] = array_values($data);

        return $out;
    }

    /**
     * Displays a single BoardContent value for preview.
     * @param integer $id
     * @return mixed
     */
    public function actionPreview($id)
    {
        return $this->render('preview', [
            'model' => $this->findModel($id),
        ]);
    }
}
