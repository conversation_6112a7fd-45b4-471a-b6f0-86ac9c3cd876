<?php

namespace backend\controllers;

use Yii;
use common\models\InstituteProgramRankMapping;
use backend\models\InstituteProgramRankMappingSearch;
use common\models\College;
use common\models\Program;
use common\services\CollegeService;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * InstituteProgramRankMappingController implements the CRUD actions for InstituteProgramRankMapping model.
 */
class InstituteProgramRankMappingController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all InstituteProgramRankMapping models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new InstituteProgramRankMappingSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single InstituteProgramRankMapping model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new InstituteProgramRankMapping model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new InstituteProgramRankMapping();

        if ($model->load(Yii::$app->request->post())) {
            if ($model->college_id) {
                $college = College::findOne($model->college_id);
                $feature = (new CollegeService)->getFeatures($college);
                $collegeTypeValue = !empty($feature['Institution Type']) ? ucfirst($feature['Institution Type']['value']) : '';
                if ($college && $college->city) {
                    $model->state_id = $college->city->state_id ?? null;  // Set state_id from college's city
                }
                $model->college_type = ucfirst($collegeTypeValue) ?? '';
            }

            if ($model->program_id) {
                $program = Program::findOne($model->program_id);
                $model->course_id = $program->programCourseMapping->course_id ?? null;
            }

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                print_r($model->getErrors());
                return $this->render('create', [
                    'model' => $model,
                ]);
            }
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing InstituteProgramRankMapping model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            if ($model->college_id) {
                $college = College::findOne($model->college_id);
                $feature = (new CollegeService)->getFeatures($college);
                $collegeTypeValue = !empty($feature['Institution Type']) ? ucfirst($feature['Institution Type']['value']) : '';
                if ($college && $college->city) {
                    $model->state_id = $college->city->state_id ?? null;  // Set state_id from college's city
                }
                $model->college_type = ucfirst($collegeTypeValue) ?? '';
            }

            if ($model->program_id) {
                $program = Program::findOne($model->program_id);
                $model->course_id = $program->programCourseMapping->course_id ?? null;
            }

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                print_r($model->getErrors());
                return $this->render('update', [
                    'model' => $model,
                ]);
            }
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing InstituteProgramRankMapping model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the InstituteProgramRankMapping model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return InstituteProgramRankMapping the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = InstituteProgramRankMapping::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
