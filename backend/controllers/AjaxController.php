<?php

namespace backend\controllers;

use backend\models\UploadForm;
use common\helpers\DataConversion;
use common\helpers\DataHelper;
use common\models\Article;
use common\models\NcertArticles;
use common\models\Board;
use common\models\Category;
use common\models\City;
use common\models\College;
use common\models\Country;
use common\models\old\GmuColleges;
use Yii;
use common\models\Course;
use common\models\Exam;
use common\models\Stream;
use common\models\News;
use common\models\State;
use common\models\Tag;
use common\models\BoardSamplePaper;
use common\models\Career;
use common\models\BusinessEntity;
use common\models\BusinessEntitySpoc;
use common\models\CareerContent;
use common\models\Company;
use common\models\LiveUpdate;
use common\models\NewsSubdomain;
use common\models\Program;
use common\models\Specialization;
use common\models\Status;
use common\models\UserMapping;
use common\models\OlympiadContent;
use yii\web\Controller;
use yii\web\Response;
use yii\web\UploadedFile;
use common\services\S3Service;
use yii\helpers\ArrayHelper;
use common\models\User;
use common\models\ContentTemplate;
use common\models\EducationBodyType;
use common\helpers\CollegeHelper;
use common\models\CollegeCourse;
use common\models\CutoffCategory;
use common\models\Degree;
use common\models\ExamContent;
use common\models\Feature;
use common\models\Filter;
use common\models\FilterPageSeo;
use common\models\LeadBucketCta;
use common\models\LeadBucketCtaDetail;
use common\models\ManageNavMenuOrder;
use common\models\SaCollege;
use common\models\SaCountry;
use common\models\SaCourse;
use common\models\SaCourseDurationType;
use common\models\SaDegree;
use common\models\SaFaq;
use common\models\SaSpecialization;
use common\models\SaStream;
use yii\web\NotFoundHttpException;

use frontend\controllers\AjaxController as ControllersAjaxController;

/**
 * ExamContentController implements the CRUD actions for ExamContent model.
 */
class AjaxController extends Controller
{

    /**
     * Get tinymce image upload path.
     *
     * Index 1: Backend image upload
     * Index 2: Frontend image show
     *
     * @param string $type Pass Entity type to get the image upload/show path
     * @return string|null
     **/
    private function getTinymceUploadPath($type)
    {
        $tinymceUploadPath = [
            Article::ENTITY_ARTICLE => Yii::getAlias('@articleTinymceUpload') . '~' . Yii::getAlias('@articleTinymceFrontend'),
            Article::ENTITY_STUDY_ABROAD => Yii::getAlias('@studyAbroadArticleTinymce') . '~' . Yii::getAlias('@studyAbroadArticleTinymce'),
            Category::ENTITY_CATEGORY => Yii::getAlias('@categoryTinymceUpload') . '~' . Yii::getAlias('@categoryTinymceFrontend'),
            Exam::ENTITY_EXAM => Yii::getAlias('@examTinymceUpload') . '~' . Yii::getAlias('@examTinymceFrontend'),
            College::ENTITY_COLLEGE => Yii::getAlias('@collegeTinymceUpload') . '~' . Yii::getAlias('@collegeTinymceFrontend'),
            Board::ENTITY_BOARD => Yii::getAlias('@boardTinymceUpload') . '~' . Yii::getAlias('@boardTinymceFrontend'),
            Course::ENTITY_COURSE => Yii::getAlias('@courseTinymceUpload') . '~' . Yii::getAlias('@courseTinymceFrontend'),
            News::ENTITY_NEWS => Yii::getAlias('@newsTinymceUpload') . '~' . Yii::getAlias('@newsTinymceFrontend'),
            BoardSamplePaper::ENTITY_BOARD_SAMPLE_PAPER => Yii::getAlias('@boardSamplePaperTinymceUpload') . '~' . Yii::getAlias('@boardSamplePaperTinymceFrontend'),
            LiveUpdate::ENTITY_LIVE_NEWS => Yii::getAlias('@liveNewsTinymceUpload') . '~' . Yii::getAlias('@liveNewsTinymceFrontend'),
            NcertArticles::ENTITY_NCERT => Yii::getAlias('@ncertTinymceUpload') . '~' . Yii::getAlias('@ncertTinymceFrontend'),
            CareerContent::CAREER_CONTENT_ENTITY => Yii::getAlias('@careerTinymceUpload') . '~' . Yii::getAlias('@careerTinymceFrontend'),
            OlympiadContent::ENTITY_OLYMPIAD_CONTENT => Yii::getAlias('@olympiadTinymceUpload') . '~' . Yii::getAlias('@olympiadTinymceFrontend'),
        ];

        if (isset($tinymceUploadPath[$type])) {
            return $tinymceUploadPath[$type];
        }

        return [];
    }

    /**
     * Undocumented function
     *
     * @param [type] $query
     * @return void
     */
    public function actionCourseList($query, $entity = '')
    {
        $models = Course::find()
            ->select(['id', 'name', 'slug'])
            ->parentOnly()
            ->andWhere(['like', 'name', $query])
            ->all();

        $items = [];
        foreach ($models as $model) {
            if (!empty($entity)) {
                $items[] = ['id' => $model[$entity], 'text' => $model[$entity]];
            } else {
                $items[] = ['id' => $model['id'], 'text' => $model['name']];
            }
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Undocumented function
     *
     * @param [type] $query
     * @return void
     */
    public function actionTagList($query)
    {
        $models = Tag::find()->andWhere(['like', 'name', $query])->all();
        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model->id, 'slug' => $model->slug, 'name' => $model->name];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return $items;
    }

    public function actionTinymceUpload()
    {
        $request = Yii::$app->request;
        $model = new UploadForm();
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!$request->isAjax) {
            return false;
        }
        if (!$filepath = $this->getTinymceUploadPath($request->post('type'))) {
            return false;
        }
        list($upload, $frontendPath) = explode('~', $filepath);
        $model->editorImage = UploadedFile::getInstance($model, 'editorImage');
        $fileName = $request->post('type') . '-' . md5(uniqid(rand(), true)) . '.' . $model->editorImage->extension;
        $response = (new S3Service())->uploadFile(DataHelper::s3Path($fileName, $request->post('type')), $model->editorImage->tempName);
        // $response = $model->uploadTinymce($upload, $fileName);
        if (!empty($response)) {
            return [
                'location' => $frontendPath . '/' . $fileName
            ];
        }

        return [];
    }


    public function actionGetCollege($q = null)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $data = ['results' => ['id' => '', 'name' => '']];

        if (is_null($q)) {
            return $data;
        }

        $colleges = GmuColleges::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', trim($q)])
            ->andWhere(['is_deleted' => (string) GmuColleges::STATUS_ACTIVE])
            ->limit(10)
            ->all();

        $data['results'] = array_values($colleges);

        return $data;
    }

    /**
     * Finds college list on the basis of search parameter.
     * @param string $q
     * @return College List matched with $q param
     */
    public function actionCollegeList($q, $entity = '')
    {
        $query = College::find()
            ->select(['id', 'name', 'slug'])
            ->where(['like', 'name', $q])
            ->andWhere(['status' => College::STATUS_ACTIVE]);

        if (!empty($entity)) {
            $query->where(['type' => College::TYPE_UNIVERSITY]);
        }

        $models = $query->all();

        $items = [];
        foreach ($models as $model) {
            if (!empty($entity)) {
                $items[] = ['id' => $model[$entity], 'text' => $model[$entity]];
            } else {
                $items[] = ['id' => $model['id'], 'text' => $model['name']];
            }
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Finds board list on the basis of search parameter.
     * @param string $q
     * @return Board List matched with $q param
     */
    public function actionBoardList($q)
    {
        $models = Board::find()
            ->select(['id', 'display_name'])
            ->where(['like', 'display_name', $q])
            ->andWhere(['status' => Board::STATUS_ACTIVE])
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['display_name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     *
     * @param [type] $query
     * @return void
     */
    public function actionExamList($query, $entity = '')
    {
        $models = Exam::find()
            ->select(['id', 'name', 'slug'])
            ->where(['like', 'name', $query])
            ->andWhere(['status' => Exam::STATUS_ACTIVE])
            ->all();

        $items = [];
        foreach ($models as $model) {
            if (!empty($entity)) {
                $items[] = ['id' => $model[$entity], 'text' => $model[$entity]];
            } else {
                $items[] = ['id' => $model['id'], 'text' => $model['name']];
            }
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Finds article list on the basis of search parameter.
     * @param string $q
     * @return Article List matched with $q param
     */
    public function actionArticleList($q)
    {
        $models = Article::find()
            ->select(['id', 'title'])
            ->where(['like', 'title', $q])
            ->andWhere(['status' => Article::STATUS_ACTIVE])
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['title']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Finds article list on the basis of search parameter with respective to slug.
     * @param string $q
     * @return Article List matched with $q param
     */
    public function actionTranslatedArticleList($q)
    {
        $models = Article::find()
            ->select(['id', 'slug'])
            ->where(['like', 'slug', $q])
            ->andWhere(['status' => Article::STATUS_ACTIVE])
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['slug']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Finds news list on the basis of search parameter.
     * @param string $q
     * @return News List matched with $q param
     */
    public function actionNewsList($q)
    {
        $data = [];

        if (empty($q)) {
            return false;
        }
        $models = News::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->andWhere(['status' => News::STATUS_ACTIVE])
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Finds news list on the basis of search parameter.
     * @param string $q
     * @return News List matched with $q param
     */
    public function actionSubdomainNewsList($q)
    {
        $data = [];

        if (empty($q)) {
            return false;
        }
        $models = NewsSubdomain::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->andWhere(['status' => News::STATUS_ACTIVE])
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     *
     * @param
     * @return array
     * Get study abroad country for country field
     */

    public function actionGetCountry()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $item = [];
        if (isset($_POST['depdrop_parents'])) {
            $parents = $_POST['depdrop_parents'];
            if ($parents != null && $parents[0] == Article::ENTITY_STUDY_ABROAD) {
                $countries = Country::find()->select(['id', 'slug', 'name'])->active()->all();

                foreach ($countries as $country) {
                    $item[] = [
                        'id' => $country['slug'],
                        'name' => $country['name'],
                    ];
                }

                return ['output' => $item, 'selected' => ''];
            }
        }
        return ['output' => '', 'selected' => ''];
    }

    //get page name based on category
    public function actionCachePage()
    {
        $categoryValues = [
            '0' => 'News',
        ];

        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $item = [];
        if (isset($_POST['depdrop_parents'])) {
            $parents = $_POST['depdrop_parents'];
            if (isset($parents[0])) {
                $data = DataHelper::cacheFlush();
                $pageValue = $categoryValues[$parents[0]];
                foreach ($data as $keys => $value) {
                    foreach ($value as $detail) {
                        if ($pageValue == $keys) {
                            $item[] = [
                                'id' => $detail['category'],
                                'name' => $detail['category'],
                            ];
                        }
                    }
                }

                return ['output' => $item, 'selected' => ''];
            }
        }
        return ['output' => '', 'selected' => ''];
    }
    /*
     * Finds State list on the basis of search parameter.
     * @param string $q
     * @return State List matched with $q param
     */
    public function actionStateList($q, $entity = '')
    {
        $models = State::find()
            ->select(['id', 'name', 'slug'])
            ->where(['like', 'name', $q])
            ->all();

        $items = [];
        foreach ($models as $model) {
            if (!empty($entity)) {
                $items[] = ['id' => $model[$entity], 'text' => $model[$entity]];
            } else {
                $items[] = ['id' => $model['id'], 'text' => $model['name']];
            }
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Undocumented function
     *
     * @param [type] $query
     * @return void
     */
    public function actionStreamList($query, $entity = '')
    {
        $models = Stream::find()
            ->select(['id', 'name', 'slug'])
            ->where(['like', 'name', $query])
            // ->andWhere(['<>', 'slug', 'other'])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            if (!empty($entity)) {
                $items[] = ['id' => $model[$entity], 'text' => $model[$entity]];
            } else {
                $items[] = ['id' => $model['id'], 'text' => $model['name']];
            }
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Undocumented function
     *
     * @param [type] $query
     * @return void
     */
    public function actionDegreeList($query)
    {
        $models = Degree::find()
            ->select(['id', 'display_name', 'name', 'slug'])
            ->where(['like', 'display_name', $query])
            // ->andWhere(['<>', 'slug', 'other'])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name'] . '-' . $model['display_name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }


    /**
     * Undocumented function
     * load specialization list on course panel
     * @param [type] $query
     * @return void
     */
    public function actionSpecializationList($query, $entity = '')
    {
        $models = Specialization::find()
            ->select(['id', 'name', 'slug'])
            ->andWhere(['like', 'name', $query])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            if (!empty($entity)) {
                $items[] = ['id' => $model[$entity], 'text' => $model[$entity]];
            } else {
                $items[] = ['id' => $model['id'], 'text' => $model['name']];
            }
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    public function actionCoursesList()
    {
        $options = '';
        Yii::$app->response->format = Response::FORMAT_JSON;

        if ($streamIds = Yii::$app->request->post('ids')) {
            $courses = Course::find()->where(['stream_id' => $streamIds])->all();
            $course_ids = Yii::$app->request->post('courses');
            if (count($courses) > 0) {
                foreach ($courses as $course) {
                    if (!empty($course_ids)) {
                        if (in_array($course->id, $course_ids)) {
                            $options .=  "<option value='" . $course->id . "' selected>" . $course->name . '</option>';
                        } else {
                            $options .=  "<option value='" . $course->id . "'>" . $course->name . '</option>';
                        }
                    } else {
                        $options .=  "<option value='" . $course->id . "'>" . $course->name . '</option>';
                    }
                }
            } else {
                $options .= "'<option>-</option>'";
            }
        }

        return $options;
    }

    /**
     * Undocumented function
     *
     * @param [type] $query
     * @return void
     */
    public function actionCityList()
    {
        $options = '';
        Yii::$app->response->format = Response::FORMAT_JSON;
        if ($stateIds = Yii::$app->request->post('ids')) {
            $states = City::find()->where(['state_id' => $stateIds])->all();
            $state_ids = Yii::$app->request->post('state');
            if (count($states) > 0) {
                foreach ($states as $state) {
                    if (!empty($state_ids)) {
                        if (in_array($state->id, $stateIds)) {
                            $options .=  "<option value='" . $state->id . "' selected>" . $state->name . '</option>';
                        } else {
                            $options .=  "<option value='" . $state->id . "'>" . $state->name . '</option>';
                        }
                    } else {
                        $options .=  "<option value='" . $state->id . "'>" . $state->name . '</option>';
                    }
                }
            } else {
                $options .= "'<option>-</option>'";
            }
        }

        return $options;
    }

    /**
     * Finds college list on the basis of Search Parameter if Sponsor is True.
     * @param string $q
     * @return College List matched with $q param
     */
    public function actionSponsorCollegeList($q)
    {
        $models = College::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->andWhere(['is_sponsored' => true])
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /** Finds course list on the basis of search parameter.
     * @param string $q
     * @return Course List matched with $q param
     */
    public function actionAllCourse($q)
    {
        $models = Course::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    public function actionBdCollege($query = null)
    {
        $models = College::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $query])
            ->andWhere(['status' => College::STATUS_ACTIVE])
            ->all();
        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Undocumented function
     *
     * @param [type] $query
     * @return void
     */
    public function actionCompanyList($query)
    {
        $models = Company::find()
            ->select(['id', 'name'])
            ->andWhere(['like', 'name', $query])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /** Get the College Data from Business Entity */
    public function actionGetBusinessEntity($q)
    {
        $results = BusinessEntity::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->andWhere(['status' => BusinessEntity::STATUS_ACTIVE])
            ->all();

        $items = [];
        foreach ($results as $result) {
            $items[] = ['id' => $result->id, 'text' => $result->name];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;
        $data['results'] = $items;

        return $data;
    }

    /** Get the status of BDE */
    public function actionGetStatus()
    {
        $results = Status::find()->select(['id', 'name'])->all();

        $items = [];
        foreach ($results as $result) {
            $items[] = ['id' => $result->id, 'text' => $result->name];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;
        return $data;
    }

    /** Get Business Entity Lead BDE */
    public function actionGetBde()
    {
        $userRole = Yii::$app->authManager->getRolesByUser(Yii::$app->user->identity->id);
        // dd($useRole);
        if (array_key_exists(Yii::$app->params['tlRole'], $userRole)) {
            $counsellorIds = DataConversion::objectToArrayConversion(UserMapping::find()->getBdeList(Yii::$app->user->identity->id)->all(), 'id', 'child_id');
            $counsellors = DataConversion::objectToArrayConversion(User::find()->where(['in', 'id', $counsellorIds])->all(), 'id', 'name');
            $counsellors[Yii::$app->user->identity->id] = Yii::$app->user->identity->name;
        } else if (array_key_exists(Yii::$app->params['BDHead'], $userRole)) {
            $counsellors = UserMapping::find()->getSalesManagerBDEList(Yii::$app->user->identity->id, true);
            $counsellors[Yii::$app->user->identity->id] = Yii::$app->user->identity->username;
        } elseif (array_key_exists(Yii::$app->params['bdeRole'], $userRole)) {
            $counsellors = [Yii::$app->user->identity->id => Yii::$app->user->identity->name];
        } else {
            // get all active BDE
            $user = new User();
            $allBDERoles = $user->getCounsellorsToAssignLeads(); //@todo to be changed with counsellor function
            $counsellors = ArrayHelper::map($allBDERoles, 'id', 'name');
        }
        // dd($counsellors);
        $items = [];
        foreach ($counsellors as $key => $value) {
            $items[] = ['id' => $key, 'text' => $value];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;
        return $data;
    }

    /** Get the Spoc of BDE */
    public function actionGetSpoc($q)
    {
        $spocs = BusinessEntitySpoc::find()->select(['id', 'name'])->where(['business_entity_id' => $q])->all();

        $items = [];
        foreach ($spocs as $spoc) {
            $items[] = ['id' => $spoc->id, 'text' => $spoc->name];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;
    }

    /** Finds user list on the basis of search parameter with respective to name.
     * @param string $q
     * @return User List matched with $q param
     */
    public function actionAllUsers($q)
    {
        $models = User::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->andWhere(['status' => User::STATUS_ACTIVE])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Finds career list on the basis of search parameter.
     * @param string $q
     * @return Career List matched with $q param
     */
    public function actionCareerList($q)
    {
        $models = Career::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->andWhere(['status' => Career::STATUS_ACTIVE])
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Undocumented function
     *
     * @param [type] $query
     * @return void
     */
    public function actionProgramList($query)
    {
        $models = Program::find()
            ->select(['id', 'name'])
            ->andWhere(['like', 'name', $query])
            ->andWhere(['status' => Program::STATUS_ACTIVE])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    public function actionTemplateList()
    {
        $request = Yii::$app->request;
        $page = $request->get('sub_page');
        $q = $request->get('q');
        $entity = $request->get('entity');
        $models = ContentTemplate::find()
            ->where(['page' => $page])
            ->andWhere(['like', 'name', $q])
            ->andWhere(['entity_type' => $entity])
            ->andWhere(['status' => ContentTemplate::STATUS_ACTIVE])
            ->all();
        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    //fetches the list of cities based states
    public function actionStatewiseCityList()
    {
        $data = [];
        Yii::$app->response->format = Response::FORMAT_JSON;
        $stateIds = Yii::$app->request->get('ids');
        $q = Yii::$app->request->get('q');
        if ($stateIds) {
            $states = City::find()
                ->select(['name', 'id'])
                ->where(['state_id' => $stateIds])
                ->andWhere(['like', 'name', $q])
                ->all();
            if (count($states) > 0) {
                foreach ($states as $state) {
                    $items[] = [
                        'id' => $state['id'],
                        'text' => $state['name'],
                    ];
                }
                $data['results'] = $items;
                return $data;
            }
        }
    }

    /** Finds user list on the basis of search parameter with respective to name.
     * @param string $q
     * @return User List matched with $q param
     */
    public function actionExamUsers($q)
    {
        $models = User::find()
            ->select(['id', 'entity_id', 'name'])
            ->where(['like', 'name', $q])
            ->andWhere(['status' => User::STATUS_ACTIVE])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['entity_id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     * Finds city list on the basis of search parameter.
     * @param string $q
     * @return City List matched with $q param
     */
    public function actionCityLists($q, $entity = '')
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $citiesData = [];
        $englishPattern = '/^[A-Za-z0-9\s]*$/';
        if (!preg_match($englishPattern, $request->get('term'))) {
            return [];
        }

        $cities = City::find()
            ->select(['id', 'name', 'state_id', 'slug'])
            ->where(['like', 'name', trim($q)])
            ->andWhere(['status' => City::STATUS_ACTIVE])
            ->all();

        if (empty($cities)) {
            return [];
        }

        foreach ($cities as $city) {
            if (!empty($entity)) {
                $citiesData[] = [
                    'id' => $city->$entity,
                    'text' => $city->$entity,
                ];
            } else {
                $citiesData[] = [
                    'id' => $city->id,
                    'text' => $city->name,
                ];
            }
        }

        $data['results'] = $citiesData;

        return $data;
    }

    public function actionGetProgramMode($q, $entity = '')
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $items = [];
        $collegeModes = CollegeCourse::find()->select(['mode'])->distinct()->all();

        if (empty($collegeModes)) {
            return [];
        }

        foreach ($collegeModes as $collegeMode) {
            if ($entity == 'name') {
                $items[] = [
                    'id' => ucfirst($collegeMode->mode),
                    'text' => ucfirst($collegeMode->mode),
                ];
            } else {
                $items[] = [
                    'id' => $collegeMode->mode,
                    'text' => $collegeMode->mode,
                ];
            }
        }
        $data['results'] = $items;

        return $data;
    }

    public function actionGetCourseType($q, $entity)
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $items = [];

        $degrees = Degree::find()
            ->where(['like', 'name', trim($q)])
            ->select(['slug', 'name'])
            ->all();

        if (empty($degrees)) {
            return [];
        }

        foreach ($degrees as $degree) {
            if ($entity == 'name') {
                $items[] = [
                    'id' => $degree->name,
                    'text' => $degree->name,
                ];
            } else {
                $items[] = [
                    'id' => $degree->slug,
                    'text' => $degree->slug,
                ];
            }
        }

        $data['results'] = $items;

        return $data;
    }

    //approvals
    public function actionGetApprovals($q, $entity)
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $items = [];

        $filters = Feature::find()
            ->select('name, slug')
            ->where(['feature_group_id' => 3])
            ->andWhere(['like', 'name', trim($q)])
            ->groupBy('slug')
            ->active()
            ->all();

        if (empty($filters)) {
            return [];
        }

        foreach ($filters as $filter) {
            if ($entity == 'name') {
                $items[] = [
                    'id' => $filter->name,
                    'text' => $filter->name,
                ];
            } else {
                $items[] = [
                    'id' => $filter->slug,
                    'text' => $filter->slug,
                ];
            }
        }

        $data['results'] = $items;

        return $data;
    }

    public function actionGetFilterList()
    {
        $requestParam = Yii::$app->request->get('depdrop_parents');
        $requestParamValues = Yii::$app->request->get('name');
        $requestParamQuery = Yii::$app->request->get('q');

        if ($requestParam == 1) {
            return self::actionStateList($requestParamQuery, $requestParamValues);
        } elseif ($requestParam == 2) { //parent_id state_id
            return self::actionCityLists($requestParamQuery, $requestParamValues);
        } elseif ($requestParam == 3) {
            return self::actionStreamList($requestParamQuery, $requestParamValues);
        } elseif ($requestParam == 4) { //parent_id stream_id
            return self::actionCourseList($requestParamQuery, $requestParamValues);
        } elseif ($requestParam == 5) { //parent_id stream_id
            return self::actionSpecializationList($requestParamQuery, $requestParamValues);
        } elseif ($requestParam == 6) {
            return self::actionGetProgramMode($requestParamQuery, $requestParamValues);
        } elseif ($requestParam == 8) {
            return self::actionExamList($requestParamQuery, $requestParamValues);
        } elseif ($requestParam == 9) {
            return self::actionGetCourseType($requestParamQuery, $requestParamValues);
        } elseif ($requestParam == 10) {
            return self::actionCollegeList($requestParamQuery, $requestParamValues);
        } elseif ($requestParam == 12) {
            return self::actionGetApprovals($requestParamQuery, $requestParamValues);
        }
    }

    public function actionGetFilterParentList()
    {
        $requestParam = Yii::$app->request->get('depdrop_parents');
        $requestParamQuery = Yii::$app->request->get('q');

        $parentArrayMap = [
            2 => 1,
            4 => 3,
            5 => 3,
        ];

        $models = Filter::find()
            ->select(['id', 'name', 'slug'])
            ->where(['like', 'name', $requestParamQuery])
            ->andWhere(['filter_group_id' => $parentArrayMap[$requestParam]])
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    public function actionGetSaveMenuOrder()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $data = Yii::$app->request->post();
        $examContent = [];
        $returnData = [];
        foreach ($data['data'] as $val) {
            $exam_id = explode('--', $val);
            $examContent[$exam_id[2]] = $exam_id[3] . '--' . $exam_id[0];
        }
        $checkExits = ManageNavMenuOrder::find()->select(['id'])->where(['entity_id' => $exam_id[1]])->andWhere(['entity' => DataHelper::$manageMenuOrder[$data['entity']]])->one();
        if (!empty($checkExits)) {
            $model = $this->findModel($checkExits->id);
        } else {
            $model = new ManageNavMenuOrder();
        }
        $model->entity = 1;
        $model->entity_id = $exam_id[1];
        $model->menu_order = serialize($examContent);
        try {
            $model->save();
            $returnData['success'] = true;
        } catch (\Throwable $th) {
            $returnData['success'] = false;
        }
        return $returnData;
    }

    public function actionGetSaveBoardMenuOrder()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $data = Yii::$app->request->post();
        //echo "<pre>"; print_r($data); die;
        $boardContent = [];
        $returnData = [];
        foreach ($data['data'] as $val) {
            $board_id = explode('--', $val);
            $boardContent[$board_id[2]] = $board_id[3] . '--' . $board_id[0];
        }
        $checkExits = ManageNavMenuOrder::find()->select(['id'])->where(['entity_id' => $board_id[1]])->andWhere(['entity' => DataHelper::$manageMenuOrder[$data['entity']]])->one();
        if (!empty($checkExits)) {
            $model = $this->findModel($checkExits->id);
        } else {
            $model = new ManageNavMenuOrder();
        }
        $model->entity = 2;
        $model->entity_id = $board_id[1];
        $model->menu_order = serialize($boardContent);
        try {
            $model->save();
            $returnData['success'] = true;
        } catch (\Throwable $th) {
            $returnData['success'] = false;
        }
        return $returnData;
    }


    protected function findModel($id)
    {
        if (($model = ManageNavMenuOrder::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    //sa country lists
    public function actionSaCountry($q)
    {
        $models = SaCountry::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->andWhere(['status' => SaCountry::STATUS_ACTIVE])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    //sa college lists
    public function actionSaCollege($q)
    {
        $models = SaCollege::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    //sa course lists
    public function actionSaCourse($q)
    {
        $models = SaCourse::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    //sa stream lists
    public function actionSaStream($q)
    {
        $models = SaStream::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    //sa degree lists
    public function actionSaDegree($q)
    {
        $models = SaDegree::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    //sa specialization lists
    public function actionSaSpecialization($q)
    {
        $models = SaSpecialization::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    //sa course duration mode lists
    public function actionSaCourseDurationMode($q)
    {
        $models = SaCourseDurationType::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $q])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    /**
     *
     * @param
     * @return array
     * Get study abroad country for country field
     */

    public function actionGetEntityId()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $item = [];
        if (isset($_POST['depdrop_parents'])) {
            $parents = $_POST['depdrop_parents'];
            if ($parents != null && $parents[0] == SaFaq::ENTITY_COUNTRY) {
                $countries = SaCountry::find()->select(['id', 'slug', 'name'])->where(['status' => SaCountry::STATUS_ACTIVE])->all();

                foreach ($countries as $country) {
                    $item[] = [
                        'id' => $country['id'],
                        'name' => $country['name'],
                    ];
                }

                return ['output' => $item, 'selected' => ''];
            } else {
                $colleges = SaCollege::find()->select(['id', 'name'])->where(['status' => SaCollege::STATUS_ACTIVE])->all();

                foreach ($colleges as $college) {
                    $item[] = [
                        'id' => $college['id'],
                        'name' => $college['name'],
                    ];
                }

                return ['output' => $item, 'selected' => ''];
            }
        }
        return ['output' => '', 'selected' => ''];
    }

    public function actionAllCtaData($q)
    {
        $models = LeadBucketCta::find()
            ->select(['cta_text'])
            ->distinct()
            ->where(['NOT', ['cta_text' => null]])
            ->andWhere(['!=', 'cta_text', ''])
            ->andWhere(['like', 'cta_text', $q])
            ->union(
                LeadBucketCtaDetail::find()
                    ->select(['cta_text'])
                    ->distinct()
                    ->where(['NOT', ['cta_text' => null]])
                    ->andWhere(['!=', 'cta_text', ''])
                    ->andWhere(['like', 'cta_text', $q])
            )
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['cta_text'], 'text' => $model['cta_text']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    public function actionFilterPageSeoList($q)
    {
        $models = FilterPageSeo::find()
            ->select(['id', 'slug'])
            ->where(['like', 'slug', $q])
            ->andWhere(['status' => FilterPageSeo::STATUS_ACTIVE])
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['slug']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    public function actionCutoffCategoryList($query)
    {
        $models = CutoffCategory::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', $query])
            ->asArray()
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }

    public function actionCollegePredictorExamList($query = '')
    {
        $models = Exam::find()
            ->select(['exam.id', 'exam.display_name'])
            ->leftJoin('exam_content', 'exam.id = exam_content.exam_id')
            ->where(['like', 'exam..display_name', $query])
            ->andWhere(['exam_content.slug' => 'college-predictor'])
            ->andWhere(['exam_content.status' => ExamContent::STATUS_ACTIVE])
            ->andWhere(['not', ['exam.primary_stream_id' => null]])
            ->andWhere(['exam.status' => Exam::STATUS_ACTIVE])
            ->all();

        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['display_name']];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }
}
