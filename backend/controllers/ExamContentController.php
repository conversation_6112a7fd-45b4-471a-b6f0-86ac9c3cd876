<?php

namespace backend\controllers;

use Yii;
use common\models\ExamContent;
use backend\models\ExamContentSearch;
use Carbon\Carbon;
use common\helpers\DataHelper;
use common\models\SeoInfo;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\models\Exam;
use common\models\ManageNavMenuOrder;

/**
 * ExamContentController implements the CRUD actions for ExamContent model.
 */
class ExamContentController extends Controller
{

    /**
     * Lists all ExamContent models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ExamContentSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ExamContent model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        // $seoModel = SeoInfo::find()->unique(SeoInfo::ENTITY_EXAM, $model->exam_id, $model->slug);
        $query = SeoInfo::find()->where(['entity' => SeoInfo::ENTITY_EXAM, 'entity_id' => $model->exam_id, 'page' => $model->slug]);

        if (!empty($model->parent_id)) {
            $query->andWhere(['parent_id' => $model->parent_id]);
        }

        $seoModel = $query->one();

        return $this->render('view', [
            'model' => $model,
            'seoModel' => $seoModel ?? [],
        ]);
    }

    /**
     * Creates a new ExamContent model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($examId)
    {
        $model = new ExamContent();
        $model->exam_id = $examId;
        $seoModel = new SeoInfo();

        $searchModel = new ExamContentSearch(['exam_id' => $examId]);
        $dataGet = [];
        if (!empty((Yii::$app->request->queryParams)['ExamContentSearch'])) {
            $dataGet['ExamContentSearch'] = (Yii::$app->request->queryParams)['ExamContentSearch'];
        } else {
            $dataGet['ExamContentSearch'] = Yii::$app->request->queryParams;
        }


        $dataProvider = $searchModel->search($dataGet, 'create');

        if ($model->load(Yii::$app->request->post()) && $seoModel->load(Yii::$app->request->post())) {
            $data = Yii::$app->request->post();
            $model->created_by = $model->updated_by = Yii::$app->user->identity->id;
            /**** Check for cache and restricted urls in content ****/
            if (!empty($data['ExamContent']['content'])) {
                $restrictedUrl = DataHelper::checkRestrictedUrl($data['ExamContent']['content']);
            }
            if (!empty($restrictedUrl)) {
                return $this->redirect(Yii::$app->request->referrer);
            }
            /*********************************************************/

            if (!empty($data['ExamContent']['parent_id'])) {
                $dropDownArr = DataHelper::getExamDropDownSubPages();
                $dropValue = $dropDownArr[$data['ExamContent']['slug']][$data['ExamContent']['parent_id']];
                $parentId = ExamContent::find()
                    ->select('id')
                    ->where(['slug' => $data['ExamContent']['slug']])
                    ->andWhere(['exam_id' => $examId])->one();

                $existingSubPageContent = $this->checkExistingSubPageContent($examId, $dropValue, $parentId->id);

                if (!empty($existingSubPageContent)) {
                    Yii::$app->session->setFlash('error', 'Exam and Sub Page combination already exist');
                    return $this->redirect('/exam-content/create?examId=' . $examId);
                }
                $allowed = $this->getAllowed();
                if (in_array($data['ExamContent']['slug'], $allowed)) {
                    $existingContent = $this->checkExistingContent($data, $examId);
                    if ($existingContent->id) {
                        if ($model->status == Exam::STATUS_ACTIVE) {
                            $model->published_at = Carbon::now();
                        }
                        $model->parent_id = $existingContent->id;
                        $model->slug = $this->createSlug($dropValue);
                        $model->name = $dropValue;
                        $model->validate();
                        if ($model->save()) {
                            $this->createSeoInfo($seoModel, $model);
                            return $this->redirect(['view', 'id' => $model->id]);
                        } else {
                            print_r($model->getErrors());
                        }
                    } else {
                        Yii::$app->session->setFlash('error', 'Exam and Page combination does not exist');
                        return $this->redirect('/exam-content/create?examId=' . $examId);
                    }
                } else {
                    Yii::$app->session->setFlash('error', 'Exam and Sub Page combination does not allowed');
                    return $this->redirect('/exam-content/create?examId=' . $examId);
                }
            } else {
                if ($model->status == Exam::STATUS_ACTIVE) {
                    $model->published_at = Carbon::now();
                }
                $name = DataHelper::examContentList();
                $model->name = $name[$model->slug];

                $model->parent_id = null;
                if (!$model->save()) {
                    $model->validate();
                    Yii::$app->session->setFlash('error', $this->getValidationErrors($model->errors));
                    return $this->redirect('/exam-content/create?examId=' . $examId);
                }
                $this->createSeoInfo($seoModel, $model);
            }

            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('create', [
            'model' => $model,
            'seoModel' => $seoModel,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function createSeoInfo(SeoInfo $seoModel, ExamContent $model)
    {
        $seoModel->entity = SeoInfo::ENTITY_EXAM;
        $seoModel->entity_id = $model->exam_id;
        $seoModel->page = $model->slug;
        $seoModel->lang_code = $model->lang_code;
        $seoModel->parent_id = $model->parent_id ?? '';

        if ($seoModel->validate() && $seoModel->checkEmptyAndSave($seoModel)) {
            $seoModel->save();
        }
    }

    /**
     * Updates an existing ExamContent model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        $query = SeoInfo::find()
            ->where(['entity' => SeoInfo::ENTITY_EXAM, 'entity_id' => $model->exam_id, 'page' => $model->slug]);
        if (!empty($model->parent_id)) {
            $query->andWhere(['parent_id' => $model->parent_id]);
        }

        $seoModel = $query->one();

        $model->updated_by = Yii::$app->user->identity->id;

        if (empty($seoModel)) {
            $seoModel = new SeoInfo();
            $seoModel->entity = $seoModel::ENTITY_EXAM;
            $seoModel->entity_id = $model->exam_id;
        }

        $data = Yii::$app->request->post();

        /**** Check for cache and restricted urls in content ****/
        if (!empty($data['ExamContent']['content'])) {
            $restrictedUrl = DataHelper::checkRestrictedUrl($data['ExamContent']['content']);
        }
        if (!empty($restrictedUrl)) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        /*********************************************************/

        if (!empty($data) && isset($data['ExamContent']['status']) && $data['ExamContent']['status'] == Exam::STATUS_ACTIVE && $model->published_at == null) {
            $model->published_at = Carbon::now();
        }

        if ($model->load(Yii::$app->request->post()) && $seoModel->load(Yii::$app->request->post()) && $model->save()) {
            $seoModel->entity = SeoInfo::ENTITY_EXAM;
            $seoModel->entity_id = $model->exam_id;
            $seoModel->parent_id = $model->parent_id ?? '';
            $seoModel->page = $model->slug;
            $seoModel->lang_code = $model->lang_code;

            if ($seoModel->validate() && $seoModel->checkEmptyAndSave($seoModel)) {
                $seoModel->save();
            }

            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            if (!empty($model->parent_id)) {
                $allowed = $this->getAllowed();
                $parentModel = $this->findModel($model->parent_id);
                if (in_array($parentModel->slug, $allowed)) {
                    $model->slug = $parentModel->slug;
                }
                $model->parent_id = 1;
            }

            return $this->render('update', [
                'model' => $model,
                'seoModel' => $seoModel,
            ]);
        }
    }

    /**
     * Deletes an existing ExamContent model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    // public function actionDelete($id)
    // {
    //     $this->findModel($id)->delete();

    //     return $this->redirect(['index']);
    // }

    /**
     * Finds the ExamContent model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ExamContent the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ExamContent::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionCheckCollegePageCombination()
    {
        $data = Yii::$app->request->post();
        // $allowed = ['overview', 'eligibility', 'form', 'exam-centres', 'exam-pattern', 'syllabus', 'reference-books', 'results', 'cut-off', 'counselling', 'previous-years-papers'];
        $allowed = array_keys(DataHelper::getExamDropDownSubPages());
        if (in_array($data['page'], $allowed)) {
            $existingContent = ExamContent::find()->select('id')->where(['exam_id' => $data['exam']])->andWhere(['slug' => $data['page']])->one();
            if (!empty($existingContent) && $existingContent->id) {
                return true;
            }
        }
        return false;
    }

    //to do
    public function actionGetSubPage()
    {
        $subpage = [];
        $requestParam = Yii::$app->request->post('depdrop_parents');


        if (!empty($requestParam)) {
            $dropDownArr = DataHelper::getExamDropDownSubPages();
            $allowed = $this->getAllowed();
            if (in_array($requestParam['0'], $allowed)) {
                $subpage = array_map(function ($key, $value) {
                    return [
                        'id' => $key,
                        'name' => $value
                    ];
                }, array_keys($dropDownArr[$requestParam['0']]), array_values($dropDownArr[$requestParam['0']]));
            }
        }

        return json_encode(['output' => $subpage, 'selected' => '']);
    }

    private function createSlug($string)
    {
        if ($string == 'Logical Reasoning & Data Interpretation') {
            $string = 'Logical Reasoning Data Interpretation';
        } elseif ($string == 'Commerce & Accountancy') {
            $string = 'Commerce Accountancy';
        } elseif ($string == 'Political Science & International Relations') {
            $string = 'Political Science International Relations';
        }
        $string = str_replace(' ', '-', $string); // Replaces all spaces with hyphens.

        return strtolower(preg_replace('/[^A-Za-z0-9\-]/', '', $string)); // Removes special chars.
    }

    private function getAllowed()
    {
        return ['overview', 'eligibility', 'form', 'exam-centres', 'exam-pattern', 'syllabus', 'reference-books', 'results', 'cut-off', 'counselling', 'previous-years-papers'];
    }

    protected function checkExistingSubPageContent($examId, $dropValue, $parent_id)
    {
        return ExamContent::find()->select('id')
            ->where(['exam_id' => $examId])
            ->andWhere(['parent_id' => $parent_id])
            ->andWhere(['name' => $dropValue])
            ->andWhere(['IS NOT', 'parent_id', null])->one();
    }

    protected function checkExistingContent($data, $examId)
    {
        return ExamContent::find()->select('id')->where(['slug' => $data['ExamContent']['slug']])->andWhere(['exam_id' => $examId])->one();
    }

    protected function getValidationErrors($errors)
    {
        $validationErrros = [];
        foreach (array_keys($errors) as $error) {
            $validationErrros = array_merge($validationErrros, array_values($errors[$error]));
        }
        return reset($validationErrros);
    }

    /**
     * Displays a single BoardContent value for preview.
     * @param integer $id
     * @return mixed
     */
    public function actionPreview($id)
    {
        return $this->render('preview', [
            'model' => $this->findModel($id),
        ]);
    }

    /*Manage Menu Order*/
    public function actionManageMenuOrder()
    {

        $examData =  Yii::$app->request->get();
        $existingMenuOrder =  ManageNavMenuOrder::find()
            ->select(['menu_order'])
            ->where(['entity' => DataHelper::$manageMenuOrder['exam_content']])
            ->andWhere(['entity_id' => $examData['examId']])
            ->one();
        if (!empty($existingMenuOrder) && !empty(unserialize($existingMenuOrder->menu_order))) {
            $data = [];
            $menuOrder = unserialize($existingMenuOrder->menu_order);
            $i = 0;
            foreach ($menuOrder as $val) {
                $contentId = explode('--', $val);
                $contentData = ExamContent::find()
                    ->select(['slug', 'name', 'id', 'exam_id'])
                    ->where(['exam_id' => $examData['examId']])
                    ->andWhere(['parent_id' => null])
                    ->andWhere(['id' => $contentId[1]])
                    ->andWhere(['status' => ExamContent::STATUS_ACTIVE])
                    ->asArray()
                    ->one();
                $data[$i]['slug'] = $contentData['slug'];
                $data[$i]['name'] = $contentData['name'];
                $data[$i]['id'] = $contentData['id'];
                $data[$i]['exam_id'] = $contentData['exam_id'];
                $i++;
            }
        } else {
            $data = ExamContent::find()
                ->select(['slug', 'name', 'id', 'exam_id'])
                ->where(['exam_id' => $examData['examId']])
                ->andWhere(['parent_id' => null])
                ->andWhere(['status' => ExamContent::STATUS_ACTIVE])
                ->asArray()
                ->all();
        }
        return $this->render('manage-menu-order', [
            'data' => $data
        ]);
    }
}
