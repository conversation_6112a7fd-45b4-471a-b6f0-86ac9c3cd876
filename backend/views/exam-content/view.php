<?php

use common\helpers\DataHelper;
use common\models\ExamContent;
use common\models\User;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\ExamContent */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Exam Contents', 'url' => ['create', 'examId' => $model->exam_id]];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="exam-content-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                [
                    'attribute' => 'exam_id',
                    'label' => 'Exam',
                    'value' => $model->exam->name
                ],
                'name',
                'slug',
                [
                    'label' => 'Document details',
                    'format' => 'raw',
                    'value' => html_entity_decode($model->content),
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ExamContent::class), $model->status)
                ],
                [
                    'attribute' => 'created_by',
                    'label' => 'Created By',
                    'value' => function ($model) {
                        if (!empty($model->created_by)) {
                            $user =  User::find()->select('name')->where(['id' => $model->created_by])->one();
                            return $user->name ?? '';
                        } else {
                            return '';
                        }
                    },
                ],
                [
                    'label' => 'Parent',
                    'attribute' => 'parent_id',
                    'value' => function ($seoModel) {
                        if (!empty($seoModel->parent_id)) {
                            return ExamContent::find()
                                ->select('name')
                                ->where(['id' => $seoModel->parent_id])
                                ->one()->name ?? '';
                        }
                    }
                ],
                [
                    'attribute' => 'updated_by',
                    'label' => 'Updated By',
                    'value' => function ($model) {
                        if (!empty($model->updated_by)) {
                            $user =  User::find()->select('name')->where(['id' => $model->updated_by])->one();
                            return $user->name ?? '';
                        } else {
                            return '';
                        }
                    },
                ],
                'created_at:datetime',
                'updated_at:datetime',
                'published_at:datetime',
            ],
        ]) ?>
        <?= DetailView::widget([
            'model' => $seoModel,
            'attributes' => [
                'h1',
                'title',
                'description',
                [
                    'label' => 'Language Code',
                    'attribute' => 'lang_code',
                    'format' => 'html',
                    'value' => function ($seoModel) {
                        if (!empty($seoModel->lang_code)) {
                            return array_search($seoModel->lang_code, DataHelper::$languageCode);
                        }
                    }
                ],
            ],
        ]) ?>
    </div>
</div>