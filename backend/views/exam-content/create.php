<?php

use common\helpers\DataHelper;
use common\models\ExamContent;
use yii\bootstrap\ButtonDropdown;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $model common\models\ExamContent */

$this->title = 'Create Exam Content';
$this->params['breadcrumbs'][] = ['label' => 'Exam Contents', ['view', 'examId' => $model->exam_id]];
$this->params['breadcrumbs'][] = $this->title;
?>


<div class="box box-success collapsed-box">
    <div class="box-header with-border">
        <h3 class="box-title">Create Exam Content</h3>
        <div class="box-tools pull-right">
            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i></button>
        </div>
    </div>

    <div class="box-body">
        <?php echo $this->render('_form', [
            'model' => $model,
            'seoModel' => $seoModel,
        ]) ?>
    </div>
</div>

<div class="box box-primary">
    <div class="box-body table-responsive">
        <div class="box-header with-border">
            <?= Html::a('Manage Nav Menu Order', ['manage-menu-order', 'examId' => Yii::$app->request->get('examId')], ['class' => 'btn btn-success btn-flat']) ?>
        </div>
        <?php echo GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'options' => [
                'class' => 'grid-view table-responsive',
            ],
            'columns' => [
                'name',
                'slug',
                [
                    'attribute' => 'parentName',
                    'value' => function ($model) {
                        if (!empty($model->parent_id)) {
                            return $model->parent->name;
                        }
                        return '';
                    },
                    'label' => 'Parent'
                ],
                'author_id' => [
                    'attribute' => 'author_id',
                    'value' => function ($model) {
                        return $model->user->name ?? '';
                    },
                    'label' => 'Author Name'
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ExamContent::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', ExamContent::class),
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => '{preview} {update} {view}',
                    'buttons' => [
                        'preview' => function ($url) {
                            return Html::a('<span class="glyphicon glyphicon-check"></span>', $url, ['title' => 'Preview']);
                        },
                    ]
                ],
            ],
        ]); ?>
    </div>
</div>