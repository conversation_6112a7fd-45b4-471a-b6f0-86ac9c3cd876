<?php

use common\helpers\DataHelper;
use common\models\College;
use common\models\CutoffCategory;
use common\models\Exam;
use common\models\InstituteProgramRankMapping;
use common\models\Program;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\InstituteProgramRankMapping */
/* @var $form yii\widgets\ActiveForm */

$currentYear = 2023;
$yearRange = range($currentYear, $currentYear + 5);

// Check if there are any errors and render them at the top
if ($model->hasErrors()) {
    echo '<div class="alert alert-danger">';
    foreach ($model->errors as $error) {
        echo '<p>' . implode('</p><p>', $error) . '</p>';
    }
    echo '</div>';
}

?>
<style>
    .form-group.has-error .help-block {
        display: none;
    }
</style>
<div class="institute-program-rank-mapping-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-4">
            <?= $form->field($model, 'exam_id')->widget(Select2::classname(), [
                'data' => ArrayHelper::map(Exam::find()->select(['id', 'name'])->where(['id' => $model->exam_id])->all(), 'id', 'name') ?? [],
                'options' => [
                    'placeholder' => '--Select Exam--',
                    'multiple' => false,
                ],
                'disabled' => !$model->isNewRecord,
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 0,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/college-predictor-exam-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {query:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Exam Name');
?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'college_id')->widget(Select2::classname(), [
                'data' => ArrayHelper::map(College::find()->select(['id', 'name'])->where(['id' => $model->college_id])->all(), 'id', 'name') ?? [],
                'options' => [
                    'placeholder' => '--Select College--',
                    'multiple' => false,
                ],
                'disabled' => !$model->isNewRecord,
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/college-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('College Name');
?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'program_id')->widget(Select2::classname(), [
                'data' => ArrayHelper::map(Program::find()->select(['id', 'name'])->where(['id' => $model->program_id])->all(), 'id', 'name') ?? [],
                'options' => [
                    'placeholder' => '--Select Program--',
                    'multiple' => false,
                ],
                'disabled' => !$model->isNewRecord,
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/program-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {query:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) {return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Program');
?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'year')->dropDownList(array_combine($yearRange, $yearRange)) ?>
        </div>


        <div class="col-md-4">
            <?= $form->field($model, 'highest_rank')->textInput() ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'lowest_rank')->textInput() ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'round')->textInput() ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'cutoff_category_id')->widget(Select2::classname(), [
                'data' => ArrayHelper::map(CutoffCategory::find()->select(['id', 'name'])->where(['id' => $model->cutoff_category_id])->all(), 'id', 'name') ?? [],
                'options' => [
                    'placeholder' => '--Select Cutoff Category--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 1,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/cutoff-category-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {query:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) {return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Cutoff Category');
?>
        </div>

        <div class="col-md-12">
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', InstituteProgramRankMapping::class)) ?>
        </div>
        <div class="box-footer">
            <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
        </div>
        <?php ActiveForm::end(); ?>
    </div>