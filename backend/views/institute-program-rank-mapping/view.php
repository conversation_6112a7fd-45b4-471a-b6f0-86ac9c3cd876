<?php

use common\helpers\DataHelper;
use common\models\InstituteProgramRankMapping;
use frontend\helpers\Url;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\InstituteProgramRankMapping */

$this->title = $model->exam->name;
$this->params['breadcrumbs'][] = ['label' => 'Main Index', 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => 'Index', 'url' => ['index?InstituteProgramRankMappingSearch[college_id]=' . $model->college->name]];
$this->params['breadcrumbs'][] = ['label' => $model->college->name, 'url' => ['college/view', 'id' => $model->college_id]];
$this->params['breadcrumbs'][] = [
    'label' => $model->program->name,
    'url' => Url::to([
        'index',
        'InstituteProgramRankMappingSearch[college_id]' => $model->college->name,
        'InstituteProgramRankMappingSearch[program_id]' => $model->program->name
    ])
];
?>
<div class="institute-program-rank-mapping-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'college_id',
                    'value' => function ($model) {
                        return $model->college->name;
                    }
                ],
                [
                    'attribute' => 'program_id',
                    'value' => function ($model) {
                        return $model->program->name;
                    }
                ],
                'year',
                'highest_rank',
                'lowest_rank',
                'round',
                [
                    'attribute' => 'cutoff_category_id',
                    'value' => function ($model) {
                        return $model->cutoffCategory->name;
                    }
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', InstituteProgramRankMapping::class), $model->status);
                    }
                ]
            ],
        ]) ?>
    </div>
</div>