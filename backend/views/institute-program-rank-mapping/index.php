<?php

use common\helpers\DataHelper;
use common\models\InstituteProgramRankMapping;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\InstituteProgramRankMappingSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Institute Program Rank Mappings';
$this->params['breadcrumbs'][] = ['label' => 'Main Index', 'url' => ['index']];
$currentYear = 2023;
?>
<div class="institute-program-rank-mapping-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Institute Program Rank Mapping', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'exam_id',
                    'label' => 'Exam',
                    'value' => 'exam.name',
                ],
                [
                    'attribute' => 'college_id',
                    'label' => 'College',
                    'value' => 'college.name',
                ],
                [
                    'attribute' => 'program_id',
                    'label' => 'Program',
                    'value' => 'program.name',
                ],
                [
                    'attribute' => 'cutoff_category_id',
                    'label' => 'Cutoff Category',
                    'value' => 'cutoffCategory.name',
                ],
                [
                    'attribute' => 'year',
                    'label' => 'Year',
                    'value' => function ($model) {
                        return $model->year;
                    },
                    'filter' => array_combine(
                        $yearRange = range($currentYear, $currentYear + 5),
                        $yearRange
                    ),
                ],
                'round',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', InstituteProgramRankMapping::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', InstituteProgramRankMapping::class)
                ],

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>