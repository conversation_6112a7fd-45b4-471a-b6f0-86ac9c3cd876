<?php

use common\helpers\DataHelper;
use common\models\User;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\UserSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Users';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="user-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create User', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                // 'id',
                // 'entity',
                // 'entity_id',
                'name',
                'slug',
                'username',
                // 'contact_number',
                // 'auth_key',
                // 'password_hash',
                // 'password_reset_token',
                'email:email',
                // 'status',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', User::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', User::class)
                ],
                // 'created_at',
                // 'updated_at',
                // 'verification_token',

                [
                    'class' => 'yii\grid\ActionColumn', 'template' => '{update_profile} {view} {update}',
                    'buttons' => [
                        'update_profile' => function ($url) {
                            return Html::a('<span class="glyphicon glyphicon-user"></span>', $url, ['title' => 'User Profile Update']);
                        },
                    ],
                    'urlCreator' => function ($action, $model) {

                        if ($action === 'update_profile') {
                            $url = 'profile?slug=' . $model->slug;
                            return $url;
                        } elseif ($action === 'view') {
                            $url = 'view?id=' . $model->id;
                            return $url;
                        } elseif ($action === 'update') {
                            $url = 'update?id=' . $model->id;
                            return $url;
                        }
                    },
                    'visibleButtons' => [
                        'update_profile' => function () {
                            return Yii::$app->user->can('Webmaster');
                        }
                    ]

                ],
                [
                    'attribute' => 'Reset Password',
                    'format' => 'raw',
                    'value' => function ($model) {
                        $requestId = ($model->id);
                        $btnHtml = '';
                            $btnHtml .= Html::a('<span class="glyphicon glyphicon-refresh"></span>   Reset Password', ['user/reset-password', 'id' => $requestId], [
                                'class' => 'btn btn-danger',
                                'data' => [
                                    'confirm' => 'Are you sure you want to Reset Password of ' . $model->name . '?',
                                    'method' => 'post',
                                ]
                            ]);
                        return $btnHtml;
                    } ,
                    'visible' => true
                ]
            ],
        ]); ?>
    </div>
</div>