<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\SaCountry;
use common\models\SaCountryDetail;
use common\models\User;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\SaCountryDetail */
/* @var $form yii\widgets\ActiveForm */

$countryList = ArrayHelper::map(SaCountry::find()->where(['id' => $model->sa_country_id])->all(), 'id', 'name');
$disabled = false;

if (isset($_GET['country_id'])) {
    $selectedCountry = SaCountry::findOne($_GET['country_id']); // Fetch the country by ID
    if ($selectedCountry) {
        $countryList[$selectedCountry->id] = $selectedCountry->name;
        $model->sa_country_id = $selectedCountry->id; // Set the model's value
    }
}

if (isset($_GET['country_id']) || !$model->isNewRecord) {
    $disabled = true;
}

$model->description = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->description)));
$model->content = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->content)));
$model->related_article_h1 = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->related_article_h1)));

?>

<div class="sa-country-detail-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-6">
            <?= $form->field($model, 'sa_country_id')->widget(Select2::classname(), [
                'data' => $countryList ?? [],
                'options' => [
                    'placeholder' => '--Select--',
                    'disabled' => $disabled,
                    'options' => [],
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 2,
                    'maximumInputLength' => 10,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-country'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Country');
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'created_by')->dropDownList(
                ArrayHelper::map(User::find()->all(), 'id', 'name'),
                [
                    'options' => [
                        Yii::$app->user->identity->id => ['Selected' => true], // Set the selected value
                    ],
                    'disabled' => true // Disable the entire dropdown
                ]
            )->label('Author'); ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'title')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'h1')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'meta_title')->textInput(['maxlength' => true]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'meta_description')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-12">
            <?php
            if (!empty($model->cover_image)) {
                echo Html::img(\Yii::$aliases['@gmuAzureSaCollegeImage'] . '/' . $model->cover_image, ['width' => '50', 'height' => '50']);
            }
            ?>
            <?= $form->field($model, 'cover_image')->fileInput() ?>

            <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'type' => '',
                'entity' => 'description'
            ])
            ?>

            <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'type' => '',
                'entity' => 'content'
            ])
            ?>

            <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'type' => '',
                'entity' => 'related_article_h1'
            ])
            ?>

            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', SaCountryDetail::class)) ?>
        </div>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>