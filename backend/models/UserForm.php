<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use common\models\User;
use common\helpers\DataHelper;

/**
 * Signup form
 */
class UserForm extends Model
{
    public $id;
    public $name;
    public $entity;
    public $email;
    public $password;
    public $status;
    public $username;
    public $slug;
    public $contact_number;
    public $role;
    
    public $model;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['email'], 'trim'],
            [['email', 'name'], 'required'],
            [['contact_number'], 'safe'],
            ['email', 'email'],
            [['slug'], 'safe'],
            [['email', 'contact_number', 'name'], 'unique', 'targetClass' => User::class, 'filter' => function ($query) {
                if (!$this->getModel()->isNewRecord) {
                    $query->andWhere(['not', ['id' => $this->getModel()->id]]);
                }
            }, 'message' => 'This {attribute} has already been taken.'],
            ['password', 'required', 'on' => 'create'],
            ['password', 'string', 'min' => 6],

            ['status', 'in', 'range' => [User::STATUS_ACTIVE, User::STATUS_INACTIVE, User::STATUS_DELETED]],
        ];
    }

    /**
     * Signs user up.
     *
     * @return bool whether the creating new account was successful and email was sent
     */
    public function save()
    {
        if (!$this->validate()) {
            return null;
        }

        $slug = str_replace(' ', '-', strtolower($this->name));

        $user = $this->getModel();
        $user->username = $slug;
        $user->entity = 'admin-user';
        $user->name = $this->name;
        $user->slug = $slug;
        $user->email = $this->email;
        $user->status = $this->status;
        $user->contact_number = $this->contact_number;
        if ($this->password) {
            $user->setPassword($this->password);
        }
        $newPassword = DataHelper::getRandPwd(12);
        if ($this->password) {
            $newPassword = $this->password;
            $user->setPassword($this->password);
        } else {
            $user->setPassword($newPassword);
        }
        $user->generateAuthKey();
        $user->generateEmailVerificationToken();
        if ($user->save()) {
            Yii::$app->mailer
            ->compose()
            ->setFrom([Yii::$app->params['supportEmailUser'] => Yii::$app->name])
            ->setTo($user->email) //$user->email
            ->setSubject('Welcome')
            ->setHtmlBody("<p>Hello, $user->email</p>
        <p>Your account has been successfully created.</p><p>Your login details are as follows:</p>
        <p>Username: $user->username</p><p>Password: $newPassword</p>
        <p>Best regards,</p><p>getmyuni.com</p>")
            ->send();
            return true;
        } else {
            return false;
        }
    }


    /**
     * @return User
     */
    public function getModel()
    {
        if (!$this->model) {
            $this->model = new User();
        }
        return $this->model;
    }

    /**
     * @param User $model
     * @return mixed
     */
    public function setModel($model)
    {
        $this->username = $model->username;
        $this->name = $model->name;
        $this->slug = $model->slug;
        $this->email = $model->email;
        $this->status = $model->status;
        $this->contact_number = $model->contact_number;
        $this->model = $model;
        return $this;
    }

    /**
     * Sends confirmation email to user
     * @param User $user user model to with email should be send
     * @return bool whether the email was sent
     */
    protected function sendEmail($user)
    {
        return Yii::$app
            ->mailer
            ->compose(
                ['html' => 'emailVerify-html', 'text' => 'emailVerify-text'],
                ['user' => $user]
            )
            ->setFrom([Yii::$app->params['supportEmail'] => Yii::$app->name . ' robot'])
            ->setTo($this->email)
            ->setSubject('Account registration at ' . Yii::$app->name)
            ->send();
    }
    public function setNewPassword()
    {
        $updatedby = Yii::$app->user->identity->id;

        $user = $this->getModel();

        $newPassword = DataHelper::getRandPwd(12);
        if ($this->password) {
            $user->setPassword($this->password);
        } else {
            $user->setPassword($newPassword);
        }

        $user->generateAuthKey();
        $user->generateEmailVerificationToken();
        if ($user->save()) {
            $this->sendNewPasswordEmail($user, $newPassword);
            return true;
        }
    }

    protected function sendNewPasswordEmail($user, $newPassword)
    {
        if (!empty($user) && !empty($newPassword)) {
            $updatedbyEmail = Yii::$app->user->identity->email;

            return   Yii::$app->mailer
                ->compose()
                ->setFrom('<EMAIL>')
                ->setTo($user->email)
                // ->setCc($updatedbyEmail)
                ->setSubject('Reset Password for GMU backend ')
                ->setHtmlBody('<p>Hello, ' . (($user->name . ' (' . $user->email . ') ') ?? $user->email) . "</p>
                        <p>Your account has been successfully created.</p><p>Your login details are as follows:</p>
                        <p>Username: $user->username</p><p>Password: $newPassword</p>
                        <p><a href =https://backend.getmyuni.com/>Backend Link </p>
                        <p>Best regards,</p><p>IELTSMaterial.com</p>")
                ->send();
        } else {
            return false;
        }
    }
}
