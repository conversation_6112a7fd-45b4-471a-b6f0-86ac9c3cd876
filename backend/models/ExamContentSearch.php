<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\ExamContent;

/**
 * ExamContentSearch represents the model behind the search form of `common\models\ExamContent`.
 */
class ExamContentSearch extends ExamContent
{
    public $examname;
    public $author_id;
    public $updated_by;
    public $parentName;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'exam_id', 'status', 'created_at', 'updated_at'], 'integer'],
            [['name', 'slug', 'content', 'examname', 'created_at', 'updated_at', 'published_at', 'author_id', 'updated_by', 'parentName'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params, $page = 'index')
    {
        $query = ExamContent::find();

        $query->joinWith(['exam', 'user', 'parent']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            // 'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $dataProvider->sort->attributes['examname'] = [
            'asc' => ['exam.name' => SORT_ASC],
            'desc' => ['exam.name' => SORT_DESC]
        ];
        
        $this->load($params);
        // if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            // return $dataProvider;
        // }

        // grid filtering conditions
        $query->andFilterWhere([
            'exam_content.id' => $this->id,
           'exam_content.exam_id' => $this->exam_id,
            'exam_content.status' => $this->status,
        ]);

        $query->andFilterWhere(['like', 'exam_content.name', $this->name])
            // ->andFilterWhere(['like', 'exam_content.slug', $this->slug])
            ->andFilterWhere(['like', 'user.name', $this->author_id])
            ->andFilterWhere(['like', 'user.name', $this->updated_by])
            ->andFilterWhere(['like', 'parent.name', $this->parentName])
            ->andFilterWhere(['like', 'exam.display_name', $this->examname]);
        if ($page == 'create') {
            $query->andFilterWhere(['like', 'exam_content.slug', $this->slug]);
        } else {
            $query->andFilterWhere(['like', 'exam.slug', $this->slug]);
        }

        $query->andFilterWhere(['DATE(exam_content.published_at)' => $this->published_at])
        ->andFilterWhere(['DATE(exam_content.updated_at)' => $this->updated_at])
        ->andFilterWhere(['DATE(exam_content.created_at)' => $this->created_at]);

        return $dataProvider;
    }
}
