<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\InstituteProgramRankMapping;

/**
 * InstituteProgramRankMappingSearch represents the model behind the search form of `common\models\InstituteProgramRankMapping`.
 */
class InstituteProgramRankMappingSearch extends InstituteProgramRankMapping
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'highest_rank', 'lowest_rank', 'round', 'cutoff_category_id', 'status', 'year'], 'integer'],
            [['created_at', 'updated_at', 'college_id', 'program_id', 'college_type', 'exam_id'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = InstituteProgramRankMapping::find();
        $query->joinWith(['college', 'program', 'cutoffCategory', 'exam']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'institute_program_rank_mapping.id' => $this->id,
            'highest_rank' => $this->highest_rank,
            'lowest_rank' => $this->lowest_rank,
            'round' => $this->round,
            'institute_program_rank_mapping.status' => $this->status,
            'year' => $this->year,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['college.name' => $this->college_id]);
        $query->andFilterWhere(['program.name' => $this->program_id]);
        $query->andFilterWhere(['cutoff_category.name' => $this->cutoff_category_id]);
        $query->andFilterWhere(['exam.name' => $this->exam_id]);

        return $dataProvider;
    }
}
