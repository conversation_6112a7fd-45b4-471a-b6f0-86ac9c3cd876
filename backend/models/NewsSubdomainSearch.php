<?php

namespace backend\models;

use common\models\NewsContentSubdomain;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\NewsSubdomain;

/**
 * NewsSearch represents the model behind the search form of `common\models\News`.
 */
class NewsSubdomainSearch extends NewsSubdomain
{
    public $category;
    public $tags;
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'is_popular', 'position', 'status', 'is_live', 'lang_code'], 'integer'],
            [['name', 'display_name', 'slug', 'category', 'tags', 'expired_at', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        // $query->orderBy(['news_content_subdomain.updated_at' => SORT_DESC]);

        $query = NewsSubdomain::find();
        $query->joinWith([
            'category',
            'tags',
            'newsContent' => function ($q) {
                $q->from(['news_content_subdomain' => NewsContentSubdomain::tableName()]);
            }
        ]);

        $query->orderBy(new \yii\db\Expression('COALESCE(news_content_subdomain.updated_at, news_subdomain.updated_at) DESC'));

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'news_subdomain.is_popular' => $this->is_popular,
            'news_subdomain.is_live' => $this->is_live,
            'news_subdomain.position' => $this->position,
            'news_subdomain.status' => $this->status,
        ]);

        $query->andFilterWhere(['like', 'news_subdomain.name', $this->name])
            ->andFilterWhere(['like', 'news_subdomain.display_name', $this->display_name])
            ->andFilterWhere(['like', 'tags.name', $this->tags])
            ->andFilterWhere(['like', 'news_subdomain.name', $this->category])
            ->andFilterWhere(['like', 'news_subdomain.created_at', $this->created_at])
            ->andFilterWhere(['like', 'news_subdomain.slug', $this->slug])
            ->andFilterWhere(['like', 'lang_code', $this->lang_code]);

        return $dataProvider;
    }
}
