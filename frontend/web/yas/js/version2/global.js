var mobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

jQuery(document).ready(function ($) {
    var pathname = window.location.pathname.split('/');
    var countryArray = ["canada", "usa", "uk", "germany", "australia"];

    if ($.inArray(pathname[1], countryArray) != -1) {
        $.get('/site/sa-logged-in', function (data) {
            if (data.data.name) {
                var userName = truncateName(data.data.name, 20);
                $("#registerNew").css("display", "none");
                $('#loggedInUser').replaceWith('<li class="sa_dropdown registerNew"><a class="registerWelcomeMsg">Hi ' + userName + '<span class="caretWhite spriteIcon"></span></a><ul class="login-options"><span class="spriteIcon whiteCaretIcon"></span><a href="/user-profile"><li style="display: block;"><span class="spriteIcon myProfileIcon"></span>My Profile</li></a><a href="/site/sa-logout" title="Logout" data-method="post"><li><span class="spriteIcon logoutIcon"></span>Logout</li></a></ul></li>');
                $('.user-text-mobile').html('Hi ' + userName + '');
                $('#mobileRegisterNew').hide();
                $('#mobileLogout').html('<div class="hamburgerMenuOptions"><a href="/user-profile" class="myProfileOption"><div style="display: block"><span class="spriteIcon myProfileIcon"></span>My Profile</div></a><a  onclick="javascript:logout()" title="Logout" class="logOutOption"><div><span class="spriteIcon logoutIcon"></span>Log Out</div></a></div>');
                gmu.config.isLoggedIn = true;
            }
        });
    } else {
        $.get('/site/logged-in', function (data) {
            if (data.data.name) {
                var userName = truncateName(data.data.name, 20);
                $("#registerNew").css("display", "none");
                $('#loggedInUser').replaceWith('<li class="sa_dropdown registerNew"><a class="registerWelcomeMsg">Hi ' + userName + '<span class="caretWhite spriteIcon"></span></a><ul class="login-options"><span class="spriteIcon whiteCaretIcon"></span><a href="/user-profile"><li style="display: block;"><span class="spriteIcon myProfileIcon"></span>My Profile</li></a><a href="/site/logout" title="Logout" data-method="post"><li><span class="spriteIcon logoutIcon"></span>Logout</li></a></ul></li>');
                $('.user-text-mobile').html('Hi ' + userName + '');
                $('#mobileRegisterNew').hide();
                $('#mobileLogout').html('<div class="hamburgerMenuOptions"><a href="/user-profile" class="myProfileOption"><div style="display: block"><span class="spriteIcon myProfileIcon"></span>My Profile</div></a><a  onclick="javascript:logout()" title="Logout" class="logOutOption"><div><span class="spriteIcon logoutIcon"></span>Log Out</div></a></div>');
                gmu.config.isLoggedIn = true;
            }
        });
    }
});

//truncate the name
function truncateName(name, maxLength) {
    if (name.length <= maxLength) {
        return name;
    } else {
        return name.slice(0, maxLength) + '...';
    }
}

//loaderscript
function removePreLoader() {
    $(".pageLoader").fadeOut(500, function () {
        $(".pageLoader").hide();
    });
}

$("body").on("change", ".examBooking", function () {
    $(this).css("color", "#282828;")

})

/*
function moveLabelIntoFlatPickr() {
    document.querySelectorAll('.examDateField').forEach((item) => {
        if (item.querySelector('.flatpickr-wrapper').childElementCount === 2) {
            item.querySelector('.flatpickr-wrapper').appendChild(item.lastElementChild)
        }
    })
}
*/

// download assets and redirect dynamic url after lead form submit
function dynamicPdfUrlRedirect(uri) {
    var a = document.createElement('a');
    a.setAttribute('href', uri);
    a.setAttribute('target', '_blank');
    a.click();
    a.remove();
}

$('#filter-loader').hide();


/** CTA onclick getFormUrl script starts */

// $('body').on('click', '.loadLeadModelNew', function (event) {
//     event.preventDefault();
//     $('#leadForm').show();
// });

/** CTA onclick getFormUrl script ends */

/**secondary Navigation */
const secNav = localStorage.getItem('gmuSecondary1');
if (secNav == null) {
    gmu.config.language;
    $.get('/' + gmu.config.language + '/get-secondary-navigation', {}, function (secondaryNav) {

        localStorage.setItem('gmuSecondary', secondaryNav);
        $("#secondaryHeader-web").html(secondaryNav);
    });
} else {
    $("#secondaryHeader-web").html(secNav);
}

$("body .navigation_header_dropdown").prepend("<p class='greyBg'></p>");
$("body .navigation_header_dropdown a").mouseenter(function () {
    $("p.greyBg").css('display', 'block');
    // $("body").addClass("no-scroll");
});

$("body p.greyBg, .topHeader, .headerMegaMenu ul li:first-child a").mouseenter(function () {
    $("p.greyBg").css('display', 'none');
    $("body").removeClass("no-scroll");

});


/** URL Helper starts */

gmu.url = (function () {
    pub = {
        goto: function (href) {
            window.location.href = href;
        },

        gotoWindow: function (href) {
            window.open(href, "_blank");
        }
    }
    return pub;
})(jQuery);

async function showMoreContent() {
    var pageInfoText = $('.pageInfo').text();
    var wordsCount = $.trim(pageInfoText.replace(/[\t\n]+/g, ' ')).length;
    if (wordsCount > 250) {
        $('.pageInfo').after('<p class="readMoreInfo">Show More</p>');
        $('.pageInfo').css('margin-bottom', '0px');
    } else {
        $('.pageInfo').css({ 'margin-bottom': '20px', 'max-height': '100%' });
    }
    $(".readMoreInfo").wrap("<div class='readMoreDiv'></div>");
    $('.readMoreInfo').click(function () {
        $(this).parent().prev().toggleClass("pageInfo");
        contentBlock.toggleClass("expanded");
        var text = $(this).text() == 'Show Less' ? 'Show More' : 'Show Less';
        $(this).text(text);

        var infoOnTop = $(this).parent().prev('.pageData, .showMoreScroll, .pageDataSection');
        //for exam and scholarship showMore/showLess
        if (infoOnTop.length === 0) {
            $("html, body").animate({
                scrollTop: ($(".readMoreInfo").offset().top - $(".pageInfo").outerHeight()) - 100,
            });
        } else if (infoOnTop.hasClass("pageInfo")) {
            console.log('catch');
            $('body,html').animate({
                scrollTop: infoOnTop.offset().top - 70
            }, 20);
        } else {
            return false;
        }

        return false;
    });
}

showMoreContent();

$('body').on('keyup', '.search-autocomplete', function (e) {

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    if (gmu.config.prodEvn == 'prod') {
        link.href = 'https://www.getmyuni.com/yas/css/version2/min/search.css';
    } else {
        link.href = 'https://www.getmyuni.com/yas/css/version2/search.css';
    }
    document.head.appendChild(link);

    var type = $(this).data('type');
    var id = this.id;
    searches('#' + id, type);
});

function searches(autocompleteElement, type) {
    $(autocompleteElement).autocomplete({
        appendTo: '.selection',
        minLength: 2,
        source: function (query, done) {
            $.ajax({
                type: "GET",
                url: "/global-search/search",
                data: { q: query['term'], type: type },
                dataType: "json",
                success: function (response) {
                    response = JSON.parse(response);
                    if (!response.length) {
                        var result = [
                            {
                                label: 'No result found',
                                value: '',
                                group: ''
                            }
                        ];
                        done(result);
                    } else {
                        done(response);
                    }
                },
            });
        },
        select: function (event, ui) {
            event.preventDefault();
            if (ui.item.label == "No result found") {
                return false;
            }
            window.location.href = ui.item.url;
        },
        open: function (event, ui) {
            $("body .ui-autocomplete.ui-front.ui-menu.ui-widget.ui-widget-content").addClass('category');
            $('body .selection').css('display', 'block')
        },
        close: function () {
            $("html, body").css({ overflow: 'inherit' });
        },

    }).autocomplete("instance")._renderItem = function (ul, item) {
        return $("<li>")
            .append(" <span'> "
                + item.label + "</span>")
            .appendTo(ul);
    };
}

function setResendInterval() {
    var timer = 30;
    $("#resendOtp").hide();
    $("#resendOtpUser").hide();
    var handle = setInterval(function () {
        $("#resendTimer").html("Resend in <span>" + timer + "</span> Sec");
        timer--;

        if (timer == -1) {
            resend = true;
            $("#resendTimer").html("");
            $("#resendOtp").show();
            $("#resendOtpUser").show();
            clearInterval(handle);
        }
    }, 1000)
}

function showLeadResponse(leadId) {
    //check for clickedreview
    if (localStorage.getItem('reviewReadMoreId')) {
        localStorage.setItem('loggedIn', "Yes");
    }
    // $(leadId)[0].reset();
    $(".thankYouText").html("Thank you for your response.").parent().show();
    if (($('#leadform-durl').length) != 0 || ($('#leadform-dynamic_redirection').length) != 0) {
        if ($("#leadform-durl").val() != '') {
            dynamicPdfUrlRedirect($("#leadform-durl").val());
            getScrollPosition()
        }

        if ($("#leadform-dynamic_redirection").val() != '') {
            dynamicPdfUrlRedirect($("#leadform-dynamic_redirection").val());
        }
    }

    if (($('#gmusaleads-durl').length) != 0 || ($('#gmusaleads-dynamic_redirection').length) != 0) {
        if ($("#gmusaleads-durl").val() != '') {
            dynamicPdfUrlRedirect($("#gmusaleads-durl").val());
        }

        if ($("#gmusaleads-dynamic_redirection").val() != '') {
            dynamicPdfUrlRedirect($("#gmusaleads-dynamic_redirection").val());
        }
    }
}

//open lead form for download link
if (gmu.config.isLoggedIn == false) {
    $('body').on("click", '.downloadLead', function (e) {
        e.preventDefault();
        redirectionUrl = $(this).attr('href');
        $('.js-open-lead-form-new').trigger('click');
        $(".headingText").html("Fill in the details to download now");
        $(".subHeadingText").html("Get access to premium content and updates");
        document.querySelector('#cta_location').value = gmu.config.entity + "_download_link_cta";
        document.querySelector('#cta_text').value = gmu.config.entity + "_download_button";
        document.querySelector("#durl").value = redirectionUrl ?? '';

    });

    //open lead form for download links
    $('body').on('click', 'a[href$=".pdf"], a[href^="https://drive.google.com/"]', function (event) {
        event.preventDefault();
        var url = $(this).attr('href');
        var leadForm = $('.js-open-lead-form-new');
        leadForm.each(function (index, button) {
            var onclickURL = $(button).attr('onclick');
            if (onclickURL) {
                localStorage.setItem('onClickUrl', onclickURL);
            }
            $(this).removeAttr('onclick');
        });

        if (!leadForm.hasClass('click-triggered')) {
            leadForm.trigger('click');
        } else {
            leadForm.removeClass('click-triggered'); // Remove the 'click-triggered' class
            leadForm.trigger('click');
        }

        $(".textDivHeading").html("Register to download PDF now");
        $(".subHeadingText").html("Get access to premium content and updates");

        $('#cta_location').val(gmu.config.entity + "_download_link_cta");
        $('#cta_text').val(gmu.config.entity + "_download_button");
        $('#durl').val(url || '');
    });
}

//local storage of pageOffset
function getScrollPosition() {
    if (localStorage.getItem('scrollPosition')) {
        window.scrollTo(0, localStorage.getItem('scrollPosition'));
    }
    removeScrollPosition();
}

//set leadform pop up position
function setScrollPosition() {
    localStorage.setItem('scrollPosition', window.pageYOffset);
}

//remove popup position
function removeScrollPosition() {
    if (localStorage.getItem('scrollPosition')) {
        localStorage.removeItem('scrollPosition');
    }
}

//     $("select#leadform-interested_course").css("color", "#282828");
// });
// $("body").on("change", ".field-leadform-qualification", function () {
//     $("select#leadform-qualification").css("color", "#282828");
// });

// //close lead form
// $("body").on('click', '.closeLeadFormContainer, .closeLeadForm', function () {
//     $('form').on('reset', function () {
//         $("input[type='hidden']", $(this)).each(function () {
//             var $formValues = $(this);
//             $formValues.val($formValues.data('defaultvalue'));
//         });
//     });
//     $('.subscribeSectionNews, .thankYouMsgNews, .leadFormContainer, .leadFormContainerNews').attr('style', 'display: none !important');
//     $(".headingText").html("");
//     $(".pageMask").css("display", "none");
//     $("body").css("overflowY", "unset");
//     $(".headerCTAPair").css("z-index", 1);
//     $("#interested_lead_course").val('').trigger('change');
//     $("#leadform-college-course-exam").val('');
//     $("#leadform-qualification").css("color", "#989898");
//     $('option:selected', "#leadform-qualification").removeAttr('selected');
//     $('.help-block').html('');
//     // location.reload();
//     document.querySelector('body').style.position = 'unset';
//     // document.querySelector('body').style.top = 'unset';

//     getScrollPosition();
//     document.querySelector('body').style.bottom = 'unset';
//     document.querySelector('body').style.right = 'unset';
//     document.querySelector('body').style.left = 'unset';

//     //check to loggedin Through review readMore
//     if ((localStorage.getItem('loggedIn') === 'Yes')) {
//         localStorage.setItem('singupClicked', "Yes");
//         scrollToClickedReview();
//     }
// });

// article
$(".replyTxt").click(function () {
    $(".comment-response").html("");
    document.querySelector("#comment-entity").value = this.dataset.entity;
    document.querySelector("#comment-entity_id").value = this.dataset.entityid;
    document.querySelector("#comment-parent_id").value = this.dataset.parentid;

    $(".write-comment").fadeIn();
    $("#replyForm").find("input[type=text], textarea").val("");
    $("#replyForm").fadeIn();
});

$('body').on('submit', '#comment-form', function (event) {
    event.preventDefault();
    submitComment("#comment-form")
});
$('body').on('submit', '#replyForm', function (event) {
    event.preventDefault();
    submitComment("#replyForm")
});

// comment form popup scrip
$('body').on('click', '.closeForm', function () {
    $(".write-comment.replyFormJs").fadeOut();

});

$(".commentByOrganization").each(function () {
    if ($(this).children().length == 0) {
        $(this).css({ 'display': "none" });
    }
});

function submitComment(id) {
    var $commentForm = $(id);

    $.post($commentForm.attr('action'), $commentForm.serialize(), function (response) {
        $(id).find('.has-error').removeClass('has-error');

        if (response.success) {
            $(id).fadeOut();
            $(id).siblings(".comment-response").append("<p>Thank you for your comment.</p>")
        } else {
            let errors = response.errors;

            $.each(errors, function (index, value) {
                var $selector = $('#' + index);
                $selector.parent().addClass('has-error');
            });
        }
    }, 'json');
}

function getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(';');

    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1);

        if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
        }
    }
    return "";
}

function setCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    var expires = "expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + "; " + expires;
}

// wp-news comment
$("body").on("submit", "#wpCommentForm", function (e) {
    e.preventDefault();
    $form = $(this);

    $.post($form.attr('action'), $form.serialize(),
        function (data, textStatus, jqXHR) {
            if (data.status) {
                $("#wpCommentForm").hide();
                $(".comment-response").html(data.message);
            } else {
                $
                $(".comment-response").html(data.message);
            }
        },
        "JSON"
    );
});

//unset all the style when esc key is pressed, after opening lead form
$(document).keydown(function (e) {
    if (e.keyCode == 27) {
        $('.leadFormContainer').attr('style', 'display: none !important');
        $(".headingText").html("");
        $(".pageMask").css("display", "none");
        $("body").css("overflowY", "unset");
        location.reload(true);
    };
});

$.get('/comment-reply-form', {}, function (replyFormJs) {
    $("#comment-reply-form-js").html(replyFormJs);
});

//skip the the 3rd screen in lead form
// $('body').on('click', '.skip-colleges', function (e) {
//     $(".engagementPanel").hide();
//     showLeadResponse('#lead-form');
// });

// leadForm 3rd screen Js starts
// $('body').on('click', '.sponser-colleges', function (e) {
//     var $leadFormSponser = $("#lead-form");

//     $.post($leadFormSponser.attr('action'), $leadFormSponser.serialize(), function (response) {
//         if (response.success) {
//             $("#leadFormInputs").hide();
//             $(".engagementPanel").hide();
//             showLeadResponse('#lead-form');
//         }
//     }, 'json');
// });

// study abroad lead form changes
var pathname = window.location.pathname.split('/');
var countryArray = ["canada", "usa", "uk", "germany", "australia"];

function validateMobileNumber(mobileNumber) {
    const mobile = /^[6-9]\d{9}$/;
    return mobile.test(mobileNumber);
}

// if ($.inArray(pathname[1], countryArray) != -1) {
//     $.get('/sa-lead-form', {}, function (leadFormContainer) {
//         $("#sa-lead-form-js").html(leadFormContainer);
//     });

//     $('body').on('click', '.submit-sa-lead-form', function (event) {
//         event.preventDefault();

//         if ($('#gmusaleads-full_name').val() != '' && $('#gmusaleads-mobile_num').val() != '' && $('#gmusaleads-email_id').val() != '' && $('#gmusaleads-current_country_id').val() != '' && $('#gmusaleads-current_city_id').val() != '' && $('#gmusaleads-study_destination').val() != '' && $('#gmusaleads-planning_duration').val() != '' && $('#gmusaleads-intrestred_degree').val() != '') {
//             $('.submit-sa-lead-form').attr('disabled', true);
//         } else {
//             $('#submit-sa-lead-form').attr('disabled', false);
//         }

//         var $leadForm = $('#sa-lead-form');

//         $.post($leadForm.attr('action'), $leadForm.serialize(), function (response) {
//             $('#sa-lead-form').find('.error').removeClass('error');
//             if (response.success) {
//                 if (validateMobileNumber(response.model.mobile_num) == false) {
//                     $("#leadFormInputs").hide();
//                     showLeadResponse('#sa-lead-form');
//                 } else {
//                     if (response.otp) {
//                         $("#leadFormInputs").hide();
//                         $("#leadUserMobile").html($("#gmusaleads-mobile_num").val());
//                         $(".optSection .headingText").html("VERIFY MOBILE NUMBER");
//                         if (response.model) {
//                             $(".engagementPanel").html(response.model);
//                         }
//                         $(".optSection").show();
//                     } else {
//                         $("#leadFormInputs").hide();
//                         showLeadResponse('#sa-lead-form');
//                     }
//                 }
//             } else {
//                 let errors = response.errors;
//                 $.each(errors, function (index, value) {
//                     var $selector = $('#' + index);
//                     if ($selector.data('select2-id')) {
//                         let id = $selector.data('select2-id');
//                         id = id.replace('-data', '');
//                         $('#' + id + '-container').addClass('error');
//                     }
//                     $selector.addClass('error');
//                 });
//             }
//         }, 'json');
//     });

//     $('body').on('click', '.sa-verifyOtp', function (event) {
//         event.preventDefault();
//         var validForm = true;
//         var otpResponseText = $("#otpResponseText").html("");

//         $.each($(".numberInputs input[name='digit[]']"), function (index, value) {
//             if ($(this).val() == "") {
//                 validForm = false;
//             }
//         });

//         if (validForm == false) {
//             otpResponseText.html("Invalid otp entered");
//             return false;
//         }

//         $.post('/ajax/sa-verify-otp', $('#sa-lead-form').serialize(), function (response) {
//             if (response.success) {
//                 $(".optSection").hide();
//                 showLeadResponse('#sa-lead-form');
//             } else {
//                 otpResponseText.html(response.message);
//             }
//         })
//     });

//     var resend = true;
//     $("body").on('click', '#sa-resendOtp', function (e) {
//         e.preventDefault();
//         if (!resend) {
//             return false;
//         }
//         $.post('/ajax/sa-resend-otp', $("#sa-lead-form").serialize(), function (response) {
//             if (response.success) {
//                 resend = false;
//                 setResendInterval();
//             }
//         })
//     })

//     // $('body').on('click', '.sa-js-open-lead-form', function (e) {
//     //     // $(".pageMask").css("display", "block");
//     //     $("body").css("overflowY", "hidden");
//     //     $(".submit-sa-lead-form").attr("disabled", false);
//     //     // document.querySelector('body').style.position = 'fixed';
//     //     // document.querySelector('body').style.top = '0';
//     //     // document.querySelector('body').style.bottom = '0';
//     //     // document.querySelector('body').style.right = '0';
//     //     // document.querySelector('body').style.left = '0';
//     //     var leadFormTitle = this.dataset.leadformtitle;
//     //     var url = document.querySelectorAll('meta[property="og:url"]')[0].content;
//     //     document.querySelector('#gmusaleads-sa_clg_name').value = this.dataset.entity ?? 'study-abroad-article';
//     //     document.querySelector('#gmusaleads-sa_clg_id').value = this.dataset.entityid ?? null;
//     //     document.querySelector('#gmusaleads-click_source').value = this.dataset.ctalocation;
//     //     document.querySelector("#gmusaleads-durl").value = this.dataset.durl ?? ''
//     //     document.querySelector("#gmusaleads-dynamic_redirection").value = this.dataset.dynamic_redirection ?? ''
//     //     document.querySelector('#gmusaleads-url').value = url;

//     //     if (this.dataset.subheadingtext) {
//     //         $('.subHeadingText').html(this.dataset.subheadingtext);
//     //     } else {
//     //         $('.subHeadingText').html('Get details and latest updates');
//     //     }

//     //     if (leadFormTitle) {
//     //         $('.headingText').append(this.dataset.leadformtitle);
//     //     } else {
//     //         $('.headingText').append('REGISTER NOW TO APPLY');
//     //     }

//     //     $("#leadFormInputs").show();
//     //     $(".optSection").hide();
//     //     $(".engagementPanel").hide();
//     //     $(".thankYouMsg").hide();
//     //     $(".leadFormContainer").show();
//     // });

//     // // set sa-leadform cookie
//     // if (getCookie('gmu_gmusaleads') != '1' && gmu.config.showLeadForm && gmu.config.show_lead_form != 1) {
//     //     setTimeout(function () {
//     //         if ($(".headingText").html() == "") {
//     //             $(".headingText").html("REGISTER NOW TO APPLY");
//     //             $('.subHeadingText').html('Get details and latest updates');
//     //         }
//     //         var url = document.querySelectorAll('meta[property="og:url"]')[0].content;
//     //         document.querySelector('#gmusaleads-url').value = url;
//     //         document.querySelector('#gmusaleads-sa_clg_name').value = gmu.config.entity;
//     //         document.querySelector('#gmusaleads-sa_clg_id').value = gmu.config.entity_id;
//     //         document.querySelector('#gmusaleads-click_source').value = gmu.config.cta_location;

//     //         $(".leadFormContainer").fadeIn();
//     //     }, 15000);
//     // }

//     // $("body").on("click", ".closeLeadForm", function () {
//     //     $('#sa-lead-form').trigger("reset");
//     //     $("body").css("overflowY", "unset");
//     //     $(".headingText").html("");
//     //     $(".pageMask").css("display", "none");
//     //     $('.help-block').html('');
//     //     setCookie('gmu_gmusaleads', 1, 10);
//     //     $(".leadFormContainer").fadeOut();
//     // });

//     // $('body').on('change', '#gmusaleads-current_country_id', function (e) {
//     //     var countryName = this.value;
//     //     $.ajax({
//     //         url: "/ajax/sa-city",
//     //         type: "POST",
//     //         data: 'current_country=' + countryName,
//     //         dataType: "json",
//     //         success: function (jsonData) {
//     //             $("body #gmusaleads-current_city_id").html('');
//     //             $("body #gmusaleads-current_city_id").select2({
//     //                 data: jsonData,
//     //                 placeholder: {
//     //                     id: "-1",
//     //                     text: '--- Please select your city ---',
//     //                     selected: 'selected'
//     //                 }
//     //             });
//     //         }
//     //     });
//     // });
// }

$(document).ready(function () {
    var showForm = $(".leadFormContainer").css("style", "block");
    if (!showForm) {
        $("body").addClass("no-scroll");
    } else {
        $("body").removeClass("no-scroll");
    }

    rankPredictorMaxMarks();

    $('#rankPredictStartOver').click(function () {
        location.reload();
    });

    var rankPredictor = localStorage.getItem('rankPredictorResult');
    if (rankPredictor) {
        $('#rankPredictorLandingForm').hide();
        $('#rankPredictorResult').show();
        $('#rankPredictResult').html(rankPredictor);
        localStorage.removeItem('rankPredictorResult');
    }
    localStorage.removeItem('rankPredictorResult');

});

function rankPredictorMaxMarks() {
    var slug = /[^/]*$/.exec($(location).attr('pathname'))[0];
    var maximumMarks = {
        'kmat-percentile-predictor': 120,
        'snap-percentile-predictor': 60,
        'mat-percentile-predictor': 801,
        'nmat-percentile-predictor': 360,
        'uceed-rank-predictor': 300,
        'jee-main-rank-predictor': 350,
        'cmat-percentile-predictor': 400,
        'cat-percentile-predictor': 235,
        'ipu-cet-rank-predictor': 400,
        'kcet-rank-predictor': 180,
        'neet-rank-predictor': 720,
        'mh-cet-law-percentile-predictor': 200,
        'nid-rank-predictor': 100,
        'clat-rank-predictor': 165,
        'comedk-uget-rank-predictor': 180,
        'ap-eamcet-rank-predictor': 160,
        'apicet-rank-predictor': 200,
        'ts-icet-rank-predictor': 200,
        'mht-cet-percentile-predictor': 200,
        'mht-cet-rank-predictor': 200
    }
    var minMarks = {
        'kmat-percentile-predictor': 30,
        'snap-percentile-predictor': 3,
        'mat-percentile-predictor': 200,
        'nmat-percentile-predictor': 36,
        'mht-cet-percentile-predictor': 100,
        'mht-cet-rank-predictor': 100
    }
    if (maximumMarks[slug]) {
        $("#rankPredictormarks").attr({ "max": maximumMarks[slug] });
    }
    if (minMarks[slug]) {
        $("#rankPredictormarks").attr({ "min": minMarks[slug] });
    }
}
// ends

function shareViaGmail(link) {
    let url = 'https://mail.google.com/mail/?view=cm&fs=1&tf=1&to=&su=Share+Link+&body=' + link + '&ui=2&tf=1&pli=1';
    window.open(url, 'sharer', 'toolbar=0,status=0,width=648,height=395');
}
function copyToClipboard(link) {
    var $temp = $("<input>");
    document.getElementById("custom-tooltip").style.display = "inline";
    $("body").append($temp);
    $temp.val(link).select();
    document.execCommand("copy");
    setTimeout(function () {
        document.getElementById("custom-tooltip").style.display = "none";
    }, 1000);

    $temp.remove();
}

//data chart
if (gmu.config.courseChartData !== undefined && gmu.config.courseChartData.length !== 0) {
    jQuery(document).ready(function ($) {
        var result = $.map(gmu.config.courseChartData, function (data) {
            return data.collegeCount
        });

        var ctx = document.getElementById("chartDiv").getContext('2d');
        var chartDiv = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ["0-1 Lakh", "1-2 lakh", "2-3 Lakh", "3-5 Lakh", "> 5 Lakh"],
                datasets: [{
                    label: 'College Count',
                    data: result,
                    backgroundColor: "rgb(61,143,242)"
                }],
            },
            options: {
                scales: {
                    xAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Fees',
                            fontSize: 16,
                        }
                    }],
                    yAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'No of Colleges',
                            fontSize: 16,
                        }
                    }]
                }
            }
        });
    });
}

/** Global search start here */
var showChar = "Type 3 or more characters for search results";
$(document).ready(function () {

    //iphone 
    if (navigator.userAgent.match(/(iPod|iPhone|iPad)/)) {
        // Only active input fields in use
        $('input, textarea').on('focus', function () {
            $('input, textarea').not(this).attr("readonly", "readonly");
        });
        $('input, textarea').on('blur', function () {
            $('input, textarea').removeAttr("readonly");
        });

        // Disable tabing to select box's
        $('select').attr('tabindex', '-1');
    }

    //popup for elastic search
    $("body").on("click", ".searchIcon", function () {

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        if (gmu.config.prodEvn == 'prod') {
            link.href = 'https://www.getmyuni.com/yas/css/version2/min/search.css';
        } else {
            link.href = 'https://www.getmyuni.com/yas/css/version2/search.css';
        }
        document.head.appendChild(link);

        $("body .zsiq_floatmain").addClass('overrideZindex');
        $(".advanceSearch").fadeIn();
        $('meta[name=viewport]').attr('content', 'width=device-width, minimum-scale=1.0, maximum-scale=0.5, initial-scale=0.5');
        $("body .seachInput").focus();
        $("body").css('overflow', 'hidden');
        $('#showSearchDiv').show();
        getPopularRecentList();
    });

    $("body").on("click", ".searchIcon-home", function () {
        $("body .zsiq_floatmain").addClass('overrideZindex');
        $(".advanceSearch").fadeIn();
        $('meta[name=viewport]').attr('content', 'width=device-width, minimum-scale=1.0, maximum-scale=0.5, initial-scale=0.5');
        $("body .seachInput").focus();
        $("body").css('overflow', 'hidden');
        $('#showSearchDiv').show();
        getPopularRecentList();
    });

    $("body").on("click", ".cancelIcon, .close-layer", function () {
        $(".advanceSearch").fadeOut();
        $("body").css({ 'height': 'auto', 'overflow': 'auto' })
        $(".seachInput").val('');
        $('#showSearchDiv').show();
        $("body").css('overflow', 'inherit');
        $('meta[name=viewport]').attr('content', 'width=device-width, initial-scale=1.0');
        if ($('body .mainTuple').length) {
            $('#seachInput').autocomplete("destroy");
        }
    });

    //remove recentlist and clear cookie
    $("body").on("click", ".clr-srch, #clearAll", function () {
        $('#recentSearch').remove();
        setCookie("recentSearch", "");
    });

    //back button click
    $("body").on("click", ".leftArrow", function () {
        $(".advanceSearch").fadeOut();
        $('meta[name=viewport]').attr('content', 'width=device-width, initial-scale=1.0');
        $("body").css('overflow', 'inherit');
        $(".seachInput").val('');
        if (gmu.config.isMobile == 1) {
            $('body #letterCount').text(showChar);
        }
        $("body .zsiq_floatmain").removeClass('overrideZindex');
        if ($('body .mainTuple').length) {
            $('#seachInput').autocomplete("destroy");
        }
    });

    $("body").on("click", ".getName", function (e) {
        //set cookies
        setUserSearchHistory($(this).data('slug'));
        window.location.href = $(this).data('url');
    });

    $("body").on("click", ".searchCloseIcon", function () {
        $(".seachInput").val('');
        $('body #letterCount').text(showChar);

        if ($('body .mainTuple').length) {
            $('#seachInput').autocomplete("destroy");
        }
        $('#showSearchDiv').show();
        $('body .close-span').removeClass('searchCloseIcon');
    });
});

/** get the popular and recent search list */
function getPopularRecentList() {
    var data = getCookie('recentSearch');
    $.ajax({
        type: "POST",
        url: '/global-search/popular-recent-search',
        data: { query: data },
        success: function (response) {
            if (response.list != '') {
                $('#showSearchDiv').html(response.list);
            }
        }
    })
}

//search Input 
$('body').on('keyup', '.seachInput', function (e) {
    var id = this.id;
    var data = document.getElementById('seachInput').value;
    var showChar = "Type 3 or more characters for search results";

    if (parseInt(data.length) > 2) {
        showChar = '';
        $('body #showSearchDiv').hide();
        $('body .close-span').addClass('searchCloseIcon');

    } else {
        $('#showSearchDiv').show();
        if ($('.mainTuple').length) {
            $('#' + this.id).autocomplete("destroy");
            $('body .close-span').removeClass('searchCloseIcon');
        }
    }
    webengage.track("search", {
        "entity": gmu.config.entity,
        "entity_name": gmu.config.entity_name,
        "page_url": gmu.config.page_url,
        "keyword": data,
        
    });
    searchList('#' + id);
    //checking for mobile
    if (gmu.config.isMobile == 1) {
        $('#letterCount').text(showChar);
        $('#letterCount').css('margin-top', '35px');
        $('.rctSearchWrap').css('margin-top', '40px');
    } else {
        $('#letterCount').text(showChar);
        $('.rctSearchWrap').css('margin-top', '50px');
    }
});

function searchList(autocompleteElement) {
    $(autocompleteElement).autocomplete({
        appendTo: '.showsuggestorBox',
        minLength: 3,
        source: function (query, done) {
            $.ajax({
                type: "GET",
                url: "/global-search/search-all",
                data: { query: query },
                dataType: "json",
                success: function (response) {
                    response = JSON.parse(response);
                    if (!response.length) {
                        var result = [
                            {
                                label: 'No result found',
                                value: '',
                                group: ''
                            }
                        ];
                        done(result);
                    } else {
                        done(response);
                    }
                },
            });
        },
        select: function (event, ui) {
            event.preventDefault();
            if (ui.item.label == "No result found") {
                return false;
            }
           
            //set cookies
            setUserSearchHistory(ui.item.slug);
            window.location.href = ui.item.url;
        },
        open: function (event, ui) {
            $("html, body").css({ overflow: 'hidden' });
        },
        close: function () {
            $("html, body").css({ overflow: 'inherit' });
        },

    }).autocomplete("instance")._renderItem = function (ul, item) {
        return $("<li class='mainTuple'>")
            .append(" <span class='mainTuple'> "
                + item.label + "</span><span class='srchType mainTuple'>" + item.group + "</span>")
            .appendTo(ul);
    };
}

//Setting cookies in browser
function setUserSearchHistory(slug) {
    cookie = getCookie('recentSearch')
    var items = cookie ? cookie.split(/,/) : new Array()
    items = items.filter(function (value, index) {
        if (value !== slug) {
            return value;
        }
    });

    items.push(slug)
    items = [... new Set(items)]
    items = items.join(',')
    setCookie('recentSearch', items, 15);
}
/** end of global search */

//review landing college search
$('body').on('keyup', '.search-autocomplete-review', function (e) {
    var type = $(this).data('type');
    var id = this.id;
    searchReviewCollege('#' + id, type);
});

function searchReviewCollege(selectionId, type) {
    document.querySelector(selectionId).addEventListener("results", (event) => { });

    var collegeS = new autoComplete({
        data: {                              // Data src [Array, Function, Async] | (REQUIRED)
            src: async () => {
                const query = document.querySelector(selectionId).value;
                const source = await fetch(
                    '/ajax/search-review-college' + '?q=' + query + '&type=' + type
                );
                const data = await source.json();

                if (data !== '') {
                    return JSON.parse(data);
                }
            },
            onSelection: (feedback) => {
                const selection = feedback.selection.value[feedback.selection.key];
                window.location.href = feedback.selection.value.url;
            },
            key: ["name"],
            cache: false
        },
        selector: selectionId,                  // Input field selector              | (Optional)
        threshold: 3,                           // Min. Chars length to start Engine | (Optional)
        searchEngine: "strict",                 // Search Engine type/mode           | (Optional)
        resultsList: {                          // Rendered results list object      | (Optional)
            container: source => {
                source.setAttribute("id", selectionId.substring(1) + '_');
            },
            destination: selectionId,
            position: "afterend",
            element: "ul"
        },
        maxResults: 5,                          // Max. number of rendered results  | (Optional)
        highlight: {
            render: true,                       // Highlight matching results       | (Optional)
        },
        resultItem: {                           // Rendered result item             | (Optional)
            content: (data, source) => {
                source.innerHTML = `<span>${data.match}</span>`;
            },
            element: "li"
        },
        noResults: (dataFeedback, generateList) => {
            generateList(collegeS, dataFeedback, dataFeedback.results);
            const result = document.createElement("li");
            result.setAttribute("class", "no_result");
            result.setAttribute("tabindex", "1");
            result.innerHTML = `<span>Found No Reviews for this College</span>`;
            document.querySelector(`#${selectionId.substring(1) + '_'}`).appendChild(result);
        },
        onSelection: (feedback) => {
            const selection = feedback.selection.value[feedback.selection.key];
            window.location.href = feedback.selection.value.url;
        }
    });
}

/** course fees search */
$("body").on("click", ".programLink", function () {
    let link = $(this).data('program');
    window.location.href = link;

});

let inputSearchBar = document.querySelector('#filterCourseSeach');
let searchBarContainer = document.querySelector('.searchBar');

if (inputSearchBar !== null) {
    inputSearchBar.addEventListener('blur', (event) => {
        if (event?.relatedTarget?.classList[0] === 'programLink') {
            event.preventDefault();
        } else {
            document.querySelector('.courseSelection').style.display = 'none';
        }
    })
    inputSearchBar.addEventListener('keyup', () => {
        if (inputSearchBar.value === '') {
            document.querySelector('.courseSelection').style.display = 'none';
        }
    })
}
function getCourses() {
    document.querySelector('.courseSelection').style.display = "block";
    var input, filter, ul, li, a, i, txtValue;
    input = document.getElementById("filterCourseSeach");
    filter = input.value.toUpperCase();
    ul = document.getElementById("search-program");
    li = ul.getElementsByTagName("li");
    for (i = 0; i < li.length; i++) {
        a = li[i].getElementsByTagName("a")[0];
        txtValue = a.textContent || a.innerText;
        if (txtValue.toUpperCase().indexOf(filter) > -1) {
            li[i].style.display = "";
        } else {
            li[i].style.display = "none";
        }
    }
}
/** end of course fees search */

//Log User Activity
$(document).ready(function () {
    if (gmu.config.isLoggedIn) {
        gmu.config.parent_activity_id = sessionStorage.getItem('activity_id');
        $.ajax({
            type: "POST",
            url: "/site/log-activity",
            data: gmu.config,
            dataType: "json",
            success: function (response) {
                if (typeof gmu.config.parent_activity_id === 'undefined' || gmu.config.parent_activity_id == null) {
                    sessionStorage.setItem("activity_id", response)
                }
            },
            failure: function (error) {
                console.error(error);
            }
        });
    }

    if (gmu.config.entity == 'college' && gmu.config.entity_id != '') {
        $.ajax({
            type: "GET",
            url: "/ajax/live-application-form?collegeid=" + gmu.config.entity_id,
            dataType: "json",
            success: function (response) {
                $('#liveApplicationForm').html(response);
            },
            failure: function (error) {
                console.error(error);
            }
        });
    }

    $("body").on("click", "#loadMoreReview", function (e) {
        let page = this.dataset.page;
        let college_id = this.dataset.college;

        $('#filter-loader').show();
        $('#loadMoreReview').removeAttr("data-page");

        $.get('/ajax/load-reviews', { college_id: college_id, page: page }, function (response) {
            if (response.review == null) {
                $("#loadMoreReview").hide();
            }
            setTimeout(removePreLoader, 400)
            $('#paginationData').append(response.review);
            $("#loadMoreReview").attr("data-page", response.pageNo);
        });
    });

    //College/Articles & News
    $('.articleNewsClick').click(function () {
        $('.paginationDataArticlesNews').html('');
        articleNewsOffset = 12;
        let tab = $(this).data('tab');
        if (tab == 'articles') {
            if (articleNewsOffset >= $('#articlesCount').data('value')) {
                $('#collegeArticlesBtn').css('display', 'none');
            } else {
                $('#collegeArticlesBtn').css('display', 'block');
            }
            $('#collegeNewsBtn').css('display', 'none');
        } else if (tab == 'news') {
            if (articleNewsOffset >= $('#newsCount').data('value')) {
                $('#collegeNewsBtn').css('display', 'none');
            } else {
                $('#collegeNewsBtn').css('display', 'block');
            }
            $('#collegeArticlesBtn').css('display', 'none');
        }
    })

    var articleNewsOffset = 12;
    $("body").on("click", ".loadMoreArticlesNews", function (e) {
        let tab = $(this).data('tab');

        $('#filter-loader').show();
        $(this).removeAttr("data-page");

        $.get('news', { requestType: 'button', tab, offset: articleNewsOffset }, function (response) {
            !response ?? $(this).hide();

            setTimeout(removePreLoader, 400)
            if (tab === 'articles') {
                $('.paginationDataArticlesNews').append(response);
                if (articleNewsOffset + 12 >= $('#articlesCount').data('value')) {
                    $('#collegeArticlesBtn').css('display', 'none');
                }
            } else if (tab === 'news') {
                $('.paginationDataArticlesNews').append(response);
                if (articleNewsOffset + 12 >= $('#newsCount').data('value')) {
                    $('#collegeNewsBtn').css('display', 'none');
                }
            }
            articleNewsOffset += 12;
        });
    });

    //toggle the login form on click of review readmore
    $('body').on('click', '.showloginForm', function (e) {
        $(".textDivHeading").html("Signup to continue");
        $('.textDivSubHeading').html('Get latest updates and access to all premium content');
        var tab_id = $(this).attr('data-id');
        if (gmu.config.isLoggedIn == false) {
            $(".pageMask").css("display", "block");
            document.body.style.height = '100vh';
            document.body.style.overflowY = 'hidden';
            if (/iPhone|iPod|iPad/.test(navigator.userAgent)) {
                document.body.style.position = 'fixed';
            }
            document.querySelector('#leadform-state_id').value = this.dataset.stateid ?? '';
            document.querySelector('#cta_location').value = 'college_reviews_read_more' ?? '';
            document.querySelector('#cta_text').value = 'college_review_read_more' ?? '';
            document.querySelector('#entity_subtype').value = gmu.config.entity_subtype;
            document.querySelector('#entity_type').value = gmu.config.entity_type;
            document.querySelector('#leadform-entity').value = this.dataset.entity;
            document.querySelector('#leadform-entity_id').value = this.dataset.entity_id;
            document.querySelector('#interested_location').value = this.dataset.interested_location;
            document.querySelector("#activity_id").value = sessionStorage.getItem('activity_id') ?? '';
            localStorage.setItem('reviewReadMoreId', tab_id);
            localStorage.setItem('reviewPosition', window.pageYOffset);
            localStorage.setItem('reviewClicked', "Yes");
            // showLoginPopup(false)
            document.querySelector('#lead-form-js-new').style.display = "block";
        } else {
            if (tab_id) {
                $(".reviewContentBox.contentRead_" + tab_id).css('max-height', 'unset');
                $(".redirectreviewCard.reviewCardHide_" + tab_id).remove()
            }
        }
    });
    $('body').on('click', '.showloginFormCollege', function (e) {
        var tab_id = $(this).attr('data-id');
        var text = $(this).text();
        // console.log(text);
        if (tab_id) {

            if ($.trim(text) == 'Read More') {
                $(".hide-review_" + tab_id).show();
                $(".show-review_" + tab_id).hide();
                $(".reviewContentBox.contentRead_" + tab_id).css('max-height', 'unset');
                $(this).html('Read Less <span class="spriteIcon showMoreIcon read-less"></span>');
            } else {
                $(".hide-review_" + tab_id).hide();
                $(".show-review_" + tab_id).show();
                $(".reviewContentBox.contentRead_" + tab_id).css('max-height', '110px');
                $(this).html('Read More <span class="spriteIcon showMoreIcon"></span>');
            }
            //   $(".redirectreviewCard.reviewCardHide_" + tab_id).remove()
        }

    });

    scrollToClickedReview();
    $("body").on("click", "#lead-login-new", function () {
        // $(".leadFormContainer").css("display", "none");
        document.querySelector('#lead-form-js-new').style.display = "none";
        $("#login-form-js").fadeIn();
        $("#signup-form-news").css("display", "none");
        $("#signup-form").css("display", "none");
        $("#otp-form").fadeIn();
    });

    $("body").on("click", '.closeLoginpopup', function () {
        $("#login-form").css("display", "none");
        // $("#login-form-js").css("display", "none");
    });
    cutoffFilterPage();
});

if (gmu.config.entity == 'college' && gmu.config.entity_id != '') {
    $.ajax({
        type: "GET",
        url: "/ajax/live-application-form?collegeid=" + gmu.config.entity_id,
        dataType: "json",
        success: function (response) {
            $('#liveApplicationForm').html(response);
        },
        failure: function (error) {
            console.error(error);
        }
    });
}
$(document).on('click', '.featuredScrollRight', function () {
    $('.homeFeaturedCollege').slick('slickNext');
})

$(document).on('click', '.featuredScrollLeft', function () {
    $('.homeFeaturedCollege').slick('slickPrev');
})

//check login and remove key from localStorage
function scrollToClickedReview() {
    if ((localStorage.getItem('loggedIn') === 'Yes')) {
        var posit = localStorage.getItem('reviewPosition');
        if (posit !== null) {
            $('html, body').animate({
                scrollTop: posit
            }, 1000);
            $(".reviewContentBox.contentRead_" + localStorage.getItem('reviewReadMoreId')).css('max-height', 'unset');
            $(".redirectreviewCard.reviewCardHide_" + localStorage.getItem('reviewReadMoreId')).remove()
            localStorage.removeItem('reviewReadMoreId');
            localStorage.removeItem('reviewPosition');
            localStorage.removeItem('loggedIn');

            if (localStorage.getItem('reviewClicked') === 'Yes' && localStorage.getItem('singupClicked') === 'Yes') {
                // window.location.reload();
                localStorage.removeItem('loggedIn');
            }
        }
    } else {
        localStorage.removeItem('reviewReadMoreId');
        localStorage.removeItem('reviewPosition');
        localStorage.removeItem('loggedIn');
        localStorage.removeItem('singupClicked');
        localStorage.removeItem('reviewClicked')
    }
}
loadCta();

// load cta via ajax
function loadCta(cta = '', notIn = '') {
    $(document).ready(function () {
        if (cta) {
            $(".getSupport ").remove();
            $(".get1Lakhs").remove();
            $(".newHeroButtons").remove();
            $(".mobileCollegeCta").remove();
        }
        if (document.querySelector('.lead-cta') !== null) {
            if (notIn == true) {
                var lead_cta = $.map($('.lead-cta'), function (e) {
                    return $(e).attr('data-lead_cta');
                }).filter(function (value) {
                    return value !== '28'; // Remove '28' Apply Now Info Page
                }).filter(function (value) {
                    return value !== '27'; // Remove '27' Apply Now CourseFees Page
                });
            } else {
                var lead_cta = $.map($('.lead-cta'), function (e) {
                    return ($(e).attr('data-lead_cta'));
                });
            }

            var data_entity = $(".lead-cta").data("entity");
            var sponsorClientUrl = $(".lead-cta").attr("data-sponsor") ?? '';
            var durl = $(".lead-cta").attr("data-durl") ?? '';
            var image = $(".lead-cta").data("image");
            var programSlug = $(".lead-cta").data("program");
            var course = $(".lead-cta").attr("data-course");

            if (gmu.config.pageName == 'courses-fees') {
                lead_cta = "0," + lead_cta;
            }
            $.ajax({
                type: 'POST',
                url: '/ajax/load-cta',
                data: {
                    '_csrf-frontend': gmu.config.csrf,
                    dynamicCta: gmu.config.dynamicCta ?? [],
                    entity_id: gmu.config.entity_id ?? null,
                    entity: gmu.config.entity ?? '',
                    displayName: gmu.config.display_name ?? (gmu.config.entity_name ?? ''),
                    slug: gmu.config.entity_slug ?? '',
                    pageName: gmu.config.pageName ?? '',
                    data_entity: data_entity ?? '',
                    sponsorClientUrl: sponsorClientUrl ?? '',
                    image: image ?? '',
                    city: gmu.config.interested_location ?? '',
                    state: gmu.config.interested_location_state ?? '',
                    lead_cta: lead_cta ?? '',
                    programSlug: programSlug,
                    courseId: course,
                    durl: durl ?? '',
                },
                dataType: "json",
                success: function (response) {

                    if (response.data) {
                        var positionLists = [3, 4, 5, 6, 7, 8, 9, 10, 14, 15, 16, 24, 27, 28];
                        $('[data-lead_cta]').each(function (index, value) {
                            var position = $(this).data('lead_cta');
                            var id = $(value).closest('div').prop('id');
                            var slug = $(value).closest('div').attr('data-slug');
                            if (id !== "" || slug !== undefined) {
                                if (response.entity == 'exam') {
                                    $('#' + id).append(response.data[position]);
                                } if (response.entity == 'college') {
                                    if ($.inArray(position, positionLists) !== -1) {
                                        $(response.data[position]).appendTo('[data-slug="' + slug + '"]');
                                    }
                                }
                                if (response.entity == 'course') {
                                    $('#' + id).append(response.data[position]);
                                }
                            } else {
                                var position = $(this).data('lead_cta');
                                if (response.data[position] != undefined) {
                                    $(response.data[position]).appendTo('[data-lead_cta="' + position + '"]');
                                }
                            }
                        })
                    }

                }
            });
        }
    });
};

// load cta for admission listing page
if ((gmu.config.entity_subtype == 'admission-listing')) {
    loadCtaFilter('admission-listing');
}

// load college filter cta via ajax
function loadCtaFilter(cta = '', predictParams = '') {
    $(document).ready(function () {
        var flitlerStream = predictParams.stream;
        var flitlerCourse = predictParams.course;
        if (document.querySelector('.lead-cta-college-filter-1') !== null) {
            if (cta == 'college-filter') {
                $(".collegeFilterLeadOne").remove();
                $(".collegeFilterLeadTwo").remove();
            }
            var lead_cta = $.map($('.lead-cta-college-filter-1'), function (e) {
                return ($(e).attr('data-lead_cta'));
            });
            var data_entity = $(".lead-cta-college-filter-1").data("entity");
            var image = $(".lead-cta-college-filter-1").data("image");
            var course = $(".lead-cta-college-filter-1").data("course_slug");
            var sponsored = $(".lead-cta-college-filter-1").data("sponsored");
            $.ajax({
                type: 'POST',
                url: '/ajax/load-filter-cta',
                data: {
                    '_csrf-frontend': gmu.config.csrf,
                    dynamicCta: gmu.config.dynamicCta ?? [],
                    entity_id: gmu.config.entity_id ?? null,
                    entity: gmu.config.entity ?? '',
                    displayName: gmu.config.display_name ?? '',
                    slug: gmu.config.entity_slug ?? '',
                    pageName: gmu.config.pageName ?? '',
                    data_entity: data_entity ?? '',
                    image: image ?? '',
                    city: gmu.config.interested_location ?? '',
                    state: gmu.config.interested_location_state ?? '',
                    lead_cta: lead_cta ?? '',
                    course: course ?? '',
                },
                dataType: "json",
                success: function (response) {

                    if (response.data) {
                        if (response.data[10] != undefined && sponsored == 1) {
                            $(response.data[10]).appendTo('[data-lead_cta="' + 10 + '"]');
                        }
                        if (response.data[23] != undefined) {
                            $(response.data[23]).appendTo('[data-lead_cta="' + 23 + '"]');
                        }
                        if (response.data[25] != undefined) {
                            $(response.data[25]).appendTo('[data-lead_cta="' + 25 + '"]');
                        }
                    }
                }
            });
        }

        if (document.querySelector('.lead-cta-college-filter-2') !== null) {
            if (cta == 'college-filter') {
                $(".collegeFilterLeadOne").remove();
                $(".collegeFilterLeadTwo").remove();
                $(".CollegeFilterLeadScholer").remove();
            }
            var lead_cta = $.map($('.lead-cta-college-filter-2'), function (e) {
                return ($(e).attr('data-lead_cta'));
            });
            var data_entity = $(".lead-cta-college-filter-2").data("entity");
            var image = $(".lead-cta-college-filter-2").data("image");
            var course = $(".lead-cta-college-filter-2").data("course_slug");
            $.ajax({
                type: 'POST',
                url: '/ajax/load-filter-cta',
                data: {
                    '_csrf-frontend': gmu.config.csrf,
                    dynamicCta: gmu.config.dynamicCta ?? [],
                    entity_id: gmu.config.entity_id ?? null,
                    entity: gmu.config.entity ?? '',
                    displayName: gmu.config.display_name ?? '',
                    slug: gmu.config.entity_slug ?? '',
                    pageName: gmu.config.pageName ?? '',
                    data_entity: data_entity ?? '',
                    image: image ?? '',
                    city: gmu.config.interested_location ?? '',
                    state: gmu.config.interested_location_state ?? '',
                    lead_cta: lead_cta ?? '',
                    course: course ?? '',
                },
                dataType: "json",
                success: function (response) {
                    if (response.data) {
                        if (response.data[11] != undefined) {
                            $(response.data[11]).appendTo('[data-lead_cta="' + 11 + '"]');
                        }
                        if (response.data[21] != undefined) {
                            $(response.data[21]).appendTo('[data-lead_cta="' + 21 + '"]');
                        }
                        if (response.data[26] != undefined) {
                            $(response.data[26]).appendTo('[data-lead_cta="' + 26 + '"]');
                        }
                    }
                }
            });
        }

        if (document.querySelector('.filter-college-scholership') !== null) {
            if (cta == 'college-filter') {
                $(".collegeFilterLeadOne").remove();
                $(".collegeFilterLeadTwo").remove();
            }
            var lead_cta_scholership = $.map($('.filter-college-scholership'), function (e) {
                return ($(e).attr('data-lead_cta'));
            });
            var data_entity = $(".filter-college-scholership").attr("data-entity");;
            var course = $(".filter-college-scholership").attr("data-courseslug");
            var stream = $(".filter-college-scholership").attr("data-stream");
            $.ajax({
                type: 'POST',
                url: '/ajax/load-filter-cta',
                data: {
                    '_csrf-frontend': gmu.config.csrf,
                    dynamicCta: gmu.config.dynamicCta ?? [],
                    entity_id: gmu.config.entity_id ?? null,
                    entity: gmu.config.entity ?? '',
                    displayName: gmu.config.display_name ?? '',
                    slug: gmu.config.entity_slug ?? '',
                    pageName: gmu.config.pageName ?? '',
                    data_entity: data_entity ?? '',
                    image: image ?? '',
                    city: gmu.config.interested_location ?? '',
                    state: gmu.config.interested_location_state ?? '',
                    lead_cta: lead_cta_scholership ?? '',
                    course: course ?? '',
                    stream: stream ?? '',
                },
                dataType: "json",
                success: function (response) {
                    if (response.data) {
                        if (response.data[22] != undefined) {
                            $(response.data[22]).appendTo('[data-lead_cta="' + 22 + '"]');
                        }
                    }
                }
            });

        }
        if (flitlerStream != '') {
            $('.predict-my-college-cta').attr('data-stream', flitlerStream);

        }
        if (flitlerCourse != '') {
            $('.predict-my-college-cta').attr('data-courseslug', flitlerCourse);
        }
    });
}

//cut off filter
function cutoffFilterPage() {
    $('body .filterSection').on("change", "input[type='radio']", function (e) {
        updateCutoffItems();
        $('table').wrap("<div class='table-responsive'></div>");
    });

    $('body').on("click", "#resetFilter", function (e) {
        $('.tab-content li').children('input').prop('checked', false);
        updateCutoffItems();

    });

    $('body').on("click", "#resetAll", function (e) {
        $('.filterCheckButtons li').children('input').prop('checked', false);
        updateCutoffItems();
        cutOffCi();
    });

    $('body').on("click", ".filtersAll ul.tabs li", function (e) {
        var tab_id = $(this).attr('data-tab');
        $('ul.tabs li').removeClass('current');
        $('.tab-content').removeClass('current');

        $(this).addClass('current');
        $("#" + tab_id).addClass('current');
    });

    $('body').on("click", ".filterCheckContainer.courseMore", function (e) {
        $('.filterCheckContainer.hideCourse ').css('display', 'inline-block');
        $('.filterCheckContainer.courseMore').hide();
        $('#courseViewLess').css('display', 'inline-block');
    });

    $('body').on("click", "#courseViewLess", function (e) {
        $('.filterCheckContainer.hideCourse').css('display', 'none');
        $('#courseViewLess').hide();
        $('.filterCheckContainer.courseMore').show();
    });

    $('body').on('click', '.removeFilter', function (e) {
        e.preventDefault();
        var removeValue = $(this).parent().parent().data('slug');
        $("input[value='" + removeValue + "']").prop('checked', false);
        updateCutoffItems();
    });
}

function showMoreLessCutoff() {
    $(".cutOffCard").each(function () {
        var asd = $(this).children('.cutoffMatserType').length;
        if (asd > 2) {
            $(this).addClass("cutoffLimitCard");
            $(this).after('<div class="showMoreCourseWrap"><p class="showMoreCourseCard">Show More</p></div>');
            $(this).css({
                "margin-bottom": "0px",
            });
        }
    });

    $("body .courseCutOffTypeList .showMoreCourseWrap").click(function () {
        var toggleText = $(this).text() == "Show Less" ? "Show More" : "Show Less";
        $(this).children().text(toggleText);
        $(this).prev().toggleClass("cutoffLimitCard");
    });

}
showMoreLessCutoff();

function showMoreLessCutoffTable() {
    $(".cutOffTable").each(function () {
        var asd = $(this).children('table').children('tbody').children().length;
        if (asd > 6) {
            $(this).addClass("cutoffLimitCardIntable");
            $(this).after('<div class="showMoreCourseWrap"><p class="showMoreCourseCard">Show More</p></div>');
            $(this).css({
                "margin-bottom": "0px",
            });
        }
    });

    $("body .cutoffMatserType .showMoreCourseWrap").click(function () {
        var toggleText = $(this).text() == "Show Less" ? "Show More" : "Show Less";
        $(this).children().text(toggleText);
        $(this).prev().toggleClass("cutoffLimitCardIntable");
    });
}
showMoreLessCutoffTable();

var $collegeCuttOffSearchForm = $('#cut-off-search-form');
function updateCutoffItems(sortBy) {
    $('#filter-loader').show();
    sort = sortBy ? sortBy : '';
    if ($collegeCuttOffSearchForm.attr('action') != undefined) {
        $.post($collegeCuttOffSearchForm.attr('action').split("?")[0] + '?sort=' + sort, $('#cut-off-search-form').serialize(), function (html) {
            $('.cutoffFilterSection').html();
            $(".courseCutOffTypeList").html($(html).find('div.courseCutOffTypeList').html());
            $(".filtersAll").html($(html).find('section.filterSection').html());
            $('table').wrap("<div class='table-responsive'></div>");

            let buttonLe = $(".filterSectionSelection button").length;
            if (buttonLe > 0) {
                $(".filterSectionForm").css('padding-bottom', '10px');
                $(".filterSectionForm").css('border-bottom', '1px solid #d8d8d8');
                if (gmu.config.isMobile == 1) {
                    $(".filterSectionSelection").css('margin-top', '10px');
                }
            }

            showMoreLessCutoff();
            setTimeout(removePreLoader, 400);

            if (gmu.config.pageName == 'ci') {
                var courseId = gmu.config.course_id;
                $(".cutoffFilterSection .updated-info").attr("style", "display: none !important");
                var ulElement = document.getElementById("cutoffsearch-course");
                var liElements = ulElement.getElementsByTagName("li");

                for (var i = 0; i < liElements.length; i++) {
                    var inputElement = liElements[i].getElementsByTagName("input")[0];
                    if (inputElement && inputElement.id !== courseId) {
                        ulElement.removeChild(liElements[i]);
                    }
                }
                $('.filterSectionSelection button:first .spriteIcon.closeIcon').attr("style", "display: none !important");
            }
        }, 'html');
    }
}

if (gmu.config.pageName == "ci") {
    cutOffCi();
}

function cutOffCi() {
    if ($('.cutoffFilterSection').length > 0) {
        var courseId = gmu.config.course_id;
        var radioInput = $('ul#cutoffsearch-course input[type="radio"][value="' + courseId + '"]');
        if (radioInput.length > 0) {
            radioInput.prop('checked', true);
        }
        updateCutoffItems();
    }
}

function cutOffExamFilter(param) {
    $('#filter-loader').show();
    var c = $('#' + param + '_courseDiv').val();
    var s = $('#' + param + '_specDiv').val();
    var e = document.getElementById(param + 'Exam');
    var r = document.getElementById(param + 'Round');
    var data = e.options[e.selectedIndex].value.split('_');

    $.get('/ajax/get-exam-based-cut-off', {
        'exam': data[0], 'year': data[1],
        'round': r.options[r.selectedIndex].value, 'spec': s, 'course': c, 'college_id': gmu.config.entity_id
    }, function (html) {
        if (html !== false) {
            $("." + param + '_table').html(html);
            $('table').wrap("<div class='table-responsive'></div>");
            $(".getLeadForm." + param).show();
        } else {
            $("." + param + '_table').html("<div> No Result Found </div>");
            $(".getLeadForm." + param).hide();

        }

        setTimeout(removePreLoader, 400);
    });
}

function gdGoenkaCityList(city) {
    var cityIds = ['93', '125', '178', '179', '180', '182', '184', '185', '188', '191', '194', '196', '199', '200', '202', '243', '249', '574', '591', '596', '599', '698', '699', '700', '721', '732', '736', '745', '753', '757', '761', '762', '782', '790', '793'];
    if (cityIds.includes(city)) {
        return true;
    } else {
        return false;
    }
}

function fetchRankPredict(rankType = null) {
    var formData = {};
    formData.marks = $('#rankPredictormarks').val()
    formData.examId = $('#rankPredictorexamId').val()
    formData.rankType = rankType
    $.ajax({
        url: "/exam/rank-predict",
        type: "POST",
        data: formData,
        dataType: "json",
        success: function (data) {
            if (gmu.config.isLoggedIn == false) {
                localStorage.setItem('rankPredictorResult', data);
                $('#rankPredictorResult').hide();
            } else {
                $('#rankPredictorResult').show();
            }
            $('#rankPredictorLandingForm').hide();
            $('#rankPredictResult').html(data);
        },
        error: function (xhr, status, error) {
            $('#rankPredictorLandingForm').hide();
            $('#rankPredictorResult').show();
        }
    });
}

function updateCsrf(token) {
    $('meta[name="csrf-token"]').attr('content', token);
    $('input[name="_csrf-frontend"]').val(token);
    gmu.config.csrf = token;
}

function rankPredictorInputVerify(rankType = null) {

    const inputElement = $("#rankPredictormarks");
    const minAttributeValue = parseFloat(inputElement.attr("min"));
    const maxAttributeValue = parseFloat(inputElement.attr("max"));
    const userValue = parseFloat(inputElement.val());

    if (!isNaN(minAttributeValue) && !isNaN(maxAttributeValue) &&
        (isNaN(userValue) || userValue < minAttributeValue || userValue > maxAttributeValue)) {
        $("#errorMessage").text("Entered marks must be between " + minAttributeValue + " and " + maxAttributeValue + ".");
    } else {
        $("#errorMessage").empty();
        if (gmu.config.isLoggedIn == false) {
            $(".predict__rank__input__section").css("display", "none");
            $("#rankPredictButton").addClass("js-open-rank-lead-form").prop("onclick", null);
            document.querySelector('#rank_predictor_lead_value').value = 1;
            $('#firstScreenSubmitRank').attr('rankType', rankType);
        } else {

            fetchRankPredict(rankType)
        }
        // $("#rankPredictButton").click(function () {
        //     $("#rankPredictButton").removeClass("js-open-lead-form-new").prop("onclick", rankPredictorInputVerify);
        // });
    }
}

//lead form mobile validation
$(".field-leadform-mobile #leadform-mobile, #contactus-mobile").keypress(function (e) {
    var mobNum = $(this).val();
    var key = e.keyCode;
    var regex = /^[0-9]+$/;
    var cursorPosition = this.selectionStart;
    var isValid = regex.test(String.fromCharCode(key));

    if (!isValid || (
        jQuery.inArray(String.fromCharCode(key), ['9', '8', '7', '6']) == -1 &&
        cursorPosition == 0)) {
        e.preventDefault();
    } else {
        if (mobNum.length >= 10) {
            e.preventDefault();
        }
    }
});

//Contact-Us page implementation
$('.selectOfficeRow ul li').click(function () {
    var tab_id = $(this).attr('data-tab');
    Array.from(this.parentElement.children).forEach((li) => {
        li.classList.remove('current');
    })
    document.querySelectorAll(`.mapDiv`).forEach(mapElement => {
        mapElement.classList.remove('current')
    })
    $(this).addClass('current');
    $("." + tab_id).addClass('current');
});

//Contact-Us page validation for empty field
$('#contactus-fullName, #contactus-mobile, #contactus-email, #contactus-query').bind('keyup', function () {
    if (allFilled()) {
        $('.contactUsSubmitButton').removeAttr('disabled');
    } else {
        $('.contactUsSubmitButton').attr('disabled', true);
    }
});

function validateLeadEmailConatct() {
    document.querySelector("#contactus-email").addEventListener('input', (e) => {
        e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');
        if ((e.target.value.match(/\./g) || []).length > 1) {
            e.target.value = e.target.value.substring(0, e.target.value.length - 1);
        }
        if ((e.target.value.match(/@/g) || []).length > 1) {
            e.target.value = e.target.value.substring(0, e.target.value.length - 1);
        }
    })
    // document.querySelector("#contactus-email").addEventListener('blur', (e) => {
    //     if (e.target.value.includes("@")) {
    //         e.target.value = e.target.value.slice(0, e.target.value.indexOf("@"));
    //     }
    // })
}

//Contact-us page submission logic

$('body').on('click', '.contactUsSubmitButton', function (e) {
    e.preventDefault();
    if (validateMobileNumber($('#contactus-mobile').val()) === false) {
        $('#contactus-mobile').addClass('error');
        $('#contactus-mobile-label').html('Mobile number should be of 10 digits');
        $('.contactUsSubmitButton').attr('disabled', true);
        return false;
    } else {
        $('#contactus-mobile').removeClass('error');
        $('#contactus-mobile-label').html('');
        $('.contactUsSubmitButton').removeAttr('disabled');
    }
    $.ajax({
        type: 'POST',
        url: '/contact-us-mail',
        data: { userName: $('#contactus-fullName').val(), userMobile: $('#contactus-mobile').val(), userMailId: $('#contactus-email').val(), queryContent: $('#contactus-query').val() },
        dataType: "json",
        success: function (response) {
            if (response == true) {
                document.querySelector('.contactUsFormSubmitted').showModal();
                document.querySelector('body').style.overflowY = 'hidden';
                isModalOpen = true;
                document.addEventListener('touchmove', preventDefaultScroll, { passive: false });
            }
        }
    });
})

let isModalOpen = false;
function preventDefaultScroll(event) {
    if (isModalOpen) {
        event.preventDefault();
    }
}

$('body').on('click', '.contactUsCloseIcon', function (e) {
    document.querySelector('.contactUsFormSubmitted').close();
    isModalOpen = false;
    // location.reload();
})

function allFilled() {
    var filled = true;
    var required = $('.contactUsForm input,.contactUsForm textarea').filter('[required]:visible');
    required.each(function () {
        if ($(this).val() === '' || $(this).val() === undefined) {
            filled = false;
        }
    });
    return filled;
}

// document?.addEventListener('DOMContentLoaded', function () {
//     var container = document.querySelector('.courseListContainer');
//     var children = container?.querySelectorAll('.courseListCard');

//     for (var i = 0; i < children.length - 1; i++) {
//         children[i].style.borderBottom = '1px solid #ccc';
//     }
// });

document?.addEventListener('DOMContentLoaded', function () {
    var container = document.querySelector('.courseListContainer');
    if (container) {
        var children = container?.querySelectorAll('.courseListCard');
        for (var i = 0; i < children.length - 1; i++) {
            children[i].style.borderBottom = '1px solid #ccc'
        }
    }
});

//data chart
if (gmu.config.careerChartData !== undefined && gmu.config.careerChartData.length !== 0) {
    jQuery(document).ready(function ($) {

        var result = $.map(gmu.config.careerChartData.data, function (data) {
            return Math.max(data)
        });

        var xAxesData = $.map(gmu.config.careerChartData.year, function (data) {
            return "" + data + ""
        });

        var ctx = document.getElementById("careerChartData").getContext('2d');
        var maxResult = (Math.max.apply(Math, result) + 10000);

        var chartDiv = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: xAxesData,
                datasets: [{
                    label: 'salary',
                    data: result,
                    backgroundColor: "rgb(61,143,242)"
                }],
            },
            options: {
                scales: {
                    xAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Year',
                            fontSize: 16,
                        }
                    }],
                    yAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Average Salary (In INR Per Annum)',
                            fontSize: 16,
                        },
                        ticks: {
                            beginAtZero: true,
                            suggestedMax: maxResult
                        }

                    }]
                }
            }
        });
    });
}
// if (window.location.href.includes('/user-profile') == true) {
//     // const element = document.getElementById("lead-form-js-new");
//     const elementOne = document.getElementById("signupModalId");
//     // element.remove();
//     elementOne.remove();
// }

function addAttributesAndDeferLoading() {
    var pageDataElements = document.querySelectorAll('.pageData');
    pageDataElements.forEach(function (element) {
        var images = element.querySelectorAll('img');
        images.forEach(function (img) {
            var existingClasses = img.getAttribute('class');
            if (existingClasses) {
                img.setAttribute('class', existingClasses + ' lazyload');
            } else {
                img.setAttribute('class', 'lazyload');
            }
            img.setAttribute('loading', 'lazy');
            img.setAttribute('data-src', img.getAttribute('src'));
        });
    });
}

$(document).ready(function () {
    if (document.getElementById('activeLinkScroll') !== null) {
        document.getElementById('activeLinkScroll').scrollIntoView({ inline: 'center', block: 'end' });
    }
});

//college compare college header js starts
if (document.querySelector(".compareIcon") !== null) {
    document.querySelector(".compareIcon").addEventListener("click", () => {
        $.ajax({
            url: '/ajax/show-college-compare-select-panel',
            type: 'POST',
            dataType: "json",
            success: function (response) {
                $('#college_compare_header_select_panel').html(response.headerSelectPanel);
                getCollegeCompareDetails();
                document.querySelector(".collegeCompare__container").style.display = 'block';
            }
        });
    })
}

getCollegeCompareDetails();

//college compare js starts
function getCollegeCompareDetails() {
    $(document).ready(function () {
        window.onbeforeunload = function () {
            localStorage.removeItem('redirected_college');
        }

        $("body").on("click", ".shortlist__button", function () {
            isModalOpenLead = true;
            document.addEventListener('touchmove', preventDefaultScrollLead(event), { passive: false });
            if (this.id == 'shortlist__button_one') {
                var id = $('select[name="compareCollegeOne"] option:selected').val();
                var college_name = $('select[name="compareCollegeOne"] option:selected').text();
            } else {
                var id = $('select[name="compareCollegeTwo"] option:selected').val();
                var college_name = $('select[name="compareCollegeTwo"] option:selected').text();
            }
            var student_id = localStorage.getItem('studentId');
            var activity_id = localStorage.getItem('session_activity_id');
            webengage.track("college_shortlisted", {
                "college_id": id,
                "college_name": college_name,
                "page_url": gmu.config.page_url,
             });
            $.ajax({
                url: '/ajax/save-college-shortlist-data',
                type: 'POST',
                data: { college_id: id, student_id: student_id, activity_id: activity_id },
                dataType: "json",
                success: function (response) {
                    $(".collegeCompareThankYouScreen").css("display", "block");
                }
            });
        })

        $("body").on("click", ".closeThankYouScreenCollegeCompare", function () {
            isModalOpenLead = false;
            $("body").css("height", "unset");
            $("body").css("overflowY", "unset");
            $(".collegeCompareThankYouScreen").css("display", "none");
        })

        //college subpage icon functionality starts
        if (document.querySelector(".compareCloseIcon") !== null) {
            document.querySelector(".compareCloseIcon").addEventListener("click", () => {
                document.querySelector(".collegeCompare__container").style.display = 'none';
            })
        }

        $("body").on("click", "#header_comapre_button", function () {
            var collegeIdOne = $('select[name="compareCollegeOne"] option:selected').val();
            var collegeIdTwo = $('select[name="compareCollegeTwo"] option:selected').val();
            var programIdOne = $('select[name="collegeCompareProgramOne"] option:selected').val();
            var programIdTwo = $('select[name="collegeCompareProgramTwo"] option:selected').val();

            $.ajax({
                url: "/ajax/get-college-info",
                type: 'POST',
                data: { college_ids: [collegeIdOne, collegeIdTwo] },
                dataType: "json",
                success: function (response) {
                    // Redirect to another page
                    if (response.college_one !== '' && response.college_two !== '') {
                        var newUrl = window.location.protocol + "//" + window.location.hostname + '/college-compare?college-' + response.college_one + '_' + programIdOne + '-vs-' + response.college_two + '_' + programIdTwo;
                        // Redirect to the new URL
                        window.location.href = newUrl;

                        localStorage.setItem('redirected_college', 1);
                    }

                }
            });
        })

        //college subpage icon functionality ends

        //common for both one and two starts
        function clearSelections(selection__Inputs, count) {
            // Refactor to make clearSelections work on mobile as well (function now accepts a general object instead of event object)
            Array.from(selection__Inputs.children).forEach((child) => {
                if (child.classList.contains("value__selected")) {
                    count++;
                }
            })
            if (count == 2) {
                // Refactor to make clearSelections work on mobile as well (directly target the .clearSelection class of the intended .selectionInputs)
                if (selection__Inputs.querySelector('.clearSelection')) {
                    selection__Inputs.querySelector('.clearSelection').style.display = 'flex';
                }
            }
        }

        $('.selection__Select2__College').on('change', (e) => {
            // let selectionValue = $('.selection__Select2__College').select2('data')[0].text;
            e.target.parentElement.style.display = 'none';
            e.target.parentElement.nextElementSibling.classList.add("value__selected");
            let count = 0;
            // Refactor to make clearSelections work on mobile as well (only sending the required object instead of the whole event object)
            clearSelections(e.target.parentElement.parentElement, count);
        })
        $('.selection__Select2__Program').on('change', (e) => {
            // let selectionValue = $('.selection__Select2__Program').select2('data')[0].text;
            e.target.parentElement.style.display = 'none';
            e.target.parentElement.nextElementSibling.classList.add("value__selected");
            let count = 0;
            // Refactor to make clearSelections work on mobile as well (only sending the required object instead of the whole event object)
            clearSelections(e.target.parentElement.parentElement, count);
        })

        document.querySelectorAll('.editIcon').forEach((icon) => {
            icon.addEventListener('click', (e) => {
                localStorage.removeItem('redirected');
                localStorage.removeItem('redirected_college');

                //only for mobile
                if ($(window).width() < 1023) {
                    if (e.target.parentElement.classList[1] == 'selection__Input_One') {
                        if (e.target.parentElement.classList[2] == 'selectedProgram') {
                            // for program
                            $(".selection__Select2__Program_One").val('').trigger('change');
                            $(".selectedProgram_One").removeClass("value__selected");
                            $(".selection__Input_Program_One").css("display", "flex");
                            $(".selection__Select2__Program_One").prop('disabled', false);
                        } else {
                            //for college
                            $(".selection__Select2__College_One").val('').trigger('change');
                            $(".selectedCollege_One").removeClass("value__selected");
                            $(".selection__Input_College_One").css("display", "flex");

                            // for program
                            $(".selection__Select2__Program_One").val('').trigger('change');
                            $(".selectedProgram_One").removeClass("value__selected");
                            $(".selection__Input_Program_One").css("display", "flex");
                            $(".selection__Select2__Program_One").prop('disabled', true);
                        }
                    }
                    if (e.target.parentElement.classList[1] == 'selection__Input_Two') {
                        if (e.target.parentElement.classList[2] == 'selectedProgram') {

                            // for program
                            $(".selection__Select2__Program_Two").val('').trigger('change');
                            $(".selectedProgram_Two").removeClass("value__selected");
                            $(".selection__Input_Program_Two").css("display", "flex");
                            $(".selection__Select2__Program_Two").prop('disabled', false);
                        } else {
                            //for college
                            $(".selection__Select2__College_Two").val('').trigger('change');
                            $(".selectedCollege_Two").removeClass("value__selected");
                            $(".selection__Input_College_Two").css("display", "flex");

                            // for program
                            $(".selection__Select2__Program_Two").val('').trigger('change');
                            $(".selectedProgram_Two").removeClass("value__selected");
                            $(".selection__Input_Program_Two").css("display", "flex");
                            $(".selection__Select2__Program_Two").prop('disabled', true);
                        }
                    }
                }

                // To clear select2 and show placeholder again on editIcon click
                $(e.target.parentElement.previousElementSibling.firstElementChild).val('').trigger('change');
                e.target.parentElement.classList.remove("value__selected");
                e.target.parentElement.previousElementSibling.style.display = "flex";

                $(e.target.parentElement.nextElementSibling.firstElementChild).val('').trigger('change');
                e.target.parentElement.nextElementSibling.nextElementSibling.classList.remove("value__selected");
                e.target.parentElement.nextElementSibling.style.display = "flex";
                e.target.parentElement.nextElementSibling.firstElementChild.setAttribute('disabled', 'disabled');

                if (e.target.classList.contains('editIconOne')) {
                    e.target.parentElement.parentElement.querySelector('.clearSelection').style.display = "none";
                }
            })
        })

        document.querySelectorAll('.clearSelection').forEach((clear) => {
            clear.addEventListener("click", (e) => {
                localStorage.removeItem('redirected');
                localStorage.removeItem('redirected_college');
                // Refactor to make clearSelections work on mobile as well (query modified for mobile elements)
                clear.parentElement.querySelectorAll(".selection__Input__Box, .mobile__input__box").forEach((item) => {
                    $(item.firstElementChild).val('').trigger('change');
                    item.style.display = 'flex';
                    if (item.firstElementChild.classList.contains("selection__Select2__Program")) {
                        item.firstElementChild.setAttribute('disabled', 'disabled');
                    }
                    // To clear sselect2 and show placeholder again on clearIcon click
                })
                clear.parentElement.querySelectorAll(".value__selected").forEach((item) => {
                    item.classList.remove("value__selected")
                })
                clear.style.display = 'none';

            })
        })

        //mobile select2 drawer js
        document.querySelectorAll('.drawercloseIcon').forEach((icon) => {
            icon.addEventListener('click', (e) => {
                e.target.parentElement.parentElement.style.display = 'none';
            })
        })
        document.querySelectorAll('.drawer__close__submit').forEach((button) => {
            button.addEventListener('click', (e) => {
                e.target.parentElement.parentElement.style.display = 'none';
                // Refactor to make clearSelections work on mobile as well
                // translate upwards from the drawer submit button to reach mobile__selection__inputs using jquery and get the native DOM element using .get(0) so we can switch back to JS
                let selectionInput = $(button).closest('.mobile__selection__inputs').get(0);
                selectionInput.querySelectorAll('.mobile__input__box').forEach((item) => {
                    item.style.display = 'none';
                    item.nextElementSibling.classList.add('value__selected');
                })
                clearSelections(selectionInput, 0)
            })
        })
        document.querySelectorAll('.mobile__input__box').forEach((inputBox) => {
            inputBox.addEventListener('click', (e) => {
                e.target.parentElement.parentElement.lastElementChild.style.display = 'block';
                // In css, we add pointer-event: none to the child p and img tags. This stops click event on parent element to propagate to child elements.
                e.target.parentElement.lastElementChild.style.display = 'block';
            })
        })

        //common for both one and two starts ends

        //college compare || college One starts
        $('.selection__Select2__College_One').select2({
            placeholder: "Add a College",
            name: 'compareCollegeOne',
            minimumInputLength: 2,
            ajax: {
                url: "/ajax/college-list",
                dataType: "json",
                type: "GET",
                minimumInputLength: 3,
                data: function (params) {
                    var queryParameters = {
                        term: params.term,
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.text,
                                id: item.id,
                            };
                        }),
                    };
                },
            },
        });

        $('.selection__Select2__Program_One').select2({
            placeholder: "Select Program",
            name: 'collegeCompareProgramOne',
            ajax: {
                url: "/ajax/lead-courses",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term,
                        entity: gmu.config.entity,
                        entity_id: $('select[name="compareCollegeOne"] option:selected').val(),
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.text,
                                id: item.id,
                            };
                        }),
                    };
                },
            },
        });

        $('select[name="compareCollegeOne"]').change(function () {
            var collegeId = $('select[name="compareCollegeOne"] option:selected').val();
            var college_name = $('select[name="compareCollegeOne"] option:selected').text();
            $(".selection__Select2__Program_One").prop('disabled', false);
            $.ajax({
                url: '/ajax/college-city',
                type: 'POST',
                data: { college_id: collegeId },
                dataType: "json",
                success: function (response) {
                    $(".selectedCollege__heading_one").html($.trim(college_name));
                    $(".selectedCollege__heading_anchor_one").attr("href", 'college/' + response.collegeSlug)
                    $(".selectedCollege__subheading_one").html(response.cityName + ', ' + response.stateName)
                    if (response.collegeLogo !== "") {
                        $("#collegeCompareLogoOne").css("display", "block");
                        $("#collegeCompareLogoDefaultOne").attr("style", "display : none !important");
                        $("#collegeCompareLogoOne").attr("src", "https://media.getmyuni.com/azure/college-image/small/" + response.collegeLogo);
                    } else {
                        $("#collegeCompareLogoDefaultOne").css("display", "block");
                        $("#collegeCompareLogoOne").attr("style", "display : none !important");
                    }
                }
            });
        })

        $('select[name="collegeCompareProgramOne"]').change(function () {
            var programName = $('select[name="collegeCompareProgramOne"] option:selected').text();
            $(".selectedProgram__heading_one").html(programName);
        })
        //college compare || college One Ends

        //college compare || college Two Starts
        $('.selection__Select2__College_Two').select2({
            placeholder: "Add a College",
            name: 'compareCollegeTwo',
            minimumInputLength: 2,
            ajax: {
                url: "/ajax/college-list",
                dataType: "json",
                type: "GET",
                minimumInputLength: 3,
                data: function (params) {
                    var queryParameters = {
                        term: params.term,
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.text,
                                id: item.id,
                            };
                        }),
                    };
                },
            },
        });

        $('.selection__Select2__Program_Two').select2({
            placeholder: "Select Program",
            name: 'collegeCompareProgramTwo',
            ajax: {
                url: "/ajax/lead-courses",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term,
                        entity: gmu.config.entity,
                        entity_id: $('select[name="compareCollegeTwo"] option:selected').val(),
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.text,
                                id: item.id,
                            };
                        }),
                    };
                },
            },
        });

        $('select[name="compareCollegeTwo"]').change(function () {
            var collegeId = $('select[name="compareCollegeTwo"] option:selected').val();
            var college_name = $('select[name="compareCollegeTwo"] option:selected').text();
            $(".selection__Select2__Program_Two").prop('disabled', false);
            $.ajax({
                url: '/ajax/college-city',
                type: 'POST',
                data: { college_id: collegeId },
                dataType: "json",
                success: function (response) {
                    $(".selectedCollege__heading_two").html($.trim(college_name));
                    $(".selectedCollege__heading_anchor_two").attr("href", 'college/' + response.collegeSlug)
                    $(".selectedCollege__subheading_two").html(response.cityName + ', ' + response.stateName)
                    if (response.collegeLogo !== "") {
                        $("#collegeCompareLogoDefaultTwo").attr("style", "display : none !important");
                        $("#collegeCompareLogoTwo").css("display", "block");
                        $("#collegeCompareLogoTwo").attr("src", "https://media.getmyuni.com/azure/college-image/small/" + response.collegeLogo);
                    } else {
                        $("#collegeCompareLogoDefaultTwo").css("display", "block");
                        $("#collegeCompareLogoTwo").attr("style", "display : none !important");
                    }
                }
            });
        })

        $('select[name="collegeCompareProgramTwo"]').change(function () {
            var programName = $('select[name="collegeCompareProgramTwo"] option:selected').text();
            $(".selectedProgram__heading_two").html(programName);
        })
        //college compare || college Two Starts Ends

        $(".selection__Select2__College_One, .selection__Select2__College_Two, .selection__Select2__Program_One, .selection__Select2__Program_Two").on("change", function (event) {
            if ($('select[name="compareCollegeOne"] option:selected').val() !== '' && $('select[name="compareCollegeTwo"] option:selected').val() !== '' && $('select[name="collegeCompareProgramOne"] option:selected').val() !== '' && $('select[name="collegeCompareProgramTwo"] option:selected').val() !== '') {
                $(".collegeCompare__compareButton").prop('disabled', false);
                $(".collegeCompare__compareButton__container").css("display", "flex");
            } else {
                $(".collegeCompare__compareButton").prop('disabled', true);
                $('.collegeCompare__mainContent').remove();
                $(".shortlist__college").css("display", "none");
            }

            if ($(window).width() < 1023) {
                if ($('select[name="compareCollegeOne"] option:selected').val() !== '' && $('select[name="collegeCompareProgramOne"] option:selected').val() !== '') {
                    $(".drawer__close__submit_one").prop('disabled', false);
                    $(".mobile__input__box_one").css("display", "none");
                    $(".selection__Input_One").addClass("value__selected");
                } else {
                    $(".drawer__close__submit_one").prop('disabled', true);
                }

                if ($('select[name="compareCollegeTwo"] option:selected').val() !== '' && $('select[name="collegeCompareProgramTwo"] option:selected').val() !== '') {
                    $(".drawer__close__submit_two").prop('disabled', false);
                    $(".mobile__input__box_two").css("display", "none");
                    $(".selection__Input_Two").addClass("value__selected");
                } else {
                    $(".drawer__close__submit_two").prop('disabled', true);
                }
            }

            if (window.location.href.indexOf("/college-compare") > -1) {
                clearSelections(document.querySelector(".selectCollegeScreen__leftPanel .selection__Inputs"), 0);
                clearSelections(document.querySelector(".selectCollegeScreen__rightPanel .selection__Inputs"), 0);
                CheckSelectPanel()
            }
        });

        const el = document.querySelector(".selectCollegeScreen__panels")
        const observer = new IntersectionObserver(
            ([e]) => e.target.classList.toggle("is-pinned", e.intersectionRatio < 1),
            { threshold: [1] }
        );

        if (el !== null) {
            observer.observe(el);
        }

        function CheckSelectPanel() {
            var collegeIdOne = $('select[name="compareCollegeOne"] option:selected').val();
            var collegeIdTwo = $('select[name="compareCollegeTwo"] option:selected').val();
            var programIdOne = $('select[name="collegeCompareProgramOne"] option:selected').val();
            var programIdTwo = $('select[name="collegeCompareProgramTwo"] option:selected').val();

            if (collegeIdOne && collegeIdTwo && programIdOne && programIdTwo) {
                $("title").text($('select[name="compareCollegeOne"] option:selected').text() + " Vs " + $('select[name="compareCollegeTwo"] option:selected').text() + ": A Comparitive Guide");
                $("h1").text($('select[name="compareCollegeOne"] option:selected').text() + " Vs " + $('select[name="compareCollegeTwo"] option:selected').text());
                var metaDescriptionTag = document.querySelector('meta[name="description"]');
                metaDescriptionTag.setAttribute('content', $('select[name="compareCollegeOne"] option:selected').text() + " Vs " + $('select[name="compareCollegeTwo"] option:selected').text() +
                    ': Compare key factors such as fees, courses offered, admission details, placements, cutoff, ranking, facilities, scholarships offered, and more..');

                if (localStorage.getItem('redirected_college') == '1') {
                    LoadHtmlForCollegeCompare(collegeIdOne, collegeIdTwo, programIdOne, programIdTwo);
                }

                if (localStorage.getItem('redirected') !== '1') {
                    $("#college_compare_detail_button").off("click").on("click", function () {
                        LoadHtmlForCollegeCompare(collegeIdOne, collegeIdTwo, programIdOne, programIdTwo);
                    });
                } else {
                    localStorage.removeItem('redirected');
                    LoadHtmlForCollegeCompare(collegeIdOne, collegeIdTwo, programIdOne, programIdTwo);
                }
            }
        }

        function LoadHtmlForCollegeCompare(collegeIdOne, collegeIdTwo, programIdOne, programIdTwo) {
            if (document.querySelector(".collegeCompare__ladingPage__banner") !== null) {
                document.querySelector(".collegeCompare__ladingPage__banner").classList.remove("collegeCompare__ladingPage__banner");
            }
            if (document.querySelector(".collegeCompare__selectCollegeScreen__container") !== null) {
                document.querySelector(".collegeCompare__selectCollegeScreen__container").classList.remove("collegeCompare__selectCollegeScreen__container");
            }

            if (localStorage.getItem('redirected') !== '1') {
                $.ajax({
                    url: "/ajax/get-college-info",
                    type: 'POST',
                    data: { college_ids: [collegeIdOne, collegeIdTwo] },
                    dataType: "json",
                    success: function (response) {
                        // Replace the URL
                        if (response.college_one !== '' && response.college_two !== '') {
                            var newUrl = window.location.protocol + "//" + window.location.hostname + '/college-compare?college-' + response.college_one + '_' + programIdOne + '-vs-' + response.college_two + '_' + programIdTwo;
                            history.pushState({}, '', newUrl);
                        }

                    }
                });
            }
            $(".collegeCompare__compareButton__container").css("display", "none");

            $.ajax({
                url: window.location.pathname,
                type: 'POST',
                data: { college_id_one: collegeIdOne, college_id_two: collegeIdTwo, program_name_one: programIdOne, program_name_two: programIdTwo },
                dataType: "json",
                success: function (response) {
                    if (response.html != '') {
                        $('#ajaxLoadedCompareDetails').html(response.html);
                        LoggedInUserFunctionality()
                    }
                }
            });
        }

        function accordion() {
            var acc = document.getElementsByClassName("accordion");
            var i;
            if (acc.length > 0) {
                acc[0].classList.toggle("active");
                acc[0].nextElementSibling.style.maxHeight = acc[0].nextElementSibling.scrollHeight + 'px';
                acc[1].classList.toggle("active");
                acc[1].nextElementSibling.style.maxHeight = acc[1].nextElementSibling.scrollHeight + 'px';
                for (i = 0; i < acc.length; i++) {
                    acc[i].addEventListener("click", function () {
                        this.classList.toggle("active");
                        var panel = this.nextElementSibling;
                        if (panel.style.maxHeight) {
                            panel.style.maxHeight = null;
                        } else {
                            panel.style.maxHeight = panel.scrollHeight + "px";
                        }
                    });
                }
            }

        }
        LoggedInUserFunctionality();
        function LoggedInUserFunctionality() {
            if (gmu.config.isLoggedIn == true) {
                localStorage.setItem('redirected', 1);
                $(".loginToContinue").css("display", "none");
                if (document.querySelector('.shortlist__college') !== null) {
                    document.querySelector('.shortlist__college').classList.add("shortlist__college_scroll");
                }
                if (document.querySelector('.collegeCompare__mainContent') !== null) {
                    document.querySelector('.collegeCompare__mainContent').classList.remove("content__locked");

                }
                accordion()
                Array.from(document.getElementsByClassName("accordion")).forEach((item) => {
                    if (!item.classList.contains("active")) {
                        item.classList.add("active")
                        item.nextElementSibling.style.maxHeight = item.nextElementSibling.scrollHeight + 20 + 'px';
                    }
                })

                // Read More Logic
                let leftPanelScrollHeight = document.querySelector('.colllegeCompare__readMore.section__content__leftPanel') !== null ? document.querySelector('.colllegeCompare__readMore.section__content__leftPanel').scrollHeight : null;
                let rightPanelScrollHeight = document.querySelector('.colllegeCompare__readMore.section__content__rightPanel') !== null ? document.querySelector('.colllegeCompare__readMore.section__content__rightPanel').scrollHeight : null;
                let parentScrollHeight = leftPanelScrollHeight > rightPanelScrollHeight ? leftPanelScrollHeight : rightPanelScrollHeight;

                document.querySelectorAll('.viewMore__button').forEach((button) => {
                    // let parentScrollHeight = button.parentElement.parentElement.scrollHeight;
                    button.addEventListener('click', (e) => {
                        if (e.target.firstElementChild.textContent === 'Show More') {
                            e.target.parentElement.parentElement.style.maxHeight = parentScrollHeight + 20 + 'px';
                            e.target.parentElement.parentElement.style.height = parentScrollHeight + 20 + 'px';
                            e.target.parentElement.parentElement.parentElement.style.maxHeight = parentScrollHeight + 100 + 'px';
                            e.target.firstElementChild.textContent = "Show Less";
                            e.target.lastElementChild.style.transform = 'rotate(-90deg) scale(0.7)';
                        } else {
                            e.target.parentElement.parentElement.style.maxHeight = null;
                            e.target.firstElementChild.textContent = "Show More"
                            e.target.lastElementChild.style.transform = 'rotate(90deg) scale(0.7)';
                        }
                    })
                })
            } else {
                accordion()
            }
        }

        if (window.location.href.indexOf('?') == -1) {
            localStorage.removeItem('redirected');
        }

        //college compare detail js
        if (window.location.href.indexOf("/college-compare") > -1) {
            // Get the query string
            var queryString = window.location.search;

            var cleanedQueryString = queryString.replace('?college-', '');
            var cleanUrl = cleanedQueryString.split("-vs-");

            var college_One = cleanUrl[0].split("_");
            var college_Two = cleanUrl[1].split("_");

            $.ajax({
                url: "/ajax/get-college-info",
                type: 'POST',
                data: { college_slugs: [college_One[0], college_Two[0]], program_ids: [college_One[1], college_Two[1]] },
                dataType: "json",
                success: function (response) {
                    if (response.college_one !== null && response.college_one.id !== '' && response.college_one.name !== '') {
                        var $option = $("<option selected></option>").val(response.college_one.id).text(response.college_one.name);
                        $("select[name=compareCollegeOne]").append($option).trigger('change');
                    }

                    if (response.college_two !== null && response.college_two.id !== '' && response.college_two.name !== '') {
                        var $option = $("<option selected></option>").val(response.college_two.id).text(response.college_two.name);
                        $("select[name=compareCollegeTwo]").append($option).trigger('change');
                    }

                    if (response.program_one !== null && response.program_one.name !== '') {
                        var $option = $("<option selected></option>").val(college_One[1]).text(response.program_one.name);
                        $("select[name=collegeCompareProgramOne]").append($option).trigger('change');
                    }

                    if (response.program_two !== null && response.program_two.name !== '') {
                        var $option = $("<option selected></option>").val(college_Two[1]).text(response.program_two.name);
                        $("select[name=collegeCompareProgramTwo]").append($option).trigger('change');
                    }
                }
            });
            clearSelections(document.querySelector(".selectCollegeScreen__leftPanel .selection__Inputs"), 0);
            clearSelections(document.querySelector(".selectCollegeScreen__rightPanel .selection__Inputs"), 0);

            LoggedInUserFunctionality();
        }
    });
}

//author-profile
var offset = 20;
var isScrollCount = true;
if ((gmu.config.entity_subtype == 'author-profile')) {
    $('.scrollClick').click(function (e) {
        if ($(this).attr('data-scroll') == 'false') {
            isScrollCount = false;
        } else {
            isScrollCount = true;
        }
        offset = parseInt($(this).attr('data-offset'));
        // alert(isScrollCount);
    });

    $(window).scroll(function () {
        if ($(window).scrollTop() + $(window).height() >= $(document).height() && isScrollCount) {
            var authorSlug = $('.authorInfo').attr('data-slug');
            var tab = $('.activeLink').attr('data-tab');
            var isScroll = true;
            $('#filter-loader').show();
            $.ajax({
                type: "POST",
                url: authorSlug,
                data: { offset: offset, tab: tab, isScroll: isScroll },
                success: function (response) {
                    console.log(response.count);
                    if (response.count == 0) {
                        $('#filter-loader').hide();
                        isScrollCount = false;
                        $('.' + tab + 'scroll').attr('data-scroll', isScrollCount);
                        //  $('#'+tab).append('<a href="#" class="no-more-css">No More result found</a>');
                        return false;
                    }
                    var Id = $('#' + tab).attr('data-list-id');
                    $('#' + Id).append(response);
                    $('#filter-loader').hide();
                }
            });
            offset = parseInt(offset) + 20;
            $('.' + tab + 'scroll').attr('data-offset', offset);

        }
    });
}

if (gmu.config.entity == 'college-listing') {
    var url = gmu.config.page_url;
    $(document).ready(function () {
        getAllCollegeData();
        function getAllCollegeData() {
            var url = gmu.config.page_url;
            const queryString = window.location.search;
            $.post(url + queryString,
                function (data, status) {
                    $('.all-college-ajax').append(data.html);
                    $("body .foundClgs button").slice(0, 2).show();
                });
        }
    });

}

function preventDefaultScrollLead(event) {
    if (isModalOpenLead) {
        document.body.style.height = '100vh';
        document.body.style.overflowY = 'hidden';
        if (/iPhone|iPod|iPad/.test(navigator.userAgent)) {
            document.body.style.position = 'fixed';
        }
        if (event) {
            event.preventDefault();
        }
    }

}

window.addEventListener('load', addAttributesAndDeferLoading);

//admission landing college search
$('body').on('keyup', '.admission__landing__hero .search-autocomplete-admission', function (e) {
    var type = $(this).data('type');
    var query = $(this).val();
    var id = this.id;
    if (query.length > 2) {
        searchAdmissionCollege('#' + id, type, query);
    }
});

function searchAdmissionCollege(selector, type, query) {
    $.ajax({
        type: 'POST',
        url: '/ajax/search-admission-college' + '?q=' + query + '&type=' + type,
        data: { type: type, q: query },
        dataType: "json",
        success: function (data) {
            displaySearchResults($('ul#searchResults'), data);
        },
        error: function (error) {
            console.log('Error fetching search results:', error);
        }
    });
}

function displaySearchResults($selector, results) {
    $selector.empty();
    if (results.length > 0 && !Array.isArray(results)) {
        results = $.parseJSON(results);
    } else {
        $selector.append('<option disabled>No results found</option>');
        return;
    }
    results.forEach((result) => {
        $selector.append('<li class="ui-menu-item"><a class="ui-menu-item-wrapper" href="' + result.url + '">' + result.name + '</a></li>');
    });
    $selector.on('change', function () {
        var url = $(this).val();
        if (url) {
            window.location.href = url;
        }
    });
}

// var isJsLoaded = false;
// var autoPopUpTimeOut = setTimeout(function () {
//     if (!isJsLoaded) {
//         const link = document.createElement('link');
//         link.rel = 'stylesheet';
//         if (gmu.config.prodEvn == 'prod') {
//             link.href = window.location.origin + '/yas/css/version2/min/lead_form_v4.css';
//         } else {
//             link.href = window.location.origin + '/yas/css/version2/lead_form_v4.css';
//         }

//         document.head.appendChild(link);

//         const script = document.createElement('script');
//         if (gmu.config.prodEvn == 'prod') {
//             script.src = window.location.origin + '/yas/js/version2/min/lead_form_v4.js';
//         } else {
//             script.src = window.location.origin + '/yas/js/version2/lead_form_v4.js';
//         }

//         // script.src = window.location.origin+'/yas/js/version2/min/lead_form_v4.js';
//         document.body.appendChild(script);
//         isJsLoaded = true;
//         inputErrorClearonFocusLeadForm();
//     }
// }, 10000);

if (window.location.href.includes('/user-profile') == false) {
    // const GTAG_SCRIPT = window.dataLayer = window.dataLayer || [];
    // function gtag(){ console.log("Datalayer called"); dataLayer.push(arguments);}gtag('js', new Date());gtag('config', 'G-LXCLLX7LL6');


    // $('body').on('click', '.js-open-lead-form-new', function (e) {
    //     clearTimeout(autoPopUpTimeOut);
    //     let ctaLocation = $(this).data('ctalocation');
    //     let ctaText = $(this).data('ctatext');
    //     const entityId = $(this).data('entityid');
    //     localStorage.setItem('ctaData', JSON.stringify({
    //         'ctaLocation': ctaLocation,
    //         'ctaText': ctaText
    //     }));

    //     if (!isJsLoaded) {
    //         const link = document.createElement('link');
    //         link.rel = 'stylesheet';
    //         link.href = `${window.location.origin}/yas/css/version2/lead_form_v4.css`;
    //         document.head.appendChild(link);
    //         const script = document.createElement('script');
    //         script.src = `${window.location.origin}/yas/js/version2/lead_form_v4.js`;
    //         script.onload = function () {
    //             handleLeadValueClick($(this), {
    //                 articleId: entityId,
    //                 entity: gmu.config.entity,
    //                 url: '/ajax/article-stream-level',
    //                 callback: toggleStreamField
    //             });
    //         };
    //         script.onerror = function () {
    //             console.error('Failed to load the script:', script.src);
    //         };
    //         document.body.appendChild(script);
    //         isJsLoaded = true;
    //         inputErrorClearonFocusLeadForm();
    //     }
    //     isModalOpenLead = true;
    //     document.addEventListener('touchmove', preventDefaultScrollLead(event), { passive: false });
    //     e.preventDefault();
    //     setScrollPosition();

    //     if (typeof autoPopUpTimeOut !== 'undefined' && autoPopUpTimeOut !== null) {
    //         clearTimeout(autoPopUpTimeOut);
    //     }

    //     $('.textDivHeading').html("");
    //     $('.textDivSubHeading').html("");

    //     document.querySelector('#lead-form-js-new').style.display = "block";
    //     document.querySelector('.pageMask').style.display = "block";
    //     document.body.style.overflow = 'hidden';    

    //     var leadFormTitle = this.dataset.leadformtitle ?? '';
    //     document.querySelector('#cta_location').value = this.dataset.ctalocation ?? '';
    //     document.querySelector('#cta_text').value = this.dataset.ctatext ?? '';
    //     document.querySelector('#cta_title').value = this.dataset.ctatitle ?? '';
    //     document.querySelector('#entity_subtype').value = gmu.config.entity_subtype;
    //     document.querySelector('#entity_type').value = gmu.config.entity_type;
    //     document.querySelector('#leadform-entity').value = gmu.config.entity;
    //     document.querySelector('#leadform-entity_id').value = gmu.config.entity_id;
    //     document.querySelector("#durl").value = this.dataset.durl ?? '';
    //     document.querySelector("#dynamic_redirection").value = this.dataset.dynamic_redirection ?? '';
    //     document.querySelector("#activity_id").value = sessionStorage.getItem('activity_id') ?? '';

    //     if (leadFormTitle && typeof leadFormTitle == 'string') {
    //         $('.textDivHeading').append(leadFormTitle);
    //     } else {
    //         $('.textDivHeading').append('REGISTER NOW TO APPLY');
    //     }
    //     var getParams = $(this).data();
    //     console.log(getParams);
    //     sendGtmData('CTA_click-' + gmu.config.entity, getParams);

    //     // }

    // });


    $('body').on('click', '.js-open-rank-lead-form', function (e) {
        if (gmu.config.prodEvn == 'prod') {
            // script.src = window.location.origin + '/yas/js/version2/min/rank_predictor_lead.js';
        } else {
            //script.src = window.location.origin + '/yas/js/version2/rank_predictor_lead.js';
        }
        // document.body.appendChild(script);
        isJsLoaded = false;
        inputErrorClearonFocusLeadForm();

        // isModalOpenLead = true;
        // document.addEventListener('touchmove', preventDefaultScrollLead(event), { passive: false });
        e.preventDefault();
        setScrollPosition();

        if (typeof autoPopUpTimeOut !== 'undefined' && autoPopUpTimeOut !== null) {
            clearTimeout(autoPopUpTimeOut);
        }

        $('.textDivHeadingRank').html("");
        $('.textDivSubHeading').html("");

        document.querySelector('.predict__rank__form__section').style.display = "block";
        document.querySelector('#cta_location').value = "exam_" + gmu.config.pageName + "_mtf_lead";
        document.querySelector('#cta_text').value = gmu.config.entity_subtype;
        document.querySelector('#entity_subtype').value = gmu.config.entity_subtype;
        document.querySelector('#entity_type').value = "exam";
        document.querySelector('#leadform-entity').value = "exam";
        document.querySelector('#leadform-entity_id').value = gmu.config.entity_id;
        document.querySelector("#durl").value = '';
        document.querySelector("#dynamic_redirection").value = '';
        document.querySelector("#activity_id").value = sessionStorage.getItem('activity_id') ?? '';
        var rankSubText = $(this).attr('data-subheadingtext');
        if (rankSubText != undefined) {
            $('.textDivHeadingRank').append(rankSubText);
        } else {
            $('.textDivHeadingRank').append('Please fill the below details to receive a detailed report of your rank');
        }

    });
}

/*GTM Function*/

function sendGtmData(eventName, params) {
    var eventAttributes = {};
    if (typeof window === 'undefined') return;
    window.dataLayer = window.dataLayer || [];
    eventAttributes["event"] = eventName
    eventAttributes["event-name"] = eventName
    eventAttributes["page-url"] = window.location.href
    $.each(params, function (indexname, value) {
        eventAttributes[indexname] = value;

    })
    window.dataLayer.push(eventAttributes);
}

function sendGtmDataForm(eventName, params) {
    var eventAttributes = {};
    if (typeof window === 'undefined') return;
    window.dataLayer = window.dataLayer || [];
    eventAttributes["event"] = eventName
    eventAttributes["event-name"] = eventName
    eventAttributes["page-url"] = window.location.href
    $.each(params, function (indexname, value) {
        eventAttributes[indexname] = value;

    })
    window.dataLayer.push(eventAttributes);
}

/* CTA Impression  on page load*/

setTimeout(function () {
    const observer = new IntersectionObserver((entries) => {
        for (const entry of entries) {
            const intersecting = entry.isIntersecting;
            if (intersecting) {
                var getParams = entry.target.dataset
                sendGtmData('CTA_impression-' + gmu.config.entity, getParams);
                observer.unobserve(entry.target);
            }
        }
    });
    let target = '.js-open-lead-form-new';
    document.querySelectorAll(target).forEach((i) => {
        if (i) {
            observer.observe(i);
        }
    });
    const observerForm = new IntersectionObserver((entries) => {
        for (const entry of entries) {
            const intersecting = entry.isIntersecting;
            if (intersecting) {
                var getParams = {};
                // getParams['user-input'] = entry.target.dataset.userInput
                getParams['cta-location'] = $('#cta_location').val();
                getParams['cta-text'] = $('#cta_text').val();
                sendGtmDataForm('FORM_impression_field_' + entry.target.dataset.userInput, getParams);
                observerForm.unobserve(entry.target);
            }
        }
    });

    let targetForm = '.data-gtm';
    document.querySelectorAll(targetForm).forEach((i) => {
        if (i) {
            observerForm.observe(i);
        }
    });

}, 1000);
/* Fess popup*/
$(document).ready(function () {
    $('.showTooltip').click(function () {
        var element = $(this);
        var courseID = element.attr('data-course-id');
        var collegeID = element.attr('data-college-id');
        detailedFeeTooltip(courseID, collegeID);
    });
});
function detailedFeeTooltip(courseID, collegeID) {

    var courseID = courseID;
    var collegeID = collegeID;
    if (courseID !== 'undefined' && collegeID !== null) {
        $.ajax({
            url: "/course-fees-structure",
            method: "GET",
            async: false,
            data: {
                courseID: courseID,
                collegeID: collegeID
            },
            success: function (detailedFee) {
                if (detailedFee !== null) {
                    $('#fees-breakup').html(detailedFee).css('display', 'block');
                    document.getElementById('dialogModalFees').showModal();
                    $('table').wrap("<div class='table-responsive'></div>");
                }
            }
        });
    }

}

$(document).on('click', '#closeFeeDetailsFees', function () {
    document.getElementById('dialogModalFees').close();
    $('#fees-breakup').html('').css('display', 'none');

});

document.querySelectorAll(".tooltip").forEach((tooltip) => {
    tooltip.addEventListener("click", (e) => {
        if (e.target !== e.currentTarget) {
            return;
        }
        document.querySelectorAll(".tooltiptext").forEach((tooltiptext) => {
            tooltiptext.style.visibility = 'hidden';
            tooltiptext.style.opacity = '0';
        })
        e.target.lastElementChild.style.visibility = 'visible';
        e.target.lastElementChild.style.opacity = '1';
    })
})
document.body.addEventListener('click', (e) => {
    if (e.target.parentElement.classList.contains('tooltip') || e.target.classList.contains('tooltip')) {
        return;
    } else {
        document.querySelectorAll(".tooltiptext").forEach((tooltiptext) => {
            tooltiptext.style.visibility = 'hidden';
            tooltiptext.style.opacity = '0';
        })
    }
})

$('body').on('blur', '.data-gtm', function () {
    var inputValue = $(this).val();
    var checkClassNo = $(this).hasClass('no-impression');
    var checkClassYes = $(this).hasClass('yes-impression');
    if (checkClassNo) {
        return false;
    }
    if (checkClassYes) {
        return false;
    }
    if (inputValue) {
        console.log();
        var dataSetInput = $(this).attr('data-user-input');
        var getParams = {};
        getParams['user-input'] = inputValue;
        getParams['cta-location'] = $('#cta_location').val();
        getParams['cta-text'] = $('#cta_text').val();
        sendGtmDataForm('FORM_click_field_' + dataSetInput, getParams);
    }
});

$('body').on('change', '.data-gtm-change', function () {
    var inputValue = $(this).val();
    if (inputValue) {
        var dataSetInput = $(this).attr('data-user-input');
        var getParams = {};
        var selectedText = $('option:selected', this).map(function () {
            return $(this).text();
        }).get().join(',');
        getParams['user-input'] = selectedText;
        getParams['cta-location'] = $('#cta_location').val();
        getParams['cta-text'] = $('#cta_text').val();
        sendGtmDataForm('FORM_click_field_' + dataSetInput, getParams);
    }
})

$('body').on('change', '.data-gtm-change-radio', function () {
    var inputValue = $(this).val();
    var optionText = '';
    if (inputValue) {
        var dataSetInput = $(this).attr('data-user-input');
        var getParams = {};
        if (inputValue == 1) {
            optionText = 'Yes';
        } else {
            optionText = 'No';
        }
        getParams['user-input'] = optionText;
        getParams['cta-location'] = $('#cta_location').val();
        getParams['cta-text'] = $('#cta_text').val();
        sendGtmDataForm('FORM_click_field_' + dataSetInput, getParams);

    }
})

//Loading HomePage Popular Article and Recent Article data
$(document).ready(function () {
    $('.homePageArticle').children().eq(0).attr('data-tab', 'agriculture');

    $('.homePageArticle').click(function () {
        let stream = $(this).children().attr('data-tab');
        if (stream) {
            updateArticleList($('#recentArticleHome'), stream, null);
            updateArticleList($('#popularArticleHome'), stream, true);
        }
    });

    $('.homePageArticle').first().click();
});

function updateArticleList(target, stream, popular = null) {
    $('.loader').show();
    target.children().remove();

    setTimeout(function () {
        $.ajax({
            type: "get",
            url: '/site/get-home-page-articles',
            data: { stream: stream, isPopular: popular },
            success: function (response) {
                $('.loader').hide();
                if (response.length > 0) {
                    if (popular == true) {
                        $('.quickLinks h2').eq(1).css('display', 'block');
                    } else {
                        $('.quickLinks h2').eq(0).css('display', 'block');
                    }
                    let htmlContent = response.map(element => `
                        <li>
                            <a title="${element['title']}" href="/articles/${element['slug']}">
                                <img src="https://media.getmyuni.com/assets/images/articles/${element['cover_image']}" alt="${element['slug']}" />
                                <h3>${element['title']}</h3>
                            </a>
                        </li>
                    `).join('');
                    target.html(htmlContent);
                } else {
                    $('.loader').hide();
                    if (popular == true) {
                        $('.quickLinks h2').eq(1).css('display', 'none');
                    } else {
                        $('.quickLinks h2').eq(0).css('display', 'none');
                        let htmlContent = [1].map(element => `
                            <li class="errorData">No Data Found</li>
                        `).join('');
                        target.html(htmlContent);
                    }
                }
            },
            error: function (xhr, status, error) {
                $('.loader').hide();
                if (popular == true) {
                    $('.quickLinks h2').eq(1).css('display', 'none');
                } else {
                    $('.quickLinks h2').eq(0).css('display', 'none');
                }
                console.error("Error fetching general articles:", error);
            }
        });
    }, 500);
}

$(document).ready(function () {
    $(".nesteddiv> ul > li").click(function (e) {
        767 < $(window).width() && (document.querySelectorAll(".popUp"),
            e.preventDefault(),
            $(this).parents().find("li.activeLink").removeClass("activeLink"),
            $(this).addClass("activeLink"),
            $(".popUp").css("display", "none"),
            $(".popUp#" + this.id).css("display", "block"),
            e.preventDefault())
    }),
        $(".popUp ul li a").click(function (e) {
            e.stopPropagation()
        }),
        $(".nesteddiv > ul > li > span").click(function () {
            var e, t
            767 < $(window).width() && (e = $(".nesteddiv").width() - 20,
                $(".nesteddiv .popUp").css("width", e),
                t = (e = $(this).parents()).offset().left - e.parents().offset().left,
                e.find(".popUp").css("left", -t))
        }),
        $(".toggle").click(function (e) {
            $(window).width() < 767 && (e.preventDefault(),
                (e = $(this)).next().hasClass("show") ? (e.next().removeClass("show"),
                    e.next().slideUp(1e3)) : (e.parent().parent().find("li .popUp").removeClass("show"),
                        e.parent().parent().find("li .popUp").slideUp(1e3),
                        e.next().toggleClass("show"),
                        e.next().slideToggle(1e3)))
        })
});


//  var entityName= (gmu.config.entity) ? gmu.config.entity : '';
//  var entityId= (gmu.config.entity_id) ? gmu.config.entity_id : 0;
//  $.get('/banner-popup', {'entity':entityName,'entity-id':entityId}, function (bannerPopup) {
//     $("#banner-popup").html(bannerPopup);
// });

$(document).ready(function () {
    let content = $('.gmu-mock-content');
    let btn = $('#gmu-readMore-btn');
    let viewAllButton = $('#gmu-viewAll-btn');

    btn.click(function () {
        content.toggleClass('expanded');
        if (content.hasClass('expanded')) {
            btn.text('READ LESS');
        } else {
            btn.text('READ MORE');
        }
    });

    viewAllButton.click(function () {
        $('.gmu-testListItem:nth-child(n+6)').fadeIn().css('display', 'flex');
        viewAllButton.text('VIEW LESS');

        viewAllButton.click(function () {
            $('.gmu-testListItem:nth-child(n+6)').fadeOut();
            viewAllButton.text('VIEW ALL');
        });
    });
});
$(document).ready(function () {


    //---------------- Mock Tab Switching ---------------//
    $(document).on('click', 'body .gmu-mock-listItemShow', function () {
        $('.gmu-mock-listItem').removeClass('active');
        $('.gmu-mock-tabContent').removeClass('active');
        $(this).addClass('active');
        var tabContentId = parseInt($(this).data('tab'));
        var articleID = $(this).data('entityid');
        var subtopicID = $(this).data('subtopic-id');
        $('#content-' + tabContentId).addClass('active');
        $('.attempt-more').show();
        if ($('.gmu-mock-list li:last-child').hasClass('active')) {
            $('.attempt-more').hide();

        }
        if (!$(this).hasClass('noAjax') && tabContentId >= 2) {
            var isInstruction = $(this).data('intruction_id');
            $.ajax({
                type: "POST",
                url: "/ajax/get-ques-ans",
                data: { articleID: articleID, tabContentId: tabContentId, subtopicID: subtopicID, isInstruction: isInstruction },
                dataType: "json",
                success: function (response) {
                    $("#content-" + response.tabContentId).append(response.html);
                    $("#tab-" + response.tabContentId).addClass('noAjax');
                },
                failure: function (error) {

                }
            });
        }



    });
    //--------------- Explanation Container -------------//
    $(document).on('click', 'body .showExplanation', function () {
        $(this).closest('.gmu-mock-questionContent').find('.gmu-mock-explanationCtn').fadeIn();
    });
    //--------------- --- Select Action ---------------//
    $(document).on('click', 'body .gmu-mock-optionListItem', function () {

        if ($(this).hasClass('notLogin')) {
            $(this).siblings().removeClass('selected');
            $(this).addClass('selected');
            return false;
        } else {
            $(this).siblings().removeClass('selected');
            $(this).addClass('selected');
        }


        var $questionContent = $(this).closest('.gmu-mock-questionContent')
        var correctAnswer = $questionContent.find('.gmu-mock-optionList').data('answer');
        if ($(this).hasClass('isTrue')) {
            return false;
        }
        $(this).addClass('selected');
        var $selectedOption = $questionContent.find('.gmu-mock-optionListItem.selected');
        $questionContent.find('.gmu-mock-optionListItem').removeClass('selected');
        $questionContent.find('.gmu-mock-optionListItem').removeClass('isTrue isFalse');
        console.log($selectedOption.data('value'));
        if ($selectedOption.length > 0) {
            var selectedValue = $selectedOption.data('value');
            if (selectedValue === correctAnswer) {
                $selectedOption.addClass('isTrue');

            } else {
                $selectedOption.addClass('isFalse');
                $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').addClass('isTrue');

            }
        } else {

            $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').addClass('isTrue');
        }
        var IsLoginCheck = isAuthentiCate();
        $questionContent.find('.showAnswer').hide();
        if (IsLoginCheck != true) {
            $('.showAnswer').addClass('js-open-lead-form-new');
            $('.showAnswer').addClass('js-open-lead-form-new');
            $('.gmu-mock-optionListItem').addClass('notLogin');
            $('.gmu-mock-actionExplain').addClass('afterLogin');
            $questionContent.find('.showAnswer').removeClass('js-open-lead-form-new');
            $questionContent.find('.showExplanation').removeClass('js-open-lead-form-new');
            $questionContent.find('.showAnswer').addClass('afterLogin');
            $questionContent.find('.gmu-mock-optionListItem').removeClass('notLogin');
            const userData = {
                questionData: [
                    { questionID: $selectedOption.attr('question-id'), answer: $selectedOption.data('answer'), dataValue: $selectedOption.data('value') },
                ]
            };
            console.log(userData);
            localStorage.setItem("userData", JSON.stringify(userData));
        } else {

            saveUserQuesAns($selectedOption.attr('question-id'), $selectedOption.data('answer'), $selectedOption.data('value'));
            $('.showAnswer').removeClass('js-open-lead-form-new');
            $('.showAnswer').removeClass('js-open-lead-form-new');
            $('.gmu-mock-optionListItem').removeClass('notLogin');
        }

    });
    $(document).on('click', 'body .afterLogin', function () {
        var IsLoginCheck = isAuthentiCate();
        var questionID = '';
        var answer = '';
        var answerValue = '';
        var $questionContent = $(this).closest('.gmu-mock-questionContent')
        var correctAnswer = $questionContent.find('.gmu-mock-optionList').data('answer');
        var $selectedOption = $questionContent.find('.gmu-mock-optionListItem.selected');
        if ($questionContent.find('.gmu-mock-optionListItem').hasClass('notLogin')) {
            return false;
        }

        $questionContent.find('.gmu-mock-optionListItem').removeClass('selected');
        $questionContent.find('.gmu-mock-optionListItem').removeClass('isTrue isFalse');
        if ($selectedOption.length > 0) {
            var selectedValue = $selectedOption.data('value');
            if (selectedValue === correctAnswer) {
                $selectedOption.addClass('isTrue');
            } else {
                $selectedOption.addClass('isFalse');
                $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').addClass('isTrue');
            }
        } else {
            $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').addClass('isTrue');
            var questionID = $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').attr('question-id');
            var answer = $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').data('answer');
            var answerValue = $questionContent.find('.gmu-mock-optionListItem[data-value="' + correctAnswer + '"]').data('value');
        }
        /*********************************/
        if (!$(this).hasClass('showExplanation')) {
            $(this).hide();
        }
        if (IsLoginCheck != true) {
            $('.showAnswer').not(this).addClass('js-open-lead-form-new');
            $('.showAnswer').not(this).addClass('js-open-lead-form-new');
            $('.gmu-mock-optionListItem').addClass('notLogin');
            $questionContent.find('.showAnswer').removeClass('js-open-lead-form-new');
            $questionContent.find('.showExplanation').removeClass('js-open-lead-form-new');
            $questionContent.find('.showAnswer').addClass('afterLogin');
            $questionContent.find('.gmu-mock-optionListItem').removeClass('notLogin');
            $questionContent.find('.showExplanation').addClass('afterLogin');
            console.log($selectedOption);

            const userData = {
                questionData: [
                    { questionID: questionID, answer: answer, dataValue: answerValue },
                ]
            };
            localStorage.setItem("userData", JSON.stringify(userData));
        } else {

            saveUserQuesAns(questionID, answer, answerValue);
            $('.showAnswer').removeClass('js-open-lead-form-new');
            $('.showAnswer').removeClass('js-open-lead-form-new');
            $('.gmu-mock-optionListItem').removeClass('notLogin');
        }
        /*********************************/

    });
});
function callerIcon(action) {
    if (action === 1) {
        $(".container .stickyNavCls, .quickLinks, .sidebarAds").css("z-index", "0");
        $("#overlay").addClass("active").fadeIn();
        $("#callerModal").addClass("active").fadeIn();
    } else if (action === 2) {
        $(".container .stickyNavCls, .quickLinks, .sidebarAds").css("z-index", "10");
        $("#overlay").removeClass("active").fadeOut();
        $("#callerModal").removeClass("active").fadeOut();
    }
}
webengage.track("user_land_on_page", {
    "entity": gmu.config.entity,
    "entity_name": gmu.config.entity_name,
    "page_url": gmu.config.page_url,
    "entity_id": gmu.config.entity_id
});
if (gmu.config.entity == 'college-listing') {
    webengage.track("college_listing_page_viewed", {
        "entity": gmu.config.entity,
        "entity_name": gmu.config.entity_name,
        "page_url": gmu.config.page_url,
        "entity_id": gmu.config.entity_id,
        "city": gmu.config.cityWebengage,
        "state": gmu.config.stateWebengage,
        "strean": gmu.config.sponsor_params.stream ? gmu.config.sponsor_params.stream : '',
        "level": gmu.config.levelWebengage,
        "exam": gmu.config.examWebengage
    });
}else{
    if(gmu.config.entity=='college'){
        webengage.track("college_detailed_view", {
            "entity": gmu.config.entity,
            "entity_name": gmu.config.entity_name,
            "page_url": gmu.config.page_url,
            "college_id": gmu.config.entity_id,
            "city": gmu.config.cityWebengage,
            "state": gmu.config.stateWebengage,
            "strean": gmu.config.streamWebengage,
            "level": gmu.config.levelWebengage,
            "exam": gmu.config.examWebengage
        });
    }else if(gmu.config.entity=='course'){
        webengage.track("course_shortlist", {
            "entity": gmu.config.entity,
            "entity_name": gmu.config.entity_name,
            "page_url": gmu.config.page_url,
            "course_id": gmu.config.entity_id,
            "city": gmu.config.cityWebengage,
            "state": gmu.config.stateWebengage,
            "strean": gmu.config.streamWebengage,
            "level": gmu.config.levelWebengage,
            "exam": gmu.config.examWebengage
        });
    }else if(gmu.config.entity=='exam'){
        webengage.track("exam_page_viewed", {
            "entity": gmu.config.entity,
            "entity_name": gmu.config.entity_name,
            "page_url": gmu.config.page_url,
            "entity_id": gmu.config.entity_id,
            "city": gmu.config.cityWebengage,
            "state": gmu.config.stateWebengage,
            "strean": gmu.config.streamWebengage,
            "level": gmu.config.levelWebengage,
            "exam": gmu.config.examWebengage
        });
    }else if(gmu.config.entity=='college-admissions'){
        webengage.track("admission_page_viewed", {
            "entity": gmu.config.entity,
            "entity_name": gmu.config.entity_name,
            "page_url": gmu.config.page_url,
            "entity_id": gmu.config.entity_id,
            "city": gmu.config.cityWebengage,
            "state": gmu.config.stateWebengage,
            "strean": gmu.config.streamWebengage,
            "level": gmu.config.levelWebengage,
            "exam": gmu.config.examWebengage
        });

    }else{
        webengage.track("page-viewed", {
            "entity": gmu.config.entity,
            "entity_name": gmu.config.entity_name,
            "page_url": gmu.config.page_url,
            "entity_id": gmu.config.entity_id,
            "city": gmu.config.cityWebengage,
            "state": gmu.config.stateWebengage,
            "strean": gmu.config.streamWebengage,
            "level": gmu.config.levelWebengage,
            "exam": gmu.config.examWebengage
        });
    }
    
}
function menuNavigate(url,menuName,menuSubCategory,pageName){
    webengage.track("navigation_bar_element_clicked", {
        "menu_category": menuName,
        "menu_item_subcategory": menuSubCategory,
        "page_url": url,
        "clicked page": pageName
    });
}

$("body").on('click','.submenulist',function(e){
  e.preventDefault();
  webengage.track("navigation_bar_element_clicked", {
    "menu_category": $(this).parent().parent().children(":first").text(),
    "menu_item_subcategory":  $(this).text(),
    "page_url":  $(this).attr('href'),
    "clicked page": $(this).text()
 });
  window.location.href =  $(this).attr('href');
});

$("body").on('click','.menu-li-tabs',function(e){
  e.preventDefault();
  webengage.track("navigation_bar_element_clicked", {
    "menu_category":  $(this).text(),
    "menu_item_subcategory":  '',
    "page_url":  $(this).attr('href'),
    "clicked page": $(this).text()
 });
  window.location.href =  $(this).attr('href');
});

$("body").on('click','.submenulistmoremenu',function(e){
    e.preventDefault();
    webengage.track("navigation_bar_element_clicked", {
      "menu_category":  $(this).text(),
      "menu_item_subcategory":  '',
      "page_url":  $(this).attr('href'),
      "clicked page": $(this).text()
   });
   window.location.href =  $(this).attr('href');
});



// $(document).ready(function () {

//     $(document).ready(function () {
//         $(".view-more-btn").click(function () {
//             $(".exam-res-inner-tab").toggleClass("expanded");
//             if ($(".exam-res-inner-tab").hasClass("expanded")) {
//                 $(this).html('View Less <span class="view-less-icon rotateIcon"></span>');
//             } else {
//                 $(this).html('View More <span class="view-less-icon"></span>');
//             }
//         });
//     });
// });

//exam downloadable resource
if (window.location.href.includes("exams/") == true || window.location.href.includes("articles/") == true) {
    // if (window.location.href.includes("exams/") == true) {
    document.addEventListener("DOMContentLoaded", function () {

        //load widget via ajax
        const section = document.querySelector('.exam-download-section');

        // Load widget via AJAX
        if (section && section.dataset.examId && section.dataset.overview == true) {
            const { examId, streamId, entity } = section.dataset;

            $.ajax({
                type: "get",
                url: '/ajax/downloadable-resource',
                data: { examId, streamId, entity, entity_id: gmu.config.entity_id, primary_entity: gmu.config.entity },
                success: function (response) {
                    section.innerHTML = response;
                    initExamDownloadWidget(); // Init after content is rendered
                }
            });
        } else {
            initExamDownloadWidget(); // In case widget is server-rendered
        }

        window.addEventListener("load", () => {
            updateScrollButtons();
            updateDownloadSection();
            updateAllDownloadText();
        });


        // MAIN INITIALIZER
        function initExamDownloadWidget() {
            bindTabClickEvent();
            updateScrollButtons();
            updateDownloadSection();
            updateAllDownloadText();
        }

        // Tabs click event
        function bindTabClickEvent() {
            const examTabs = document.querySelector("ul.tabsExamDownload");
            if (!examTabs) return;

            examTabs.addEventListener("click", function (event) {
                const tab = event.target.closest("li");
                if (!tab) return;

                document.querySelectorAll("ul.tabsExamDownload li").forEach(el => el.classList.remove("current"));
                document.querySelectorAll(".tab-content").forEach(el => el.classList.remove("current"));

                tab.classList.add("current");
                const selectedTabId = tab.getAttribute("data-tab");
                document.getElementById(selectedTabId)?.classList.add("current");

                const examId = selectedTabId.replace("tab-", "");
                resetCategory(examId, true);
                updateScrollButtons();
                updateDownloadSection();
                updateAllDownloadText();
            });
        }
    });

    function resetCategory(examId, isExamSwitch = false) {
        let examContent = document.getElementById(`content-${examId}`);
        let tabContent = document.getElementById(`tab-${examId}`);
        let tab = tabContent ? tabContent.querySelector(".exam-res-inner-tab") : null;

        if (!examContent || !tabContent) return;

        let activeCategory = examContent.querySelector(".category-content.activeCategory");
        let firstCategory = examContent.querySelector(".category-content");

        if (isExamSwitch && activeCategory && firstCategory) {
            activeCategory.classList.remove("activeCategory");
            firstCategory.classList.add("activeCategory");
        }
        let activeTab = tab.querySelector(".category-btn.active");
        let firstTab = tab.querySelector(".category-btn");

        if (isExamSwitch && activeTab && firstTab) {
            activeTab.classList.remove("active");
            firstTab.classList.add("active");
        }
    }

    function showCategory(exam, category) {
        // Remove active class only within the selected tab
        document.querySelectorAll(`#tab-${exam} .category-content`).forEach(el => el.classList.remove("activeCategory"));
        document.getElementById(`content-${exam}-${category}`)?.classList.add("activeCategory");

        // Remove active class only for buttons in the current exam tab
        document.querySelectorAll(`#tab-${exam} .category-btn`).forEach(btn => {
            if (btn.closest(`#tab-${exam}`)) {
                btn.classList.remove("active");
            }
        });

        // Add active class to the selected category button within the current tab
        document.querySelector(`#tab-${exam} .category-btn[onclick*="'${exam}', '${category}'"]`)?.classList.add("active");

        updateScrollButtons();
        updateDownloadSection();
        updateAllDownloadText();
    }

    // Scroll button update
    function updateScrollButtons() {
        document.querySelectorAll(".customSlider").forEach(slider => {
            let cardsContainer = slider.querySelector(".customSliderCards");
            let activeCategory = cardsContainer?.querySelector(".category-content.activeCategory");
            let scrollLeft = slider.querySelector(".scrollLeft");
            let scrollRight = slider.querySelector(".scrollRight");

            if (!cardsContainer || !scrollLeft || !scrollRight) return;

            let hasMultipleCards = activeCategory?.querySelectorAll(".sliderCardInfo").length > 1;

            let canScrollLeft = cardsContainer.scrollLeft > 0;
            let canScrollRight = cardsContainer.scrollLeft + cardsContainer.clientWidth < cardsContainer.scrollWidth - 5;

            scrollLeft.style.display = canScrollLeft ? "block" : "none";
            scrollRight.style.display = canScrollRight ? "block" : "none";

            scrollLeft.classList.toggle("over", !canScrollLeft);
            scrollRight.classList.toggle("over", !canScrollRight);
        });
    }

    function updateAllDownloadText() {
        let activeTab = document.querySelector("ul.tabsExamDownload li.current"); // Find active tab
        let activeTabId = activeTab ? activeTab.getAttribute("data-tab") : '';
        let activeCategory = activeTabId ? document.querySelector("#" + activeTabId + " .category-btn.active")?.textContent.trim() : ''; // Get active category

        if (activeTab && activeCategory) {
            let activeCategoryName = activeCategory == "Previous Year Question Paper" ? "PYQs" : activeCategory;
            let tabName = activeTab.textContent.trim();
            let emailText = `Get all ${activeCategoryName} with ${tabName}s in One Click`;
            document.querySelector(".examPdfonEmail p").textContent = emailText;
        }
    }

    function updateDownloadSection() {
        let activeTab = document.querySelector("ul.tabsExamDownload li.current");

        if (!activeTab) return;

        let activeTabId = activeTab.getAttribute("data-tab");
        let activeCategoryContainer = document.querySelector(`#${activeTabId} .category-content.activeCategory`);

        if (!activeCategoryContainer) return;

        let yearCount = activeCategoryContainer.querySelectorAll(".sliderCardInfo").length;

        // Collect file names from the active category
        let fileElements = activeCategoryContainer.querySelectorAll(".download-btn");
        let fileNames = [];
        let downloadCategory = '';
        let category = '';

        fileElements.forEach(btn => {
            let files = btn.getAttribute("data-files");
            if (files) {
                fileNames.push(...files.split(","));
            }
            category = btn.getAttribute("data-category");

            if (category) {
                downloadCategory = category.replace(/-?\d{4}?/g, "");
            }
        });

        let allFiles = fileNames.join(",");

        // Update the "Get PDF" button's data-files attribute
        let leadButton = document.querySelector(".examPdfonEmail .download-btn");
        if (leadButton) {
            leadButton.setAttribute("data-files", allFiles);
            leadButton.setAttribute("data-category", downloadCategory);
        }

        // Show the section only if more than one year exists
        document.querySelector(".examPdfonEmail").style.display = yearCount > 1 ? "block" : "none";
    }
}

function saveUserQuesAns(questionID, answer, dataValue) {
    var student_id = localStorage.getItem('studentId');
    $.ajax({
        type: "POST",
        url: "/ajax/save-ques-ans",
        data: { questionID: questionID, answer: answer, dataValue: dataValue, student_id: student_id },
        dataType: "json",
        success: function (response) {

        },
        failure: function (error) {

        }
    });
}

isAuthentiCate();
function isAuthentiCate() {
    var isAuthentiCated = false;
    var checkLogin;
    $.ajax({
        type: "POST",
        url: "/ajax/get-user-session",
        data: {},
        dataType: "json",
        async: false,
        success: function (response) {
            checkLogin = response.isLogin;
        },
        failure: function (error) {
            console.log(error);
        }
    });
    if (checkLogin == 'Yes') {
        isAuthentiCated = true;
    }
    return isAuthentiCated;
}

// exam widget
if (window.location.href.includes("exams/")) {
    document.addEventListener("DOMContentLoaded", function () {

        // Load widget via AJAX if necessary
        const section = document.querySelector('#exam_widget_section');
        if (section && section.dataset.examId && section.dataset.overview == true) {
            const { examId } = section.dataset;
            $.ajax({
                type: "get",
                url: '/ajax/exam-widget-section',
                data: { examId },
                success: function (response) {
                    section.innerHTML = response;
                    initExamWidget();  // Reinitialize widget functionality after AJAX content load
                }
            });
        } else {
            initExamWidget();  // Initial widget setup if no AJAX content
        }

    });

    const initExamWidget = () => {
        const $container = $('.glance-options-inner');
        const $ul = $container.find('ul');
        const elementHeight = 40;
        const activeExamPhase = $ul.find('li.active').data('event-type');

        // Add blank items to top and bottom only once
        if (!$ul.find('li.blank').length) {
            $ul.prepend('<li class="blank"></li>');
            $ul.append('<li class="blank"></li>');
        }

        // Highlight a specific index
        const setActiveByIndex = (index) => {
            $ul.find('li').removeClass('active').eq(index).addClass('active');
        };

        // Scroll to specific event phase
        const scrollToPhase = (phase) => {
            const $items = $ul.find('li');
            const index = $items.toArray().findIndex(item => $(item).data('event-type') === phase);

            // If the last event has a tick icon or no events are left, scroll to the first event
            const lastEvent = $ul.find('li').last();
            if (lastEvent.find('.tickicon').length && $ul.find('li').not('.completed').length === 0) {
                setActiveByIndex(0);
                return;
            }

            // If no active phase or no events left, set the first event as active
            if (index === -1 || $ul.find('li').not('.completed').length === 0) {
                setActiveByIndex(0);
                return;
            }

            const scrollOffset = index * elementHeight - ($container.height() / 2) + (elementHeight / 2);
            const maxScroll = $ul[0].scrollHeight - $container.height();
            const finalScroll = Math.max(0, Math.min(scrollOffset, maxScroll));

            $container.animate({ scrollTop: finalScroll }, 300);
            setActiveByIndex(index);
        };

        // Update active index on scroll
        const updateActiveOnScroll = () => {
            const scrollTop = $container.scrollTop();
            const visibleItems = Math.floor($container.height() / elementHeight);
            const centerIndex = Math.floor(scrollTop / elementHeight + visibleItems / 2);
            setActiveByIndex(centerIndex);
        };

        // Handle click events for scroll
        const handleItemClick = (e) => {
            const $target = $(e.target).closest('li');
            if (!$target.length || $target.hasClass('blank')) return;

            const index = $ul.find('li').index($target);
            const scrollOffset = index * elementHeight - ($container.height() / 2) + (elementHeight / 2);
            const maxScroll = $ul[0].scrollHeight - $container.height();
            const finalScroll = Math.max(0, Math.min(scrollOffset, maxScroll));

            // Use requestAnimationFrame for smoother scroll
            requestAnimationFrame(() => {
                $container.animate({ scrollTop: finalScroll }, 300, () => setActiveByIndex(index));
            });
        };

        // Apply event listeners for scroll and click only once after content load
        $ul.on('click', 'li', handleItemClick);
        $container.on('scroll', updateActiveOnScroll);

        // Logic for scrolling to the right phase
        const $allSteps = $('.exam-glance-steps ul li');
        const $lastStep = $allSteps.last();
        if ($lastStep.hasClass('coming-step')) {
            setActiveByIndex(1);  // Only if the LAST step has the 'coming-step' class
        } else {
            scrollToPhase(activeExamPhase);  // Scroll to the active exam phase
        }
    };
}

function readMore(){
    var carLmt = 250;
    // Text to show when text is collapsed
    var readMoreTxt = " ...Read More";
    // Text to show when text is expanded
    var readLessTxt = " Read Less";


    //Traverse all selectors with this class and manupulate HTML part to show Read More
    $(".add-read-more").each(function () {
        var allstr = $(this).text();
        var isReadMore = false;
        if (allstr.length > carLmt) {
           var firstSet = allstr.substring(0, carLmt);
           var secdHalf = allstr.substring(carLmt, allstr.length);
           $(this).children('span').each(function (isReadMore) {
            if($(this).hasClass('read-more')==true){
             isReadMore = true;
            } 
           });
          
           if(isReadMore==false && $.trim(secdHalf)!== ''){
             console.log($(this).attr('data-college-id'));
             var strtoadd = firstSet + "<span class='second-section'>" + secdHalf + "</span><span class='read-more'  title='Click to Show More'>" + readMoreTxt + "</span><span class='read-less' title='Click to Show Less'>" + readLessTxt + "</span>";
             strtoadd = strtoadd.replace('...Read More Read Less','');
             $(this).html(strtoadd);
           }
        }
       
     });

    //Read More and Read Less Click Event binding
 }
 $(document).on("click", ".read-more,.read-less", function () {
    $(this).closest(".add-read-more").toggleClass("show-less-content show-more-content");
 });

  readMore();
