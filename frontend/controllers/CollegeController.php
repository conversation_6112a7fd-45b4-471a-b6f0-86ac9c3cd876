<?php

namespace frontend\controllers;

use common\helpers\CollegeHelper;
use common\services\UserService;
use common\helpers\DataHelper;
use common\helpers\FilterHelper;
use common\models\Category;
use common\models\College;
use common\models\SponsorCollege;
use common\models\Filter;
use common\models\ForumCategory;
use common\models\LeadBucketTagging;
use common\models\CollegeListingPageExperience;
use common\services\CollegeService;
use common\services\QnaCommonService;
use common\services\FaqService;
use common\services\ForumService;
use common\services\ReviewService;
use common\services\SponsorService;
use common\services\v2\NewsService;
use frontend\helpers\Ad;
use frontend\helpers\Url;
use frontend\models\CollegeCourseSearch;
use frontend\models\CollegeSearch;
use frontend\services\AjaxService;
use frontend\services\ArticleService;
use frontend\services\FilterService;
use common\services\ExamService;
use Yii;
use yii\caching\TagDependency;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use common\helpers\ContentHelper;
use common\models\Article;
use common\models\CollegeCiSubpageContent;
use common\models\CollegePiSubpageContent;
use common\models\NewsContent;
use common\models\Qna;
use Error;
use frontend\models\CutOffSearch;
use yii\data\ActiveDataProvider;
use yii\widgets\ListView;
use Exception;
use common\models\ContentTemplate;
use common\models\CourseProgramDates;
use common\models\Stream;
use yii\db\Query;

class CollegeController extends Controller
{
    protected $collegeService;

    protected $faqService;

    protected $forumService;

    protected $ajaxService;

    protected $newsService;

    protected $articleService;

    protected $filterService;

    protected $filterHelper;

    protected $SponsorService;

    protected $reviewService;

    protected $qnaCommonService;

    protected $examService;

    public $pageType;
    public $entityType;
    public $entityCity;

    public $hasNext = true;

    public function __construct(
        $id,
        $module,
        CollegeService $collegeService,
        FaqService $faqService,
        ForumService $forumService,
        NewsService $newsService,
        AjaxService $ajaxService,
        FilterService $filterService,
        ArticleService $articleService,
        FilterHelper $filterHelper,
        SponsorService $SponsorService,
        ReviewService $reviewService,
        QnaCommonService $qnaCommonService,
        ExamService $examService,
        $config = []
    ) {

        $this->collegeService = $collegeService;
        $this->faqService = $faqService;
        $this->newsService = $newsService;
        $this->forumService = $forumService;
        $this->ajaxService = $ajaxService;
        $this->filterService = $filterService;
        $this->articleService = $articleService;
        $this->SponsorService = $SponsorService;
        $this->reviewService = $reviewService;
        $this->qnaCommonService = $qnaCommonService;
        $this->examService = $examService;
        parent::__construct($id, $module, $config);
    }

    public function actionIndex($slug, $page = 'info', $type = '')
    {
        $college = $this->collegeService->getBySlug($slug);
        if (!$college) {
            throw new NotFoundHttpException();
        }

        if ($data = $this->collegeService->getAdTargetData($college)) {
            Ad::setTarget($data);
        }

        $pageList = CollegeHelper::$subPages;
        $this->pageType = $page;
        if ($page == 'facilities' || $page == 'images-videos') {
            $response = DataHelper::trackStudentActivity('college-index', $page, null, null, null);
            $this->entityType = $response['entityType'];
            $this->pageType = $response['pageType'];
        }
        $page = (isset($pageList) && isset($pageList[$page])) ? $page : 'info';

        // @todo remove this function and use inflector

        if ($type != '') {
            return $this->{DataHelper::slugToCamelCase($page)}($college, $type);
        }
        return $this->{DataHelper::slugToCamelCase($page)}($college);
    }

    private function info(College $college)
    {
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'info';
        $funArguments['bodyParams'] = Yii::$app->request->bodyParams;
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getAllContents($college->id);
            if (empty($content['info']->content)) {
                if (!empty($content['info']->template_id)) {
                    $templateContent = $this->collegeService->getContentTemplate($content['info']->template_id, 'info');
                }
            }
            if (!isset($content['info'])) {
                throw new NotFoundHttpException();
            }

            $this->entityCity = $college->city_id ?? '';
            $request = Yii::$app->request;
            $entitySubTypeId = $this->qnaCommonService->getEntitySubTypeId('info', 'college');
            $qna = $this->qnaCommonService->getForumQna($college->id, Qna::ENTITY_COLLEGE, $entitySubTypeId, 'qna_card');

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            $searchModel = new CollegeCourseSearch();
            if ($request->isAjax) {
                $page = false;
            }

            $params = $request->post();

            if ($sortBy = $request->get('sort')) {
                $params['sortBy'] = $sortBy;
            }

            $searchModelData = $searchModel->search($college->id, $params, $request->bodyParams);
            $dataProvider = $searchModelData['dataProvider'];
            $programList = $dataProvider->allModels['programList'];
            $dataProvider->allModels = !empty($dataProvider->allModels['collegeData']) ? ArrayHelper::index($dataProvider->allModels['collegeData'], null, 'course') : [];
            $recognised = $this->collegeService->getFeatureStringValues($features, 'Recognitions');
            $accredited = $this->collegeService->getFeatureStringValues($features, 'Accreditations');
            $approval = $this->collegeService->getFeatureStringValues($features, 'Approvals');
            $type = $this->collegeService->getInstitutionType($features);
            $parentCollege =  $this->collegeService->getParentCollege($college->parent_id);
            $featuresGroup = $this->collegeService->getFeaturesByGroup($features);
            $coursesList = $this->collegeService->getCourseHighlights($college, 'info', ['pgdm', 'diploma', 'certificate', 'fellowship-programme', 'other']);
            $coursesAvgFeesCount = $this->collegeService->getCountCourseAvgFees($college, 'info', ['pgdm', 'diploma', 'certificate', 'fellowship-programme', 'other']);
            $hostelData = $this->collegeService->getCollegeHostelData($college->id);
            $examList = $this->collegeService->getExamList($college->id);
            $collegeStudentStaffData = $this->collegeService->getCollegeStudentStaffData($college->id);
            $facilities = $this->collegeService->getFeatureStringValues($features, 'Facilities');
            $rankList = $this->collegeService->getCollegePressRanking($college->id);

            $templateData = [];
            if (!empty($content['info']->template_id)) {
                $companyList = $this->collegeService->getCompanyList($college->id);

                $templateData['accredited'] = $accredited;
                $templateData['recognised'] = $recognised;
                $templateData['approval'] = $approval;
                $templateData['institute_type'] = $type;
                $templateData['parent_college'] = $parentCollege;
                $templateData['courses'] = $coursesList;
                $templateData['course_count'] = count($coursesList);
                $templateData['program_list'] = $programList;
                $templateData['hostelData'] = $hostelData;
                $templateData['program_count'] = count($programList);
                $templateData['exam'] = $examList;
                $templateData['facilities'] = $facilities;
                $templateData['highlights'] = $features;
                $templateData['companyList'] = $companyList;
                $templateData['enrollment_count'] = $collegeStudentStaffData;
                $templateData['rank_type'] = $rankList;
                $templateData['templateContent'] = empty($templateContent) ? '' : $templateContent;
            }

            $template = $this->collegeService->getContentTemplateFormate($college, $templateData, null, null);
            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'info');

            if (empty($content['info']->author)) {
                $author = $content['info']->defaultuser;
            } else {
                $author = $content['info']->author;
            }

            return   [
                'college' => $college,
                'contentTemplate' => $template,
                'authorDetail' => $author ?? null,
                'profile' => $author->profile ?? null,
                'features' => $features,
                'parentCollege' => $parentCollege,
                'recognised' => $recognised,
                'accredited' => $accredited,
                'approval' => $approval,
                'type' => $type,
                'facilities' => $this->collegeService->getFeatureStringValues($features, 'Facilities'), // todo
                'featuresGroup' => $featuresGroup,
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'content' => $content,
                'state' => $college->city->state ?? null,
                'city' => $college->city ?? null,
                'menus' => $this->collegeService->getMenu($college, 'info', $qna),
                'facilitie' => $content['facilities'] ?? [],
                'ranking' => $rankList,
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'gallery' => $this->collegeService->getGallery($college, 4),
                'collegeExams' => $examList,
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id, ''),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'info', ''),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'other'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'collegeCourses' => [
                    'searchModel' => $searchModel,
                    'dataProvider' => $dataProvider,
                    'totalPrograms' => $searchModelData['totalProgrmsFetched'] ?? 0,
                    'courses' => $coursesList,
                    'programList' => $programList,
                ],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'coursesAvgFeesCount' => $coursesAvgFeesCount,
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'info', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'info'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'forumQna' => $qna,
                'pageName' => 'info',
                'dropdowns' => $collegeSubPageDropdown,
                'collegeStudentStaffData' => $collegeStudentStaffData,
                'studentDiversityData' => $this->collegeService->getCollegeStudentDiversityData($college->id),
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'info'),
            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
        return $this->render('index', $data);
    }

    public function actionCoursesFees($slug)
    {
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'course-fees';
        $funArguments['bodyParams'] = Yii::$app->request->bodyParams;
        $key = $this->getHash($funArguments);
        // $data = Yii::$app->cache->getOrSet($key, function () use ($slug) {
        $page = true;
        $request = Yii::$app->request;
        $college = $this->collegeService->getBySlug($slug);

        if (!$college) {
            throw new NotFoundHttpException();
        }
        $this->entityCity = $college->city_id ?? '';
        $entitySubTypeId = $this->qnaCommonService->getEntitySubTypeId('courses-fees', 'college');
        $qna = $this->qnaCommonService->getForumQna($college->id, Qna::ENTITY_COLLEGE, $entitySubTypeId, 'qna_card');

        // dd($slug);
        if ($request->get('program')) {
            if (substr_count($request->get('program'), '-pi') > 1) {
                throw new NotFoundHttpException();
            }
            return $this->actionProgram($college->slug, $request->get('program'), $queryParam = true);
        }

        if ($request->get('course')) {
            if (empty($request->get('course'))) {
                throw new NotFoundHttpException();
            }
            return $this->actionCourse($college->slug, $request->get('course'));
        }

        $content = $this->collegeService->getContent($college->id, 'courses-fees');
        if (empty($content->content)) {
            if (!empty($content->template_id)) {
                $templateContent = $this->collegeService->getContentTemplate($content->template_id, 'courses-fees');
            }
        }

        if (!$content) {
            throw new NotFoundHttpException();
        }

        $features = $this->collegeService->getFeatures($college);
        $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);

        $searchModel = new CollegeCourseSearch();

        if ($request->isAjax) {
            $page = false;
        }

        $params = $request->post();

        if ($sortBy = $request->get('sort')) {
            $params['sortBy'] = $sortBy;
        }

        $searchModelData = $searchModel->search($college->id, $params, $request->bodyParams, $page);
        $dataProvider = $searchModelData['dataProvider'];
        if ($adTargetKeys = $this->collegeService->getAdTargetData($college)) {
            Ad::setTarget($adTargetKeys);
        }

        $programList = $dataProvider->allModels['programList'];
        $dataProvider->allModels = !empty($dataProvider->allModels['collegeData']) ? ArrayHelper::index($dataProvider->allModels['collegeData'], null, 'course') : [];

        $recognised = $this->collegeService->getFeatureStringValues($features, 'Recognitions');
        $accredited = $this->collegeService->getFeatureStringValues($features, 'Accreditations');
        $approval = $this->collegeService->getFeatureStringValues($features, 'Approvals');
        $type = $this->collegeService->getInstitutionType($features);
        $parentCollege =  $this->collegeService->getParentCollege($college->parent_id);
        $coursesList = $this->collegeService->getCourseHighlights($college, 'courses-fees', ['diploma', 'certificate', 'fellowship-programme', 'other']);
        $coursesAvgFeesCount = $this->collegeService->getCountCourseAvgFees($college, 'courses-fees', ['diploma', 'certificate', 'fellowship-programme', 'other']);
        $hostelData = $this->collegeService->getCollegeHostelData($college->id);
        $examList = $this->collegeService->getExamList($college->id);

        $templateData = [];
        if (!empty($content->template_id)) {
            $companyList = $this->collegeService->getCompanyList($college->id);

            $templateData['accredited'] = $accredited;
            $templateData['recognised'] = $recognised;
            $templateData['approval'] = $approval;
            $templateData['institute_type'] = $type;
            $templateData['parent_college'] = $parentCollege;
            $templateData['courses'] = $coursesList;
            $templateData['course_count'] = count($coursesList);
            $templateData['program_list'] = $programList;
            $templateData['hostelData'] = $hostelData;
            $templateData['program_count'] = count($programList);
            $templateData['exam'] = $examList;
            $templateData['highlights'] = $features;
            $templateData['companyList'] = $companyList;
            $templateData['templateContent'] = empty($templateContent) ? '' : $templateContent;
        }

        $template = $this->collegeService->getContentTemplateFormate($college, $templateData, null, null);

        if (empty($content->author)) {
            $author = $content->defaultuser;
        } else {
            $author = $content->author;
        }
        $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'courses-fees');

        $data =  [
            'college' => $college,
            'city' => $college->city ?? null,
            'state' => $college->city->state ?? null,
            'content' => $content,
            'authorDetail' => $author,
            'contentTemplate' => $template,
            'profile' => $content->author->profile ?? null,
            'menus' => $this->collegeService->getMenu($college, 'info', $qna),
            'parentCollege' => $parentCollege,
            'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
            'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
            'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
            'recognised' => $recognised,
            'accredited' => $accredited,
            'type' => $type,
            'collegeExams' => $examList,
            'approval' => $approval,
            'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
            'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
            'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
            'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
            'qna' => $this->collegeService->getQnaCount($college->slug),
            'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'courses-fees', ''),
            'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
            'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
            'programCount' => $this->collegeService->getCollegeCourses($college->id),
            'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
            // 'coursePage' => $this->collegeService->getCoursePageList($searchModel->courses, $college),
            'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            'recentNews' => $this->newsService->getRecent(10),
            'trendingArticles' => $this->articleService->getTrendings(10),
            'recentArticles' => $this->articleService->getAll(5, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'article' => $this->collegeService->getRelatedArticles($college->id),
            'news' => $this->collegeService->getRelatedNews($college->id),
            'collegeCourses' => [
                'searchModel' => $searchModel,
                'dataProvider' => $dataProvider,
                'totalPrograms' => $searchModelData['totalProgrmsFetched'] ?? 0,
                // 'courses' => $this->collegeService->getCourseHighlights($college, 'courses-fees', ['other']),
                'courses' => $coursesList,
                'programList' => $programList,
            ],
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'courses-fees', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
            'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'courses-fees'), (!empty($college->display_name) ? $college->display_name : $college->name)],
            'dropdowns' => $collegeSubPageDropdown,
            'coursesAvgFeesCount' => $coursesAvgFeesCount,
            'forumQna' => $qna,
            'pageName' => 'courses-fees',
            'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
            'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'courses-fees'),

        ];
        //  }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        if (empty($data)) {
            throw new NotFoundHttpException();
        }

        if (isset($data['program'])) {
            return $data['program'];
        }

        return $this->render('courses-fees', $data);
    }

    private function cutOff(College $college, $type = '')
    {
       
        if (!empty($type)) {
            return $this->actionExamsCutOff($college, $type, 'cut-off');
        }
        $funArguments = func_get_args();
         $funArguments['pageType'] = 'cut-off';
         $key = $this->getHash($funArguments);
        //  $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $request = Yii::$app->request;

            $content = $this->collegeService->getContent($college->id, 'cut-off');
        if (!$content) {
            throw new NotFoundHttpException();
        }

            $searchModel = new CutOffSearch();

            $params = $request->post();
            $searchModelData = $searchModel->search($college->id, $params, $request->bodyParams);

            $dataProvider = $searchModelData['dataProvider'];
            $dataProvider->allModels = !empty($dataProvider->allModels) ? $dataProvider->allModels : [];

            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            $features = $this->collegeService->getFeatures($college);

            $entitySubTypeId = $this->qnaCommonService->getEntitySubTypeId('cut-off', 'college');
            $qna = $this->qnaCommonService->getForumQna($college->id, Qna::ENTITY_COLLEGE, $entitySubTypeId, 'qna_card');

        if (empty($content->author)) {
            $author = $content->defaultuser;
        } else {
            $author = $content->author;
        }
            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'cut-off');

            $data = [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college, 'info', $qna),
                'parentCollege'  => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                // 'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                // 'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                // 'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'collegeExams' => $this->collegeService->getExamList($college->id),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id, ''),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'cut-off', ''),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                // 'cutOffData' => $this->collegeService->getCutOffData($college->old_id),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                // 'courseCutOffData' => $this->collegeService->getCollegeCourseBasedCutOff($college->id),
                'cutOff' => [
                    'searchModel' => $searchModel,
                    'dataProvider' => $dataProvider,
                ],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'cut-off', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'cut-off'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'dropdowns' => $collegeSubPageDropdown,
                'subPageType' => null,
                'forumQna' => $qna,
                'pageName' => 'cut-off',
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'cut-off') ?? [],
            ];
        //  }, 60 * 60 * 2, new TagDependency(['tags' => 'college-cut-off-' . $college->id]));
            return $this->render('cut-off', $data);
    }

    private function admission(College $college, $type = '')
    {
        if (!empty($type)) {
            $type = strtoupper($type);
            return $this->actionSubPageDropDown($college, $type, 'admission');
        }
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'admission';
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();s
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'admission');
            if (!$content) {
                throw new NotFoundHttpException();
            }
            $this->entityCity = $college->city_id ?? '';
            $entitySubTypeId = $this->qnaCommonService->getEntitySubTypeId('admission', 'college');
            $qna = $this->qnaCommonService->getForumQna($college->id, Qna::ENTITY_COLLEGE, $entitySubTypeId, 'qna_card');
            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'admission');

            return  [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college, 'info', $qna),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'collegeCourseHighlights' => $this->collegeService->getCourseHighlights($college),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'admission', ''),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'admissions'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'admission', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'admission'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'forumQna' => $qna,
                'pageName' => 'admission',
                'dropdowns' => $collegeSubPageDropdown,
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'subPageType' => null,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'admission') ?? [],

            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('admissions', $data);
    }

    public function actionReviews($slug)
    {
        $response = DataHelper::trackStudentActivity('college-review');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $college = $this->collegeService->getBySlug($slug);

        if (!$college) {
            throw new NotFoundHttpException();
        }
        $this->entityCity = $college->city_id ?? '';

        if ($adTargetKeys = $this->collegeService->getAdTargetData($college)) {
            Ad::setTarget($adTargetKeys);
        }

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'reviews';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // TagDependency::invalidate(Yii::$app->cache, 'college-reviews-' . $college->id);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($slug, $college) {
            $content = $this->collegeService->getContent($college->id, 'reviews');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $reviews = $this->reviewService->getCollegeReviews($college->id);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);


            if (Url::hasParameters()) {
                throw new NotFoundHttpException();
            }
            // $reviews->prepare();
            // if ((int) Yii::$app->request->get('page') > $reviews->pagination->pageCount) {
            //     $this->redirect(Url::toCollege($slug, 'reviews'), 302);
            //     Yii::$app->end();
            // }
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'reviews');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'reviews' => $reviews,
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'other'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'reviews', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],

            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('reviews', $data);
    }

    private function imagesVideos(College $college)
    {

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'images-videos';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'images-videos');
            $gallery  = $this->collegeService->getGallery($college);
            if (empty($gallery)) {
                throw new NotFoundHttpException();
            }
            $features = $this->collegeService->getFeatures($college);
            $this->entityCity = $college->city_id ?? '';
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);

            if (!$content) {
                throw new NotFoundHttpException();
            }

            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'images-videos');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'collegeImages' => $this->collegeService->getGallery($college),
                'studentReviewImages' => $this->reviewService->getStudentReviewImage($college->id) ?? [],
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'images-videos', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],

            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('gallery', $data);
    }

    private function placements(College $college)
    {

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'placements';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'placements');
            if (!$content) {
                throw new NotFoundHttpException();
            }
            $this->entityCity = $college->city_id ?? '';
            $entitySubTypeId = $this->qnaCommonService->getEntitySubTypeId('placements', 'college');
            $qna = $this->qnaCommonService->getForumQna($college->id, Qna::ENTITY_COLLEGE, $entitySubTypeId, 'qna_card');
            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'placements');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college, 'info', $qna),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'placements', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'placements'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'placementHighlights' => $this->collegeService->getplacementHighlights(8, $college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'placements', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'placements'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'forumQna' => $qna,
                'pageName' => 'placements',
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'placements'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('placements', $data);
    }

    private function scholarships(College $college)
    {

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'scholarships';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'scholarships');
            if (!$content) {
                throw new NotFoundHttpException();
            }
            $this->entityCity = $college->city_id ?? '';
            $entitySubTypeId = $this->qnaCommonService->getEntitySubTypeId('scholarships', 'college');
            $qna = $this->qnaCommonService->getForumQna($college->id, Qna::ENTITY_COLLEGE, $entitySubTypeId, 'qna_card');
            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'scholarships');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college, 'info', $qna),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewService' => $this->reviewService,
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'scholarships', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'scholarships'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'scholarships'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'forumQna' => $qna,
                'pageName' => 'scholarships',
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'courses' => $this->collegeService->getCourseHighlights($college, null, ['other']),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'scholarships', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'scholarships'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'scholarshipCasteInfo' => $this->collegeService->getScholarshipCategoryInfo($college->id),
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'scholarships'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('scholarships', $data);
    }

    private function facilities(College $college)
    {

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'facilities';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'facilities');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            if (empty($content->content)) {
                $templateContent = $this->collegeService->getContentTemplate($content->template_id, 'facilities');
            }
            $entitySubTypeId = $this->qnaCommonService->getEntitySubTypeId('facilities', 'college');
            $qna = $this->qnaCommonService->getForumQna($college->id, Qna::ENTITY_COLLEGE, $entitySubTypeId, 'qna_card');

            $this->entityCity = $college->city_id ?? '';

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);

            $recognised = $this->collegeService->getFeatureStringValues($features, 'Recognitions');
            $accredited = $this->collegeService->getFeatureStringValues($features, 'Accreditations');
            $approval = $this->collegeService->getFeatureStringValues($features, 'Approvals');
            $type = $this->collegeService->getInstitutionType($features);
            $parentCollege =  $this->collegeService->getParentCollege($college->parent_id);
            $featuresGroup = $this->collegeService->getFeaturesByGroup($features);
            $coursesList = $this->collegeService->getCourseHighlights($college);
            $hostelData = $this->collegeService->getCollegeHostelData($college->id);
            $examList = $this->collegeService->getExamList($college->id);
            $collegeStudentStaffData = $this->collegeService->getCollegeStudentStaffData($college->id);
            $facilities = $this->collegeService->getFeatureStringValues($features, 'Facilities');
            // $rankList = $this->collegeService->getCollegePressRanking($college->id);

            $templateData = [];
            if (!empty($content->template_id)) {
                $companyList = $this->collegeService->getCompanyList($college->id);

                $templateData['accredited'] = $accredited;
                $templateData['recognised'] = $recognised;
                $templateData['approval'] = $approval;
                $templateData['institute_type'] = $type;
                $templateData['parent_college'] = $parentCollege;
                $templateData['courses'] = $coursesList;
                $templateData['course_count'] = count($coursesList);
                // $templateData['program_list'] = $programList;
                $templateData['hostelData'] = $hostelData;
                // $templateData['program_count'] = count($programList);
                $templateData['exam'] = $examList;
                $templateData['facilities'] = $facilities;
                $templateData['highlights'] = $features;
                $templateData['companyList'] = $companyList;
                // $templateData['enrollment_count'] = $collegeStudentStaffData;
                // $templateData['rank_type'] = $rankList;
                $templateData['templateContent'] = empty($templateContent) ? '' : $templateContent;
            }
            $template = $this->collegeService->getContentTemplateFormate($college, $templateData, null, null);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'facilities');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'contentTemplate' => $template,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college, 'info', $qna),
                'parentCollege' => $parentCollege,
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $recognised,
                'accredited' => $accredited,
                'type' => $type,
                'approval' => $approval,
                'facilities' => $facilities,
                'featuresGroup' => $featuresGroup,
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'facilities', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'facilities'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'courses' => $coursesList,
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'facilities', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'facilities'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'forumQna' => $qna,
                'pageName' => 'facilities',
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'hostelData' => $hostelData,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'facilities'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('facilities', $data);
    }

    private function result(College $college)
    {
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'result';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'result');
            if (!$content) {
                throw new NotFoundHttpException();
            }
            $this->entityCity = $college->city_id ?? '';

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'result');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'result', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'result'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'result', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'result'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'result'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('result', $data);
    }

    private function ranking(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'ranking';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'ranking');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'ranking');
            $rankData = $this->collegeService->getCollegeRanking($college->id);

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'ranking', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'ranking'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'ranking', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'ranking'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'ranking'),
                'collegeRank' => $rankData['publisher'] ?? [],
                'collegeRankCriteria' => $rankData['criteria'] ?? [],
            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('ranking', $data);
    }

    private function alumni(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'alumni';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'alumni');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'alumni');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'alumni', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'alumni'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'alumni', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'alumni'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'alumni'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('alumni', $data);
    }

    private function hostel(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'hostel';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'hostel');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'hostel');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'hostel', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'hostel'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'hostel', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'hostel'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'hostel'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('hostel', $data);
    }

    private function syllabus(College $college, $type = '')
    {
        if (!empty($type)) {
            return $this->actionSubPageDropDown($college, $type, 'syllabus');
        }
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'syllabus';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'syllabus');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'syllabus');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'syllabus', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'syllabus'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'syllabus', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'syllabus'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'dropdowns' => $collegeSubPageDropdown,
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'subPageType' => null,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'syllabus'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('syllabus', $data);
    }


    private function applicationForm(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'syllabus';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('application-form');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'application-form');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'application-form');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'application-form', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'application-form'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'application-form', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'application-form'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'application-form'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('application-form', $data);
    }



    private function verdict(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'verdict';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'verdict');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'verdict');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'verdict', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'verdict'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'verdict', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'verdict'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'verdict'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('verdict', $data);
    }

    public function actionCourse($slug, $courseSlug)
    {
        $this->pageType = 'courses-fees';
        $college = $this->collegeService->getBySlug($slug);

        if (!$college) {
            throw new NotFoundHttpException();
        }
        $response = DataHelper::trackStudentActivity('course-information');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $this->entityCity = $college->city_id ?? '';

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'course-ci';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // Yii::$app->cache->delete($key);
        $data = Yii::$app->cache->getOrSet($key, function () use ($college, $slug, $courseSlug) {
            $course = $this->collegeService->getCourse($courseSlug);
            if (!$course) {
                throw new NotFoundHttpException();
            }
            $courseList = CollegeService::getCourseList($courseSlug, $college->id);
            $content = CollegeService::getCourseContent($course->id, $college->id);

            if (!$courseList && !$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            $programExams = $this->collegeService->getProgramExamNew('', $college->id, $course->id);

            //cut off data
            $cutOffRequest = Yii::$app->request;
            $cutOffParams = $cutOffRequest->post();
            $searchModelCutOff = new CutOffSearch();
            $searchModelData = $searchModelCutOff->search($college->id, $cutOffParams, $cutOffRequest->bodyParams);
            $dataProviderCutOff = $searchModelData['dataProvider'];
            $dataProviderCutOff->allModels = !empty($dataProviderCutOff->allModels) ? $dataProviderCutOff->allModels : [];

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'course' => $course,
                'content' => $content,
                'getCiSubpage' => !empty($content->id) ? $this->collegeService->checkSubpageContent(CollegeCiSubpageContent::class, $content->id) : [],
                'courseList' => $courseList,
                'dates' => !empty($content->id) ? $this->collegeService->getCourseProgramDates($content->id, CourseProgramDates::ENTITY_TYPE_CI) : [],
                'streamRanks' => $this->collegeService->getStreamBasedPressRanking($course, $college->id),
                'specializationList' => $this->collegeService->getCourseSpecializationList($course->id, $college->id),
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id, $course->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'collegeExams' => $this->collegeService->getCollegeCourseExam($course->id, $college->id),
                'programExams' => $programExams,
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'other', ''),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'other'),
                'feesFormate' => $this->collegeService->formateFee($courseList),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) ? $college->city->id : null, $course->id),
                'courseSpecialization' => $this->collegeService->getParentSpecialization($course),
                // 'specializationByLocation'=> $this->collegeService->getSpecializaionByLocation($course),
                'stateListbyCourse' => $this->collegeService->getStateListByCourse($course, !empty($college->city->state) ? $college->city->state : null),
                'cityListbyCourse' => $this->collegeService->getCityListByCourse($course, !empty($college->city) ? $college->city : null),
                'collegePages' => $this->collegeService->getCollegePage($college->id),
                'otherCourses' => $this->collegeService->getCourseHighlights($college, 'info', ['other'], 'ci', $course->degree),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'courses-fees', ($college->display_name ?? $college->name), $courseSlug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'cutOff' => [
                    'searchModel' => $searchModelCutOff,
                    'dataProvider' => $dataProviderCutOff,
                ],
                'examContentTemplate' => [],
                'collegeNotificationUpdate' => [],
                //'studentDiversityData' => $this->collegeService->getCollegeStudentDiversityData($college->id, $course->id),
            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('college-course', $data);
    }

    public function actionProgram($slug, $programSlug, $queryParam = false)
    {
        if ($queryParam) {
            $progamData = explode('-pi', $programSlug);
            $programSlug = $progamData[0];
            // dd($programSlug);
        } else {
            if (substr_count($programSlug, '-pi') > 0) {
                throw new NotFoundHttpException();
            }
            $progamData = explode('-pi', $programSlug);
            $programSlug = $progamData[0];
        }
        // dd($queryParam);
        if ($queryParam ==  true) {
            $isparam = 1;
        } else {
            $isparam = 0;
        }
        $college = $this->collegeService->getBySlug($slug);

        if (!$college) {
            throw new NotFoundHttpException();
        }

        $program = $this->collegeService->getProgramBySlug($programSlug, $college->id);

        if (!$program) {
            throw new NotFoundHttpException();
        }

        $course = $this->collegeService->getCourse($program->course_slug);

        if (empty($course)) {
            throw new NotFoundHttpException();
        }
        $mysqlProgram = $this->collegeService->getMysqlProgram($program->college_program_id);
        $programContent = $this->collegeService->getProgramContent($program->college_program_id, $college->id);

        if (empty($programContent->content) && !empty($programContent->template_id)) {
            $templateContent = $this->collegeService->getContentTemplate($programContent->template_id, 'pi');
        }

        $features = $this->collegeService->getFeatures($college);
        $recognised = $this->collegeService->getFeatureStringValues($features, 'Recognitions');
        $accredited = $this->collegeService->getFeatureStringValues($features, 'Accreditations');
        $approval = $this->collegeService->getFeatureStringValues($features, 'Approvals');
        $type = $this->collegeService->getInstitutionType($features);
        $parentCollege =  $this->collegeService->getParentCollege($college->parent_id);
        $coursesList = $this->collegeService->getCourseHighlights($college);
        $hostelData = $this->collegeService->getCollegeHostelData($college->id);
        $examList = $this->collegeService->getExamList($college->id);
        $facilities = $this->collegeService->getFeatureStringValues($features, 'Facilities');
        $programExams = $this->collegeService->getProgramExamNew($program->college_program_id);
        $programList = $this->collegeService->getCollegeBasedOnSpecialization($program);
        $rankList = $this->collegeService->getCollegePressRanking($college->id);
        $companyList = $this->collegeService->getCompanyList($college->id, $course->id);
        // dd($companyList);
        $templateData = [];
        if (!empty($programContent->template_id)) {
            $templateData['accredited'] = $accredited;
            $templateData['recognised'] = $recognised;
            $templateData['approval'] = $approval;
            $templateData['institute_type'] = $type;
            $templateData['parent_college'] = $parentCollege;
            $templateData['courses'] = $coursesList;
            $templateData['course_count'] = count($coursesList);
            $templateData['program_list'] = $programList;
            $templateData['hostelData'] = $hostelData;
            $templateData['exam'] = $examList;
            $templateData['program_exam'] =  $programExams;
            $templateData['facilities'] = $facilities;
            $templateData['highlights'] = $features;
            $templateData['companyList'] = $companyList;
            $templateData['templateContent'] = empty($templateContent) ? '' : $templateContent;
            $templateData['rank_type'] = $rankList;
        }

        $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
        $programFees = $this->collegeService->getProgramFeeDetails($program->college_program_id);
        $templateData['programFees'] = $programFees;
        $template = $this->collegeService->getContentTemplateFormate($college, $templateData, $course, $mysqlProgram);

        $response = DataHelper::trackStudentActivity('program-information');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $this->entityCity = $college->city_id ?? '';

        return $this->render('program', [
            'college' => $college,
            'isparam' => $isparam,
            'city' => $college->city ?? null,
            'state' => $college->city->state ?? null,
            'course' => $course,
            'program' => $program,
            'contentTemplate' => $template,
            'mysqlProgram' => $mysqlProgram,
            'getCiSubpage' => !empty($mysqlProgram->id) ? $this->collegeService->checkSubpageContent(CollegePiSubpageContent::class, $mysqlProgram->id) : [],
            'ranking' => $this->collegeService->getCoursePressRanking($course, $college->id),
            'streamRanks' => $this->collegeService->getStreamBasedPressRanking($course, $college->id),
            'dates' => !empty($mysqlProgram->id) ? $this->collegeService->getCourseProgramDates($mysqlProgram->id, CourseProgramDates::ENTITY_TYPE_PI) : [],
            'menus' => $this->collegeService->getMenu($college),
            'parentCollege' => $parentCollege,
            'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
            'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
            'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
            'recognised' => $recognised,
            'accredited' => $accredited,
            'type' => $type,
            'approval' => $approval,
            'featuresGroup' => $this->collegeService->getFeaturesByGroup($features),
            'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
            'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
            'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
            'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
            'qna' => $this->collegeService->getQnaCount($college->slug),
            'collegeExams' => $programExams,
            'programFeeDetails' => $programFees,
            'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
            'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
            'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'other'),
            'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            'recentNews' => $this->newsService->getRecent(10),
            'trendingArticles' => $this->articleService->getTrendings(10),
            'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'article' => $this->collegeService->getRelatedArticles($college->id),
            'news' => $this->collegeService->getRelatedNews($college->id),
            'programData' => $programContent,
            'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
            'specializationBasedState' => $this->collegeService->getSpecializationBasedStateList($program, !empty($college->city) && !empty($college->city->stage) ? $college->city->stage->slug : ''),
            'specializationBasedCity' => $this->collegeService->getSpecializationBasedCityList($program, !empty($college->city) && !empty($college->city->slug) ? $college->city->slug : ''),
            // 'otherSpecialization' => $this->collegeService->getOtherCourseList($course),
            'programList' => $programList,
            // 'otherCourseList' => $this->collegeService->getExcludedCourselist($course, $college),
            // 'otherCourseList' => $this->collegeService->getCourseHighlights($college, 'info', ['other']),
            'collegeList' => $this->collegeService->getSpecializationBasedCollegeList($program),
            'companyList' => $companyList,
            'courseEligibility' => $this->collegeService->getCourseEligibility($course->id),
            'courseCount' => $this->collegeService->getCourseCount($college->id),
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'courses-fees', ($college->display_name ?? $college->name), $programSlug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
            'dropdowns' => $collegeSubPageDropdown,
            'studentDiversityData' => $this->collegeService->getCollegeStudentDiversityData($college->id, $course->id, $program->program_id),
            'examContentTemplate' => [],
            'collegeNotificationUpdate' => [],
            // 'cutOff' => [
            //     'searchModel' => $searchModel,
            //     'dataProvider' => $dataProvider,
            // ],
        ]);
    }

    public function actionCoursesSubpageIndex($slug, $courseProgramSlug, $page, $subpageSlug, $year = null, $id = null)
    {
        if ($id === null) {
            throw new NotFoundHttpException();
        }

        $college = $this->collegeService->getBySlug($slug);

        if (!$college) {
            throw new NotFoundHttpException();
        }

        $features = $this->collegeService->getFeatures($college);
        $modelClass = ($page == 'ci' ? CollegeCiSubpageContent::class : CollegePiSubpageContent::class);
        $content = $this->collegeService->getSubpageContent($modelClass, $id, $subpageSlug, $year);

        if ($page == 'ci') {
            $course = $this->collegeService->getCourse($courseProgramSlug);

            if (!$course) {
                throw new NotFoundHttpException();
            }

            return $this->coursesCiSubpage($college, $features, $course, $content);
        } else {
            $program = $this->collegeService->getProgramBySlug($courseProgramSlug, $college->id);
            $course = $this->collegeService->getCourse($program->course_slug);

            if (!$program) {
                throw new NotFoundHttpException();
            }
            return $this->programCiSubpage($college, $course, $features, $program, $content);
        }
    }

    public function coursesCiSubpage($college, $features, $course, $content)
    {
        if (!$content) {
            throw new NotFoundHttpException();
        }

        $this->pageType = 'courses-fees';
        $response = DataHelper::trackStudentActivity('course-information');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $this->entityCity = $college->city_id ?? '';

        return $this->render('college-course-ci-subpage', [
            'college' => $college,
            'city' => $college->city ?? null,
            'state' => $college->city->state ?? null,
            'course' => $course,
            'content' => $content,
            'streamRanks' => $this->collegeService->getStreamBasedPressRanking($course, $college->id),
            'menus' => $this->collegeService->getMenu($college),
            'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
            'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
            'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
            'type' => $this->collegeService->getInstitutionType($features),
            'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
            'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id, $course->id),
            'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
            'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
            'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
            'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
            'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
            'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
            'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
            'qna' => $this->collegeService->getQnaCount($college->slug),
            'collegeExams' => $this->collegeService->getCollegeCourseExam($course->id, $college->id),
            'programExams' => $this->collegeService->getProgramExamNew('', $college->id, $course->id),
            'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
            'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'other', ''),
            'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
            'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'other'),
            'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) ? $college->city->id : null, $course->id),
            'courseSpecialization' => $this->collegeService->getParentSpecialization($course),
            'stateListbyCourse' => $this->collegeService->getStateListByCourse($course, !empty($college->city->state) ? $college->city->state : null),
            'cityListbyCourse' => $this->collegeService->getCityListByCourse($course, !empty($college->city) ? $college->city : null),
            'otherCourses' => $this->collegeService->getCourseHighlights($college, 'info', ['other'], 'ci', $course->degree),
            'collegePages' => $this->collegeService->getCollegePage($college->id),
            'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            'recentNews' => $this->newsService->getRecent(10),
            'trendingArticles' => $this->articleService->getTrendings(10),
            'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'article' => $this->collegeService->getRelatedArticles($college->id),
            'news' => $this->collegeService->getRelatedNews($college->id),
            'dropdowns' => $this->collegeService->getSubPageDropdown($college),
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'courses-fees', ($college->display_name ?? $college->name), $course->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
        ]);
    }


    public function programCiSubpage($college, $course, $features, $program, $content)
    {
        if (!$content) {
            throw new NotFoundHttpException();
        }

        $response = DataHelper::trackStudentActivity('program-information');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $this->entityCity = $college->city_id ?? '';

        return $this->render('college-program-ci-subpage', [
            'college' => $college,
            'city' => $college->city ?? null,
            'state' => $college->city->state ?? null,
            'course' => $course,
            'content' => $content,
            'program' => $program,
            'menus' => $this->collegeService->getMenu($college),
            'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
            'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
            'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
            'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
            'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
            'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
            'type' => $this->collegeService->getInstitutionType($features),
            'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
            'featuresGroup' => $this->collegeService->getFeaturesByGroup($features),
            'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
            'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
            'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
            'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
            'qna' => $this->collegeService->getQnaCount($college->slug),
            'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
            'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
            'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'other'),
            'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            'recentNews' => $this->newsService->getRecent(10),
            'trendingArticles' => $this->articleService->getTrendings(10),
            'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'article' => $this->collegeService->getRelatedArticles($college->id),
            'news' => $this->collegeService->getRelatedNews($college->id),
            'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
            'specializationBasedState' => $this->collegeService->getSpecializationBasedStateList($program, !empty($college->city) && !empty($college->city->stage) ? $college->city->stage->slug : ''),
            'specializationBasedCity' => $this->collegeService->getSpecializationBasedCityList($program, !empty($college->city) && !empty($college->city->slug) ? $college->city->slug : ''),
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'courses-fees', ($college->display_name ?? $college->name), $program->program_slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
            'dropdowns' => $this->collegeService->getSubPageDropdown($college),
        ]);
    }



    public function actionGetFilterData()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        if (false == (Yii::$app->request->isPost)) {
            throw new NotFoundHttpException();
        }
        $request = Yii::$app->request;
        $funArguments = func_get_args();
        $funArguments[] = $request->post();
        $key = $this->getHash($funArguments);
        $data = Yii::$app->cache->getOrSet($key, function () use ($request) {
            $searchModel = new CollegeSearch();
            $sponsorColleges = $this->SponsorService->getSponsorColleges(($request->bodyParams['CollegeSearch']['stream'] ?? null), ($request->bodyParams['CollegeSearch']['course'] ?? null), ($request->bodyParams['CollegeSearch']['specialization'] ?? null), ($request->bodyParams['CollegeSearch']['state'] ?? null));
            // $liveAppCollegesList = $this->SponsorService->getLAFColleges(($request->bodyParams['CollegeSearch']['stream'] ?? null), ($request->bodyParams['CollegeSearch']['course'] ?? null), ($request->bodyParams['CollegeSearch']['specialization'] ?? null), ($request->bodyParams['CollegeSearch']['state'] ?? null));
            $getFilterPageUrl = $this->filterService->generateUrl($request);
            $sponsorCollegeIds = ArrayHelper::getColumn($sponsorColleges, 'college_id', false);
            $dataProvider = $searchModel->elasticCollegeListing($request->queryParams, $request->bodyParams, $sponsorCollegeIds, 'get-filter-data');
            $totalCount = $dataProvider->getTotalCount();
            $pageSize = $dataProvider->pagination->pageSize;
            $currentPage = $dataProvider->pagination->page;
            $totalPageCount = ceil($totalCount / $pageSize);

            if ($totalCount < 27 || ($currentPage !== 0 && $currentPage == $totalPageCount)) {
                $this->hasNext = false;
            }

            if (!empty($finalUrl = $getFilterPageUrl)) {
                if (strpos($finalUrl, '?')) {
                    $url = explode('?', $finalUrl);
                    $finalUrl = $url[0];
                }

                $specializationFilter = [];
                $finalUrl = ltrim($finalUrl, '/');
                $params = explode('/', $finalUrl);
                $urlPattern = '#colleges-accepting-(.*)-score-in-(.*)#';
                $isExamFilter = preg_match($urlPattern, $params[0], $matches);
                if ($isExamFilter) {
                    $seoInfo = $this->filterService->getSeoInfo($matches[1], ($matches[2] ?? null), $totalCount, true);
                } else {
                    $filter = explode('-', $params[0]);

                    if (count($filter) > 2) {
                        array_pop($filter);
                        $specializationFilter = implode('-', $filter);
                    }
                    $seoInfo = $this->filterService->getSeoInfo((!empty($specializationFilter) ? $specializationFilter : $filter[0]), ($params[1] ?? null));
                }

                $faqs = $this->faqService->getPageLevelFaqDetails(Filter::ENTITY_FILTER, Filter::ENTITY_FILTER_ID, $finalUrl);
            }
            $paramsData =  [
                'state' => $request->bodyParams['CollegeSearch']['state'] ?? null,
                'stream' => $request->bodyParams['CollegeSearch']['stream'] ?? null,
                'course' => $request->bodyParams['CollegeSearch']['course'] ?? null,
                'specialization' => $request->bodyParams['CollegeSearch']['specialization'] ?? null,
                'sponserView' => SponsorCollege::VIEWS_GRID_SLIDER
            ];
            $sponsorCollegeList = $this->SponsorService->getSponsorCollegesGridView($paramsData, $request->bodyParams['page']);
            $streamId = Stream::find()->select('id')->where(['slug' => $searchModel['stream']])->one();
            $streamExams = $this->examService->interestedExams($streamId, '', 8);

            $html = $this->renderPartial('college-filter-ajax-new-design-load', [
                'latestArticles' => $this->articleService->getAll(8, ['category', 'author'], Category::EXCLUDE_CATEGORY, 'published_at'),
                'latestNews' => $this->newsService->getRecent(8),
                'searchModel' => $searchModel,
                'filters' => FilterHelper::sortFilterGroup($this->filterService->getFilterValues($searchModel, $getFilterPageUrl)),
                'selectedFilters' => $searchModel->selectedFilters,
                'dataProvider' => $dataProvider,
                'seoInfo' => $seoInfo,
                'colleges' =>  $dataProvider->getModels()  ?? [],
                // 'liveAppColleges' => $liveAppCollegesList ?? [],
                'page' => ($currentPage + 1),
                'hasNext' => $this->hasNext,
                'collegeNews' => [],
                'totalCount' => $totalCount,
                'faqs' => $faqs ?? [],
                'iRank' => ($request->bodyParams['rank'] != 'undefined') ? $request->bodyParams['rank'] : 1,
                'sort' => ($request->bodyParams['sortBy']) ? $request->bodyParams['sortBy'] : 'rank',
                'isWidget' => $request->bodyParams['isWidget'] ?? '',
                'sponsorCollegeList' => $sponsorCollegeList ?? [],
                'intrestedExams' => $streamExams ?? [],
                'isStream' => $searchModel['stream'] ? 'position' : 'rank',
                'url' => $getFilterPageUrl

            ]);

            return [
                'status' => 200,
                'html' => $html,
                'url' => $getFilterPageUrl,
                'page' => ($currentPage + 1),
                'data' => $seoInfo,
                'hasNext' => $this->hasNext,
                'sponsorParams' => ['state' => $searchModel->state, 'stream' => $searchModel->stream, 'course' => $searchModel->course, 'specialization' => $searchModel->specialization] ?? [],
                // 'sponsorParams' => $sponsorParams ?? ''
                'isWidget' => $request->bodyParams['isWidget'] ?? '',
                'latestArticles' => $this->articleService->getAll(8, ['category', 'author'], Category::EXCLUDE_CATEGORY, 'published_at'),
                'latestNews' => $this->newsService->getRecent(8)
            ];
        }, 60 * 60 * 2, new TagDependency(['tags' => 'ajax-filter']));

        return $data;
    }

    public function actionQnaLandingPage($slug)
    {
        $total_qn_ans = $this->qnaCommonService->getTotalQuestionsAnswers();
        $qnaDetails = $this->qnaCommonService->getQnaDetails($slug, 'college');
        $checkQna = $this->qnaCommonService->checkQnaDetails($qnaDetails[0]['entity_id']);

        if (!isset($qnaDetails->college->display_name)) {
            if (isset($qnaDetails[0]->college->display_name)) {
                $displayName = $qnaDetails[0]->college->display_name;
            } else {
                $clgDetails = College::find()->select(['display_name'])->where(['slug' => $slug])->one();
                $displayName = $clgDetails->display_name;
            }
        } else {
            $displayName = $qnaDetails->college->display_name ?? '';
        }

        $qnaLandingEntityDetails = $this->qnaCommonService->getEntityQnaLandingPage($slug, 'college');
        if (empty($checkQna)) {
            throw new NotFoundHttpException();
        }
        // if ($pageName) {
        //     $qnaDetails = $this->qnaCommonService->getSubpageByEntity('college', $pageName, $qnaDetails);
        // }
        return $this->render('../qna/_qnaLandingPageNew', [
            'qnadetails' => $qnaDetails,
            'qnaDetailsLatest' => $qnaLandingEntityDetails,
            'filterData' => QnaCommonService::$filterData,
            'total_qn_ans' => $total_qn_ans,
            'displayName' => $displayName ?? ''
        ]);
    }


    protected function actionSubPageDropDown($college, $type, $page)
    {
        $subPageSlug = $type;
        $funArguments = func_get_args();
        $funArguments['pageType'] = $page;
        $key = $this->getHash($funArguments);
        if ($page == 'admission') {
            $renderPage = 'admissions';
        } else {
            $renderPage = $page;
        }
        //yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college, $type, $page, $subPageSlug) {
            $content = $this->collegeService->getContent($college->id, $type);
            if (!$content) {
                throw new NotFoundHttpException();
            }
            $this->entityCity = $college->city_id ?? '';
            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (($page == 'syllabus' || $page == 'cut-off') && array_key_exists($page, $collegeSubPageDropdown)) {
                foreach ($collegeSubPageDropdown[$page] as $value) {
                    if ($value['slug'] == $type) {
                        $type = [$value['sub_page'], $value['slug']];
                        break;
                    }
                }
            }
            // if ($page == "cut-off" && in_array($page, $collegeSubPageDropdown)) {

            // }
            $this->entityType = 'college';
            if ($page == 'admission') {
                $newPage = 'admissions';
                $this->pageType = $type;
            } else {
                $newPage = $page;
                $this->pageType = $type[0];
            }
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'collegeCourseHighlights' => $this->collegeService->getCourseHighlights($college),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, $page, $subPageSlug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, $newPage),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, $page, ($college->display_name ?? $college->name), $college->slug, $college->city->name),
                // 'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, $page), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'recentActivity' => null,
                'examContentTemplate' => [],
                'collegeNotificationUpdate' => [],
                'dropdowns' => $collegeSubPageDropdown,
                'subPageType' => $type,
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, $page),

            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render($renderPage, $data);
    }

    private function actionExamsCutOff(College $college, $type, $page = 'cut-off')
    {
       
        $request = Yii::$app->request;

        $content = $this->collegeService->getContent($college->id, $type);
        if (empty($content->content)) {
            $parentContent = $this->collegeService->getContent($college->id, 'cut-off');
        }

        if (!$content) {
            throw new NotFoundHttpException();
        }

        $searchModel = new CutOffSearch();

        $exam = $this->collegeService->getExam($type);

        $params = $request->post();
        $params['CutOffSearch']['exam'] = $exam->id;
        $searchModelData = $searchModel->searchExam($college->id, $params, $request->bodyParams, $exam->id);

        $dataProvider = $searchModelData['dataProvider'];
        $dataProvider->allModels = !empty($dataProvider->allModels) ? $dataProvider->allModels : [];

        $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
        if (($page == 'cut-off') && array_key_exists($page, $collegeSubPageDropdown)) {
            foreach ($collegeSubPageDropdown[$page] as $value) {
                if ($value['slug'] == $type) {
                    $type = [$value['sub_page'], $value['slug']];
                    break;
                }
            }
        }

        $features =  $this->collegeService->getFeatures($college);
        $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'cut-off');

        $data = [
            'college' => $college,
            'city' => $college->city ?? null,
            'state' => $college->city->state ?? null,
            'content' => $content ?? null,
            'parentContent' => $parentContent ?? null,
            'authorDetail' => $content->author  ?? null,
            'profile' => $content->author->profile ?? null,
            'menus' => $this->collegeService->getMenu($college),
            'parentCollege'  => $this->collegeService->getParentCollege($college->parent_id ?? $college->id),
            'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
            'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
            'type' => $this->collegeService->getInstitutionType($features),
            'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
            'collegeExams' => $this->collegeService->getExamList($college->id),
            'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id, ''),
            'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
            'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
            'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
            'qna' => $this->collegeService->getQnaCount($college->slug),
            'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
            'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
            'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'cut-off', $type),
            'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
            'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
            'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
            'recentNews' => $this->newsService->getRecent(10),
            'trendingArticles' => $this->articleService->getTrendings(10),
            'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
            'article' => $this->collegeService->getRelatedArticles($college->id),
            'news' => $this->collegeService->getRelatedNews($college->id),
            'cutOff' => [
                'searchModel' => $searchModel,
                'dataProvider' => $dataProvider,
            ],
            'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'cut-off', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
            // 'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'cut-off'), (!empty($college->display_name) ? $college->display_name : $college->name)],
            'recentActivity' => [],
            'examContentTemplate' => [],
            'collegeNotificationUpdate' => [],
            'dropdowns' => $collegeSubPageDropdown,
            'subPageType' => $type,
            'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
            'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, $page),


        ];

        return $this->render('cut-off', $data);
    }

    public function actionCompareCollege()
    {
        $isMobile = \Yii::$app->devicedetect->isMobile();

        if (Yii::$app->request->isAjax) {
            \Yii::$app->response->format = Response::FORMAT_JSON;
            $request = Yii::$app->request->post();

            $college = $this->collegeService->getInfoForCollegeCompare(
                [
                    ['college_id' => $request['college_id_one'], 'program_id' => $request['program_name_one']],
                    ['college_id' => $request['college_id_two'], 'program_id' => $request['program_name_two']]
                ]
            );

            if ($isMobile) {
                $html = $this->renderPartial('partials/college_compare_detail_mobile', ['college' => $college]);
            } else {
                $html = $this->renderPartial('partials/college_compare_detail_desktop', ['college' => $college]);
            }

            return [
                'status' => 200,
                'html' => $html,
            ];
        }

        if ($isMobile) {
            return $this->render('college_compare_mobile');
        } else {
            return $this->render('college_compare_desktop');
        }
    }

    public function actionNews($slug)
    {
        $lang_code = DataHelper::getLangId();
        $request = Yii::$app->request->get();
        $college = $this->collegeService->getBySlug($slug);
        if (isset($request['requestType']) && $request['requestType'] == 'button') {
            if (!empty($request['tab']) && $request['tab'] == 'articles') {
                $data = $this->collegeService->getArticlesByCollege($college->id, $request['offset']);
                $file = 'partials/_collegeArticlesCard';
            }

            if (!empty($request['tab']) && $request['tab'] == 'news') {
                $data = $this->collegeService->getNewsByCollege($college->id, $request['offset']);
                $file = 'partials/_collegeNewsCard';
            }

            if (!empty($data['query']->all())) {
                return $this->renderPartial($file, ['models' => $data['query']->all(), 'count' => $request['tab'] == 'articles' ? $data['totalArticlesCount'] : $data['totalNewsCount']]);
            }
        }

        $funArguments = func_get_args();
        $funArguments['pageType'] = 'news';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();

        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'news');
            if (!$content) {
                throw new NotFoundHttpException();
            }
            $this->entityCity = $college->city_id ?? '';
            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);

            $newsData = $this->collegeService->getNewsByCollege($college->id);
            $articlesData = $this->collegeService->getArticlesByCollege($college->id);

            $categories = ['articles', 'news'];
            foreach ($categories as $key => $value) {
                if ($value == 'articles' && isset($articlesData['query']) && $articlesData['totalArticlesCount'] < 4) {
                    unset($categories[$key]);
                } else if ($value == 'news' && isset($newsData['query']) && $newsData['totalNewsCount'] < 4) {
                    unset($categories[$key]);
                }
            }

            $collegeArticlesNews = [
                'categories' => $categories,
                'articles' => $articlesData['query']->all() ?? '',
                'totalArticlesCount' => $articlesData['totalArticlesCount'],
                'news' => $newsData['query']->all() ?? '',
                'totalNewsCount' => $newsData['totalNewsCount'],
            ];

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $content->author ?? null,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'news', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'news'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'dropdowns' => $collegeSubPageDropdown,
                'collegeArticlesNews' => $collegeArticlesNews,
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'news'),

            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
        return $this->render('news', $data);
    }

    public function actionAdmissionListing()
    {
        $collegeList = $this->collegeService->getAdmissionColleges();
        if (empty($collegeList)) {
            throw new NotFoundHttpException();
        }
        $totalCount = $collegeList['pagination']->totalCount;
        $pageSize = $collegeList['pagination']->pageSize;
        $currentPage = $collegeList['pagination']->page;
        $totalPageCount = ceil($totalCount / $pageSize);

        if ($totalCount < 21 || ($currentPage !== 0 && $currentPage == $totalPageCount)) {
            $this->hasNext = false;
        }
        $data = [
            'collegeList' => $collegeList['results'],
            'page' => ($currentPage + 1),
            'hasNext' => $this->hasNext,
            'totalCount' => $totalCount,
            'pagination' => $collegeList['pagination'],
        ];
        return $this->render('admission-list', $data);
    }

    public function actionProgramFeesStructure()
    {
        if (Yii::$app->request->isAjax) {
            \Yii::$app->response->format = Response::FORMAT_JSON;
            $program = Yii::$app->request->get('program');
            $collegeProgramId = Yii::$app->request->get('collegeProgramId');

            $college = (new Query())
                ->select(['c.name', 'c.display_name'])
                ->from(['college c'])
                ->innerJoin('college_program cp', 'c.id = cp.college_id')
                ->where(['cp.id' => $collegeProgramId])
                ->one();

            $programFees = $this->collegeService->getProgramFeeDetails($collegeProgramId);

            if (!empty($programFees)) {
                return $this->renderPartial('/college/partials/_detailed_fee_breakup', [
                    'college' => $college['display_name'] ?? $college['name'],
                    'program' => $program ?? '',
                    'programFeeDetails' => $programFees,
                ]);
            }
        } else {
            throw new Exception('Invalid Request');
        }
    }

    public function actionCourseFeesStructure()
    {
        if (Yii::$app->request->isAjax) {
            \Yii::$app->response->format = Response::FORMAT_JSON;
            $courseID = Yii::$app->request->get('courseID');
            $collegeID = Yii::$app->request->get('collegeID');

            $programFees = $this->collegeService->getCourseSpecializationList((int)$courseID, (int)$collegeID);
            $college = (new Query())
                ->select(['c.*'])
                ->from(['college c'])
                ->where(['c.id' => $collegeID])
                ->one();
            $course = (new Query())
                ->select(['c.name', 'c.short_name'])
                ->from(['course c'])
                ->where(['c.id' => $courseID])
                ->one();

            if (!empty($programFees)) {
                return $this->renderPartial('/college/partials/specialization-course-fees', [
                    'college' => $college,
                    'specializationList' => $programFees,
                    'course' => $course,
                ]);
            }
        } else {
            throw new Exception('Invalid Request');
        }
    }

    public function actionAllColleges($location = null)
    {

        $currentUrl = Yii::$app->request->url;
        $request = Yii::$app->request;
        $funArguments = func_get_args();
        $funArguments[] = $request->get();
        $funArguments['current_url'] = $currentUrl;
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        //    Yii::$app->cache->delete($key);
        $data = Yii::$app->cache->getOrSet($key, function () use ($location, $currentUrl, $request) {

            $searchModel = new CollegeSearch();
            $queryParams =  $request->queryParams;
            $dataProvider  =  $searchModel->elasticCollegeListing($queryParams, $request->bodyParams, [], 'all-colleges');
            if (empty($dataProvider->getModels())) {
                throw new NotFoundHttpException();
            }
            $finalCollegeList = $dataProvider->getModels();
            $faqPage = !empty($location) ? 'all-colleges/' . $location : 'all-colleges';
            $totalCount = $dataProvider->getTotalCount();
            $pageSize = $dataProvider->pagination->pageSize;
            $currentPage = $dataProvider->pagination->page;
            $totalPageCount = ceil($totalCount / $pageSize);
            $seoInfo = $this->filterService->getSeoInfo('', $location, $totalCount, false);
            $faqs = $this->faqService->getPageLevelFaqDetails(Filter::ENTITY_FILTER, Filter::ENTITY_FILTER_ID, $faqPage);
            if ($totalCount < 27 || ($currentPage !== 0 && $currentPage == $totalPageCount)) {
                $this->hasNext = false;
            }
            $paramsData =  [
                'state' => $searchModel->state ?? '',
                'stream' => $searchModel->stream ?? '',
                'course' => $searchModel->course  ?? '',
                'specialization' => $searchModel->specialization  ?? '',
                'sponserView' => SponsorCollege::VIEWS_GRID_SLIDER
            ];
            $sponsorCollegeList = $this->SponsorService->getSponsorCollegesGridView($paramsData);
            $streamId = Stream::find()->select('id')->where(['slug' => $searchModel['stream']])->one();
            // $streamExams = $this->examService->interestedExams($streamId, '', 8);

            return [
                'latestArticles' => $this->articleService->getAll(8, ['category', 'author'], Category::EXCLUDE_CATEGORY, 'published_at'),
                'latestNews' => $this->newsService->getRecent(8),
                'searchModel' => $searchModel,
                'filters' => FilterHelper::sortFilterGroup($this->filterService->getFilterValues($searchModel, $currentUrl)),
                'selectedFilters' => $searchModel->selectedFilters,
                'dataProvider' => $dataProvider,
                'seoInfo' => $seoInfo,
                'colleges' => $finalCollegeList ?? [],
                //  'sponsorParams' => ['state' => $searchModel->state],
                'sponsorParams' => ['state' => $searchModel->state, 'stream' => $searchModel->stream, 'course' => $searchModel->course, 'specialization' => $searchModel->specialization] ?? [],
                'page' => ($currentPage + 1),
                'hasNext' => $this->hasNext,
                'collegeNews' => [],
                'totalCount' => $totalCount,
                'faqs' => $faqs ?? [],
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGE_LISTING, 'college-listing', '', '', '', ''),
                'sort' => 'rank',
                'isWidget' => true,
                'sponsorCollegeList' => $sponsorCollegeList ?? [],
                'intrestedExams' => [],
                'isStream' => 'rank',

            ];
        }, 60 * 60 * 24 * 90, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        if (is_array($data) === false) {
            throw new NotFoundHttpException('Page not found.');
        }

        $response = DataHelper::trackStudentActivity('all-colleges', null, $location, $data);
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];

        if ($request->isAjax) {
            \Yii::$app->response->format = Response::FORMAT_JSON;
            $html = $this->renderPartial(
                'college-filter-ajax-load-new-design',
                $data
            );
            $data = [
                'status' => 200,
                'html' => $html,
                'url' => Yii::$app->request->getPathInfo(),
                'page' => $data['page'],
                'isWidget' => true
                // 'data' => $seoInfo,
                //  'hasNext' => $this->hasNext,
                // 'sponsorParams' => ['state' => $searchModel->state, 'stream' => $searchModel->stream, 'course' => $searchModel->course, 'specialization' => $searchModel->specialization] ?? [],
                // 'sponsorParams' => $sponsorParams ?? ''
            ];
            return $data;
        } else {
            return $this->render('college-filter-ajax-new-design', $data);
        }
    }


    public function actionCollegeFilter($filter, $location = null)
    {

        $request = Yii::$app->request;
        $currentUrl = $request->url;
        $funArguments = func_get_args();
        $funArguments[] = $request->get();
        $funArguments['current_url'] = $currentUrl;
        $key = $this->getHash($funArguments);
        //Yii::$app->cache->delete($key);
        $data = Yii::$app->cache->getOrSet($key, function () use ($location, $currentUrl, $request, $filter) {

            $searchModel = new CollegeSearch();
            $dataProvider = $searchModel->elasticCollegeListing($request->queryParams, $request->bodyParams, [], 'college-filter');
            if (empty($dataProvider->getModels())) {
                throw new NotFoundHttpException();
            }
            $finalCollegeList = $dataProvider->getModels();
            $totalCount = $dataProvider->getTotalCount();
            $pageSize = $dataProvider->pagination->pageSize;
            $currentPage = $dataProvider->pagination->page;
            $totalPageCount = ceil($totalCount / $pageSize);
            $faqPage = !empty($location) ? $filter . '-colleges/' . $location : $filter;
            $seoInfo = $this->filterService->getSeoInfo($filter, $location, $totalCount, false);
            $faqs = $this->faqService->getPageLevelFaqDetails(Filter::ENTITY_FILTER, Filter::ENTITY_FILTER_ID, $faqPage);
            if ($totalCount < 27 || ($currentPage !== 0 && $currentPage == $totalPageCount)) {
                $this->hasNext = false;
            }

            $setTageting = [];

            if (!empty($request->queryParams) && !empty($searchModel)) {
                $queryParams = $request->queryParams;

                if (!empty($searchModel->stream) && !empty($queryParams['filter'])) {
                    $setTageting['discipline'] = $queryParams['filter'];
                }

                if (!empty($searchModel->course) && !empty($queryParams['filter'])) {
                    $setTageting['courses'] = $queryParams['filter'];
                }

                if ($location !== null && !empty($searchModel->state)) {
                    $setTageting['State'] = $searchModel->state;
                }

                if ($location !== null && !empty($searchModel->city)) {
                    $setTageting['City'] = is_array($searchModel->city) ? $searchModel->city[0] : $searchModel->city;
                }
            }

            $paramsData =  [
                'state' => $searchModel->state,
                'stream' => $searchModel->stream,
                'course' => $searchModel->course,
                'specialization' => $searchModel->specialization
            ];
            $sponsorCollegeList = $this->SponsorService->getSponsorCollegesGridView($paramsData);
            $streamId = Stream::find()->select('id')->where(['slug' => $searchModel['stream']])->one();
            $streamExams = $this->examService->interestedExams($streamId, '', 8);

            return [
                'latestArticles' => $this->articleService->getAll(8, ['category', 'author'], Category::EXCLUDE_CATEGORY, 'published_at'),
                'latestNews' => $this->newsService->getRecent(8),
                'searchModel' => $searchModel,
                'filters' => FilterHelper::sortFilterGroup($this->filterService->getFilterValues($searchModel, $currentUrl)),
                'selectedFilters' => $searchModel->selectedFilters,
                'dataProvider' => $dataProvider,
                'seoInfo' => $seoInfo,
                'colleges' =>  $dataProvider->getModels()  ?? [],
                'sponsorParams' => ['state' => $searchModel->state, 'stream' => $searchModel->stream, 'course' => $searchModel->course, 'specialization' => $searchModel->specialization] ?? [],
                // 'liveAppColleges' => $liveAppCollegesList ?? [],
                'page' => ($currentPage + 1),
                'hasNext' => $this->hasNext,
                'collegeNews' => [],
                'totalCount' => $totalCount,
                'faqs' => $faqs ?? [],
                'currentUrl' => Yii::$app->request->getPathInfo(),
                'setTageting' => $setTageting,
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGE_LISTING, 'college-listing', '', '', '', ''),
                'sort' => 'rank',
                'isWidget' => true,
                'sponsorCollegeList' => $sponsorCollegeList ?? [],
                'intrestedExams' => $streamExams ?? [],
                'isStream' => $searchModel['stream'] ? 'position' : 'rank',
                'filterMapping' => $this->collegeService->getCollegeListingMapping(Yii::$app->request->getPathInfo()) ?? [],
            ];
        }, 60 * 60 * 90, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        if (!empty($data['setTageting'])) {
            Ad::setTargetAllColleges($data['setTageting']);
        }
        if (is_array($data) === false) {
            throw new NotFoundHttpException('Page not found.');
        }


        $response = DataHelper::trackStudentActivity('college-filter', null, $location, $data, $filter);
        $this->entityType = isset($response['entityType']) && !empty($response['entityType']) ? $response['entityType'] : '';
        $this->pageType = isset($response['pageType']) && !empty($response['pageType']) ? $response['pageType'] : '';
        if ($request->isAjax) {
            \Yii::$app->response->format = Response::FORMAT_JSON;
            $html = $this->renderPartial(
                'college-filter-ajax-load-new-design',
                $data
            );

            $data = [
                'status' => 200,
                'html' => $html,
                'url' => Yii::$app->request->getPathInfo(),
                'isWidget' => true
                // 'page' => ($currentPage + 1),
                //'data' => $seoInfo,
                // 'hasNext' => $this->hasNext,
                //  'sponsorParams' => ['state' => $searchModel->state, 'stream' => $searchModel->stream, 'course' => $searchModel->course, 'specialization' => $searchModel->specialization] ?? [],
                // 'sponsorParams' => $sponsorParams ?? ''
            ];
            return $data;
        } else {
            return $this->render('college-filter-ajax-new-design', $data);
        }
    }

    public function actionSavePageExperience()
    {
        $request = Yii::$app->request;
        $currentUrl = Yii::$app->request->url;
        \Yii::$app->response->format = Response::FORMAT_JSON;
        if ($request->isAjax) {
            $data = Yii::$app->request->post();
            $model = new CollegeListingPageExperience;
            $model->attributes = $data;
            if (!$model->validate()) {
                foreach ($model->errors as $key => $error) {
                    $errors[$key] = $error[0];
                }
                $response = [
                    'success' => false,
                    'message' => $errors ?? 'Something went wrong, please try again!',
                ];
            }
            if ($model->save()) {
                $response = [
                    'success' => true,
                    'message' => ''
                ];
            }
            echo json_encode($response);
            exit;
        }
    }


    // New Page Function
    private function timings(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'timings';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'timings');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'timings');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'timings', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'timings'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'timings', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'timings'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'timings'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('timings', $data);
    }

    private function fees(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'fees';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'fees');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'fees');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'fees', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'fees'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'fees', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'fees'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'fees'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('fees', $data);
    }

    private function eligibility(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'eligibility';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'eligibility');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'eligibility');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'eligibility', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'eligibility'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'eligibility', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'eligibility'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'eligibility'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('eligibility', $data);
    }

    private function averagePackage(College $college)
    {

        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'average-package';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'average-package');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'average-package');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'average-package', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'average-package'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'average-package', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'average-package'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'average-package'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('average-package', $data);
    }

    private function address(College $college)
    {

        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'address';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'address');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'address');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'address', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'address'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'address', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'address'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'address'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('address', $data);
    }

    private function admissionForm(College $college)
    {

        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'admission-form';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'admission-form');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'admission-form');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'admission-form', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'admission-form'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'admission-form', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'admission-form'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'admission-form'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('admission-form', $data);
    }

    private function markSheet(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'marksheet';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'marksheet');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'marksheet');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'marksheet', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'marksheet'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'marksheet', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'marksheet'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'marksheet'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('marksheet', $data);
    }

    private function naccGrade(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'nacc-grade';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'nacc-grade');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'nacc-grade');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'nacc-grade', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'nacc-grade'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'nacc-grade', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'nacc-grade'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'nacc-grade'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('nacc-grade', $data);
    }

    private function dressCode(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'dress-code';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'dress-code');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'dress-code');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'dress-code', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'dress-code'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'dress-code', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'dress-code'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'dress-code'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('dress-code', $data);
    }

    private function refund(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'refund';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'refund');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'refund');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'refund', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'refund'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'refund', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'refund'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'refund'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('refund', $data);
    }

    private function cgpaToPercentage(College $college)
    {
        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'cgpa-to-percentage';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'cgpa-to-percentage');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'cgpa-to-percentage');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'cgpa-to-percentage', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'cgpa-to-percentage'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'cgpa-to-percentage', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'cgpa-to-percentage'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'cgpa-to-percentage'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('cgpa-to-percentage', $data);
    }

    private function previousYearPapers(College $college)
    {

        $this->entityCity = $college->city_id ?? '';
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'previous-year-papers';
        $funArguments['currentPage'] = Yii::$app->request->getQueryParam('page');
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($college) {
            $content = $this->collegeService->getContent($college->id, 'previous-year-papers');
            if (!$content) {
                throw new NotFoundHttpException();
            }

            $features = $this->collegeService->getFeatures($college);
            $collegeSubPageDropdown = $this->collegeService->getSubPageDropdown($college);
            if (empty($content->author)) {
                $author = $content->defaultuser;
            } else {
                $author = $content->author;
            }

            $examContentTemplate = $this->collegeService->getExamContentTemplate($college, 'previous-year-papers');

            return [
                'college' => $college,
                'city' => $college->city ?? null,
                'state' => $college->city->state ?? null,
                'content' => $content,
                'authorDetail' => $author,
                'profile' => $content->author->profile ?? null,
                'menus' => $this->collegeService->getMenu($college),
                'parentCollege' => $this->collegeService->getParentCollege($college->parent_id),
                'nearByCollege' => $this->collegeService->getCollegesByCity($college->city_id, 4, [$college->id]),
                'affiliatedCollege' => $this->collegeService->getAffiliatedColleges($college->id, 10, $college->parent_id),
                'popularCollege' => $this->collegeService->getPopularColleges(8, $college->id, $college->position),
                'recognised' => $this->collegeService->getFeatureStringValues($features, 'Recognitions'),
                'accredited' => $this->collegeService->getFeatureStringValues($features, 'Accreditations'),
                'type' => $this->collegeService->getInstitutionType($features),
                'approval' => $this->collegeService->getFeatureStringValues($features, 'Approvals'),
                'reviews' => $this->reviewService->getCollegeSubpageReviews($college->id),
                'revCategoryRating' => $this->reviewService->getCollegeBasedCategoryRating($college->id),
                'revDistributionRating' => $this->reviewService->getRevDistributionRating($college->id),
                'reviewCount' => $this->reviewService->getCollegeReviewCount($college->id),
                'liveApplicationForm' => $this->collegeService->getLiveApplicationFormData($college->id),
                'qna' => $this->collegeService->getQnaCount($college->slug),
                'brochure' => $this->collegeService->getCollegeBroucher($college->id, 'college'),
                'sponsorClientUrl' => $this->collegeService->getRedirectionLink('college', $college->id),
                'faqs' => $this->faqService->getPageLevelFaqDetails(College::ENTITY_COLLEGE, $college->id, 'previous-year-papers', ''),
                'forums' => $this->forumService->getByCategory(ForumCategory::FORUM_COLLEGE_TYPE, $college->slug, 'previous-year-papers'),
                'collegeByDiscipline' => $this->collegeService->getCollegeByDiscipline($college->id, !empty($college->city) && !empty($college->city->id) ? $college->city->id : ''),
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10),
                'recentNews' => $this->newsService->getRecent(10),
                'trendingArticles' => $this->articleService->getTrendings(10),
                'recentArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at'),
                'article' => $this->collegeService->getRelatedArticles($college->id),
                'news' => $this->collegeService->getRelatedNews($college->id),
                'dynamicCta' => UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGES, 'previous-year-papers', ($college->display_name ?? $college->name), $college->slug, !empty($college->city) && !empty($college->city->name) ? $college->city->name : ''),
                'recentActivity' => [$this->collegeService->getRecentActivityByEntity(College::ENTITY_COLLEGE, $college->id, 'average-package'), (!empty($college->display_name) ? $college->display_name : $college->name)],
                'courseCount' => $this->collegeService->getCourseCount($college->id),
                'dropdowns' => $collegeSubPageDropdown,
                'examContentTemplate' => [$examContentTemplate, (!empty($college->display_name) ? $college->display_name : $college->name)],
                'collegeNotificationUpdate' => $this->collegeService->getCollegeNotificationUpdate($college->id, 'previous-year-papers'),


            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('previous-year-pappers', $data);
    }
}
