<?php

namespace frontend\controllers;

use common\helpers\ReviewHelper;
use common\models\Category;
use common\services\CollegeService;
use common\services\ReviewService;
use common\services\v2\NewsService;
use frontend\models\ReviewElasticSearch;
use frontend\services\ArticleService;
use Yii;
use yii\caching\TagDependency;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use common\helpers\DataHelper;
use common\models\CollegeContent;

class ReviewController extends Controller
{
    protected $reviewService;
    protected $articleService;
    protected $newsService;
    protected $collegeService;
    public $hasNext = true;
    public $pageType;
    public $entityType;

    public function __construct(
        $id,
        $module,
        ReviewService $reviewService,
        ArticleService $articleService,
        NewsService $newsService,
        CollegeService $collegeService,
        $config = []
    ) {

        $this->reviewService = $reviewService;
        $this->articleService = $articleService;
        $this->newsService = $newsService;
        $this->collegeService = $collegeService;
        parent::__construct($id, $module, $config);
    }

    public function actionIndex()
    {
        $response = DataHelper::trackStudentActivity('review-index');
        $this->entityType = $response['entityType'];
        $this->pageType = $response['pageType'];
        $currentUrl = Yii::$app->request->url;
        $request = Yii::$app->request;

        // Use Elasticsearch for search
        $searchModel = new ReviewElasticSearch();
        $dataProvider = $searchModel->search($request->queryParams, $request->bodyParams);
        $totalCount = $dataProvider['pagination']->totalCount;
        $currentPage = $dataProvider['pagination']->page;
        $pageSize = $dataProvider['pagination']->pageSize;
        $totalPageCount = ceil($totalCount / $pageSize);
        $getFilterPageUrl = ReviewHelper::getFilterUrl($request);

        if ($totalCount < 27 || ($currentPage !== 0 && $currentPage == $totalPageCount)) {
            $this->hasNext = false;
        }

        $data = [
            'searchModel' => $searchModel,
            'filters' => $this->reviewService->getFilterValues($searchModel, $currentUrl),
            'selectedFilters' => $searchModel->selectedFilters,
            'dataProvider' => $dataProvider,
            'reviews' => $dataProvider['results'],
            'hasNext' => $this->hasNext,
            'page' => ($currentPage + 1),
            'totalCount' => $totalCount,
            'reviewService' => $this->reviewService,
            'getFilterPageUrl' => $getFilterPageUrl
        ];

        if ($request->isAjax && $request->isPost) {
            \Yii::$app->response->format = Response::FORMAT_JSON;
            $html = $this->render('review-landing', $data);

            return [
                'status' => 200,
                'html' => $html,
                'page' => ($currentPage + 1),
                'currentPage' => $currentPage,
                'hasNext' => $this->hasNext,
                'url' => $getFilterPageUrl,
            ];
        }

        return $this->render('review-landing', $data);
    }

    public function actionReviewDetail($reviewSlug)
    {
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'review';
        $funArguments['bodyParams'] = Yii::$app->request->bodyParams;
        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () use ($reviewSlug) {
            $review = $this->reviewService->getReviewBySlug($reviewSlug);

            if (empty($review)) {
                throw new NotFoundHttpException();
            }

            $college = $this->reviewService->getCollege($review['college_id']) ?? [];
            $getCollegeContentStatus = !empty($college) && !empty($college['college']) ? $this->collegeService->getStatus($college['college']['id']) : [];
            $collegOverrallRating = !empty($college) && !empty($college['college']) ? $this->reviewService->getCollegeBasedCategoryRating($college['college']['id']) : [];

            return [
                'review' => $review ?? '',
                'student' => $this->reviewService->getStudent($review['student_id']) ?? '',
                'reviewFormAnswers' => $this->reviewService->getReviewAnswers($review['id']) ?? [],
                'reviewImages' => $this->reviewService->getReviewImages($review['id']) ?? [],
                'categoryRating' => $this->reviewService->getReviewCategoryRating($review['id']) ?? [],
                'collegesByCourseLocation' => !empty($college['college']) ? $this->reviewService->getcollegesByCourseLocation($review['course_id'], $college['college']['city_id'], 10) : [],
                'examsByCollege' => !empty($college['college']) ? $this->reviewService->getExamsByCollege($review['college_id']) : [],
                'newsCards' => $this->newsService->getRecent(10) ?? [],
                'contentsByOrder' => $this->reviewService->getContent($review['id']) ?? [],
                'parentCollege' => !empty($college) && !empty($college['parentCollege']) ? $college['parentCollege'] : [],
                'trendingArticles' => $this->articleService->getAll(10, [], Category::EXCLUDE_CATEGORY, 'updated_at', 'desc') ?? [],
                'recentArticles' => $this->articleService->getTrendingArticleWithAuthor(10) ?? [],
                'featuredNews' => $this->newsService->getFeaturedNews(9, 10) ?? [],
                'recentNews' => $this->newsService->getRecent(10) ?? [],
                'liveApplicationForm' => !empty($review['college_id']) ? $this->collegeService->getLiveApplicationFormData($review['college_id'], 20) : [],
                'otherReviewsCard' => $this->reviewService->getOtherReviews($review['college_id'], $review['course_id']) ?? [],
                'reviewSlug' => $reviewSlug,
                'reviewService' => $this->reviewService,
                'college' => $college ?? [],
                'reviewCollegeContentStatus' => !empty($getCollegeContentStatus) && isset($getCollegeContentStatus['reviews']) ? ($getCollegeContentStatus['reviews'] == CollegeContent::STATUS_ACTIVE ? CollegeContent::STATUS_ACTIVE : CollegeContent::STATUS_INACTIVE) :  CollegeContent::STATUS_INACTIVE,
                'collegOverrallRating' => $collegOverrallRating ?? [],
            ];
        }, 7200, new TagDependency(['tags' => DataHelper::generateCacheKey()]));
        return $this->render('review-detail', $data);
    }
}
