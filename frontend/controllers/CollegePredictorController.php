<?php

namespace frontend\controllers;

use frontend\models\CollegePredictorSearch;
use frontend\services\ExamService;
use Yii;
use yii\web\NotFoundHttpException;

class CollegePredictorController extends Controller
{

    protected $examService;

    public function __construct(
        $id,
        $module,
        ExamService $examService,
        $config = []
    ) {
        $this->examService = $examService;
        parent::__construct($id, $module, $config);
    }

    /**
     * Common method to get college predictor data
     *
     * @param string|null $examSlug The exam slug if available
     * @param bool $showAllData Whether to show all data without rank/category filters
     * @return array Data for the view
     */
    private function getCollegePredictorData($examSlug = null, $showAllData = false)
    {
        $request = Yii::$app->request;
        $session = Yii::$app->session;
        $queryParams = $request->queryParams;
        $rank = null;
        $category = null;

        // Check if we should show all data (for /predictor/college-predictor URL)
        if (!$showAllData) {
            if (isset($_COOKIE['college_predictor_rank']) && isset($_COOKIE['college_predictor_category'])) {
                $rank = $_COOKIE['college_predictor_rank'];
                $category = $_COOKIE['college_predictor_category'];

                // Update session with cookie values
                $session->set('college_predictor_rank', $rank);
                $session->set('college_predictor_category', $category);
            } else {
                $rank = $session->get('college_predictor_rank');
                $category = $session->get('college_predictor_category');
            }
        }

        $searchParams = [];

        // Add other query params except rank and category
        foreach ($queryParams as $key => $value) {
            if ($key !== 'rank' && $key !== 'category') {
                $searchParams[$key] = $value;
            }
        }

        if ($rank && $category && !$showAllData) {
            $searchParams['rank'] = $rank;
            $searchParams['category'] = $category;
        }

        $searchModel = new CollegePredictorSearch();
        $dataProvider = $searchModel->search($searchParams);
        $totalCount = $dataProvider->getTotalCount();
        $pagination = $dataProvider->getPagination();
        $currentPage = $pagination->getPage();
        $pageSize = $pagination->getPageSize();
        $totalPageCount = ceil($totalCount / $pageSize);

        $hasNext = true;
        if ($totalCount <= $pageSize || ($currentPage !== 0 && $currentPage >= $totalPageCount - 1)) {
            $hasNext = false;
        }

        $categoryName = $searchModel->getCategory();

        // Get filter options for dropdowns
        $programOptions = $searchModel->getProgramOptions();
        $collegeTypeOptions = $searchModel->getCollegeTypeOptions();
        $stateOptions = $searchModel->getStateOptions();

        return [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'colleges' => $dataProvider->getModels(),
            'hasNext' => $hasNext,
            'page' => ($currentPage + 1),
            'totalCount' => $totalCount,
            'programOptions' => $programOptions,
            'collegeTypeOptions' => $collegeTypeOptions,
            'stateOptions' => $stateOptions,
            'category' => $categoryName,
            'rank' => $rank,
            'examSlug' => $examSlug,
            'showAllData' => $showAllData,
        ];
    }

    /**
     * Default action for college predictor
     */
    public function actionIndex()
    {
        // Get all exams with college-predictor subpage grouped by stream
        $streamExams = $this->examService->getAllExamStream();

        if (empty($streamExams)) {
            throw new NotFoundHttpException();
        }
        return $this->render('college-predictor', [
            'streamExams' => $streamExams
        ]);
    }

    /**
     * AJAX endpoint for fetching college predictor data
     *
     * @return string JSON response
     */
    public function actionAjaxPredictor()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $request = \Yii::$app->request;
        $examSlug = $request->get('examSlug', '');
        $showAllData = empty($examSlug);

        // Get the data
        $data = $this->getCollegePredictorData($examSlug, $showAllData);

        // Render the filter and list partials
        $filterHtml = $this->renderPartial('partials/_college_predictor_filter', [
            'searchModel' => $data['searchModel'],
            'programOptions' => $data['programOptions'],
            'collegeTypeOptions' => $data['collegeTypeOptions'],
            'stateOptions' => $data['stateOptions'],
            'totalCount' => $data['totalCount'],
            'showAllData' => $data['showAllData'],
        ]);

        $listHtml = $this->renderPartial('partials/_college_predictor_list', [
            'colleges' => $data['colleges'],
            'hasNext' => $data['hasNext'],
            'searchModel' => $data['searchModel'],
            'entity' => 'college'
        ]);

        return [
            'success' => true,
            'filterHtml' => $filterHtml,
            'listHtml' => $listHtml,
            'totalCount' => $data['totalCount'],
            'hasNext' => $data['hasNext'],
        ];
    }
}
