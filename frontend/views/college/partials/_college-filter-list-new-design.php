<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\FilterHelper;
use common\models\CollegeContent;
use common\models\Lead;
use common\models\LeadBucketTagging;
use common\services\CollegeService;
use common\services\UserService;
use frontend\helpers\Ad;
use frontend\helpers\Url;
use yii\helpers\ArrayHelper;

if (\Yii::$app->request->getPathInfo()== 'all-colleges') {
        $urlFilter = \Yii::$app->request->getPathInfo();
}
    
$collegeResults = array_chunk($models, 3);
if ($totalCollegeCount >0 &&  $totalCollegeCount < 3) {
    $sponsorCollegeList = $sponsorCollegeList;
} else {
    $sponsorCollegeList = array_chunk($sponsorCollegeList, 2);
}
$sponsorCollegeIds = ArrayHelper::getColumn($sponsorCollegeList, 'college_id');
$i = !empty($iRank) ? $iRank : 1;
$firstSix = 0;
$ad = 1;
$cta_data = [];
$widget = 0;
$h = 0;
?>
<div class="searchedcollegeList">
    <?php
    foreach ($collegeResults as $k => $models):
        foreach ($models as $model):
            $collegeContentPageStatus = CollegeService::getStatus($model['college_id']);
            $educationBody = CollegeService::getCollegeEducationBody($model['college_id']);
            $dynamicCta = UserService::getBucketTagging(LeadBucketTagging::LEAD_ENTITY_COLLEGE_LISTING, 'college-listing', $model['display_name'] ?? $model['name'], $model['slug'], $model['city_name']);
            $ctaLocation1 = $isMobile ? $dynamicCta['cta_position_0']['wap'] : (isset($dynamicCta['cta_position_0']['web']) ? $dynamicCta['cta_position_0']['web'] : '');
            $ctaLocation2 = $isMobile ? $dynamicCta['cta_position_1']['wap'] : (isset($dynamicCta['cta_position_1']['web']) ? $dynamicCta['cta_position_1']['web'] : '');
            $ctaLocation4 = $isMobile ? $dynamicCta['cta_position_4']['wap'] : (isset($dynamicCta['cta_position_4']['web']) ? $dynamicCta['cta_position_4']['web'] : '');
            $streamSlug = '';

            if (!empty($searchModel->stream) && !empty($searchModel->course)) {
                $courseId = $model['course']['course_id'] ?? '';
                $courseName = $model['course']['course_name'] ?? '';
                $courseSlug = $model['course']['course_slug'] ?? '';
                $streamSlug = $searchModel->stream ?? '';
            } elseif (!empty($searchModel->stream)) {
                $courseId = '';
                $courseName = '';
                $courseSlug = '';
                $streamSlug = $searchModel->stream ?? '';
            }

            $firstSix++;
            $widget++;
            $content = !empty($model['review']) ? substr($model['review'], 0, 63) : '';
            $reviewContent = !empty($content) ? ContentHelper::removeStyleTag(stripslashes(html_entity_decode($content))) : '';
            $cta1 = $isMobile ? 'colleges_listing_wap_card_left_cta' : 'colleges_listing_web_card_left_cta';
            $cta2 = $isMobile ? 'colleges_listing_wap_card_right_cta' : 'colleges_listing_web_card_right_cta';
            ?>

            <?php if (!in_array($model['college_id'], $sponsorCollegeIds)) { ?>
            <div class="college__card__new " id="search-<?= $model['college_id']; ?>">
                <div class="card__header__row">
                    <div class="college__detail__grid">

                        <img width="56" height="56" class="<?= ($firstSix > 6) ? 'lazyload college__image' : 'college__image' ?>" loading="<?= ($firstSix > 7) ? 'lazy' : '' ?>" src="<?= !empty($model['logo']) ? Url::getCollegeLogo($model['logo']) : Url::defaultCollegeLogo() ?>" onclick="gmu.url.goto('<?= Url::toRoute($model['slug']) ?>')" alt="">

                        <?php if (!empty($model['nirf_rank_overall']) && $model['nirf_rank_overall'] != null):
                            echo '<span class="list__style college__rank mobileOnly "> # ' . $model['nirf_rank_overall'] . ' NIRF  </span>';
                        endif; ?>

                        <div class="college__detail__row">
                            <h2 class="college__name"><a href="<?= Url::toRoute($model['slug']) ?>" title="<?= $model['display_name'] ?? $model['name'] ?>" data-hide="<?= $model['college_id']; ?>"><?= $model['display_name'] ?? $model['name'] ?></a></h2>
                            <div class="detail__list__mobile">
                                <span class="list__style college__location"><?= $model['city_name'] ?>, <?= $model['state_name'] ?></span>
                                <span class="list__style college__affiliation"><?= $model['type'] ? ucfirst($model['type']) : '' ?></span>

                                <?php if (!empty($model['rating'])):  ?>
                                    <?php if (!empty($collegeContentPageStatus) && isset($collegeContentPageStatus['reviews']) && $collegeContentPageStatus['reviews'] == CollegeContent::STATUS_ACTIVE): ?>
                                        <a class="list__style list__style__rating" target="_blank" title="<?= $model['name'] ?> Reviews" href="<?= Url::toCollege($model['slug'], 'reviews') ?>"><span class="list__style college__rating"><span class="spriteIcon__2 review__star__icon"></span><?= $model['rating'] ?></span> </a>
                                    <?php else: ?>
                                        <span class="list__style college__rating"><span class="spriteIcon__2 review__star__icon"></span><?= $model['rating'] ?></span>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if (!empty($model['nirf_rank_overall']) && $model['nirf_rank_overall'] != null):
                                    echo '<span class="list__style college__rank desktopOnly "> # ' . $model['nirf_rank_overall'] . ' NIRF  </span>';
                                endif; ?>
                                <div class="like__compare__grid mobileOnly">
                                    <!--span
                                                                                                        class="heart__icon spriteIcon__2"></span-->
                                    <span class="compare__icon spriteIcon__2 compareIcon"></span>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="like__compare__grid desktopOnly">
                        <!--span class="heart__icon spriteIcon__2"></span-->
                        <span class="compare__icon spriteIcon__2 compareIcon"></span>
                    </div>
                </div>
                <?php
                    
                    $isStream = 0;
                    $isLocation = 0;
                    $isDegree = 0;
                    $streamName ='';
                    $courseName ='';
                    $courseSlug ='';
                    $courseFieldName = '';
                    $urlParse = \Yii::$app->request->url;
                    if( $urlParse=='/college/get-filter-data'){
                        $urlParse = $urlFilter;
                    }
                    $position = strpos( $urlParse, '-colleges');
                    $result = substr( $urlParse, 0, $position);
                    $result = str_replace('/', '', $result);
                if (!empty($selectedFilters)) {
                    foreach ($selectedFilters as $key=>$checkFilter) {
                         if($checkFilter['filterGroup_name']=='Streams' && $result==$key){
                            $isStream = 1;
                            $isLocation = 0;
                            $isDegree = 0;
                            $streamName = $key;
                             break;
                        }
                         if(($checkFilter['filterGroup_name']=='Courses'|| $checkFilter['filterGroup_name']=='Specialization' ) && $result==$key){
                            $isStream = 0;
                            $isLocation = 0;
                            $isDegree = 1;
                            $courseName = $checkFilter['name'];
                            $courseSlug =$key;
                            $courseFieldName = $checkFilter['filterGroup_name'];
                            break;
                        }
                        if ($checkFilter['filterGroup_name']=='City' || $checkFilter['filterGroup_name']=='State') {
                            $isLocation = 1;
                            $isStream = 0;
                            $isDegree = 0;
                             continue;
                        }
                       
                    }
                }
                ?>
                <?php
                echo $this->render('_college-card-content', [
                'model' => $model,
                'selectedFilters' => $selectedFilters,
                'isLocation'=>$isLocation,
                'isStream'=>$isStream,
                'isDegree'=>$isDegree,
                'educationBody'=>$educationBody,
                'streamName'=>$streamName,
                'courseName' =>  $courseName,
                'urlFilter' =>   $urlFilter,
                'courseSlug'=> $courseSlug,
                'courseFieldName' =>  $courseFieldName,
                ]);
                ?>
                <div class="highlight__cta__row" <?= $streamName; ?>>
                    <div class="highlights__grid">
                        <div class="highlight__div">
                            <span class="highlight__name">Courses
                                Offered</span>
                            <span class="highlight__value">
                                <?php if (!empty($model['course'])  &&  $model['courseCount'] != 0): ?>
                                    <?php if (!empty($collegeContentPageStatus) && isset($collegeContentPageStatus['courses-fees']) && $collegeContentPageStatus['courses-fees'] == CollegeContent::STATUS_ACTIVE): ?>
                                        <p> <a title="<?= ($model['display_name'] ?? $model['name']) . ' Courses & Fee Details' ?>" href=" <?= Url::toCollege($model['slug'], 'courses-fees') ?>">
                                                <?= isset($model['courseCount']) ? $model['courseCount'] : count($model['course']) ?> Courses
                                            </a>
                                        </p>
                                    <?php else: ?>
                                        <p title="<?= ($model['display_name'] ?? $model['name']) . ' Courses & Fee Details' ?>"><?= isset($model['courseCount']) ? $model['courseCount'] : count($model['course']) ?> Courses</p>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </span>
                        </div>


                        <div class="highlight__div exam__accepted__div">
                            <p class="highlight__name">Exam
                                Accepted</p>
                            <span class="highlight__value">
                                <?php $examArr = [];
                                if (!empty($model['exams'])):
                                    $examArr = FilterHelper::getExams($model['exams']);
                                    if (!empty($examArr)):
                                        ?>
                                        <?php
                                        $count = 0;
                                        $noOfExam = 0;
                                        foreach ($examArr as $key => $val):
                                            if ($count < 2):
                                                $noOfExam++;
                                                ?>
                                                <a target="_blank" href="<?= Url::home() . 'exams/' . $model['exams'][$key] ?>"><?= $val; ?></a>
                                                <?php if ($count < 1  && count($examArr) != 1):
                                                    ?> , <?php
                                                endif; ?>
                                                <?php
                                            endif;
                                            $count++;
                                        endforeach;
                                        ?>
                            </span>
                            <div class="hover__tooltop"><?php if ($noOfExam > 1 && (count($examArr) - $noOfExam) != 0):
                                ?>+<?= count($examArr) - $noOfExam; ?><?php
                                                        endif; ?>
                                <span class="hover__card">
                                        <?php $examText = '';
                                        $j = 0;
                                        foreach ($examArr as $keys => $exam):
                                            if ($j < $noOfExam):
                                                $j++;
                                                continue;
                                            endif; ?>
                                            <?php $examText = $examText . '<a target="_blank" href="' . Url::home() . 'exams/' . $model['exams'][$keys] . '">' . $exam . '</a>,'; ?>
                                            <?php $j++;
                                        endforeach; ?>
                                        <?php echo rtrim($examText, ', '); ?>

                                </span>
                            </div>
                                    <?php endif; ?>
                                <?php else: ?>
                        <span class="highlight__value"> -/- </span>
                                <?php endif; ?>


                        </div>
                        <div class="desktopOnly"></div>
                        <div class="highlight__div">
                            <span class="highlight__name">Tuition
                                Fees Range</span>
                            <span class="highlight__value">
                                <?php if (!empty($model['courseCount']) && $model['courseCount'] != 0) {
                                    if (isset($model['minAvgFees']) && $model['minAvgFees'] != 0) {
                                        echo '₹' . CollegeHelper::feesFormatListing($model['minAvgFees'], 'all-colleges') . '-';
                                    }
                                    if (isset($model['maxAvgFees']) && $model['maxAvgFees'] != 0) {
                                        if ($model['minAvgFees'] == 0) {
                                            echo '₹' . CollegeHelper::feesFormatListing($model['maxAvgFees'], 'all-colleges');
                                        } else {
                                            echo CollegeHelper::feesFormatListing($model['maxAvgFees'], 'all-colleges');
                                        }
                                    } else {
                                        echo '-/-';
                                    };
                                } else {
                                    echo '-/-';
                                } ?>
                            </span>
                        </div>
                        <div class="highlight__div">
                            <span class="highlight__name">Accreditation</span>
                            <span class="highlight__value">
                                <?php if (isset($model['accreditations']) && !empty($model['accreditations']) && $model['accreditations'] != '') {
                                    $accrediation = explode(',', $model['accreditations']);

                                    if (is_array($accrediation)) {
                                        $count = 0;
                                        $noOfExam = 0;
                                        foreach ($accrediation as $acc):
                                            if ($count < 2):
                                                $noOfExam++;
                                                ?>
                                                <?= $acc; ?>
                                                <?php if ($count < 1  && count($accrediation) != 1):
                                                    ?> , <?php
                                                endif; ?>
                                                <?php
                                            endif;
                                            $count++;
                                        endforeach;
                                        ?>

                                        <div class="hover__tooltop"><?php if ($noOfExam > 1 && (count($accrediation) - $noOfExam) != 0):
                                            ?>+<?= count($accrediation) - $noOfExam; ?><?php
                                                                    endif; ?>
                                            <span class="hover__card">
                                                <?php $accText = '';
                                                $j = 0;
                                                foreach ($accrediation as $acc):
                                                    if ($j < $noOfExam):
                                                        $j++;
                                                        continue;
                                                    endif; ?>
                                                    <?php $accText = $accText . '' . $acc . ','; ?>
                                                    <?php $j++;
                                                endforeach; ?>
                                                <?php echo rtrim($accText, ','); ?>

                                            </span>
                                        </div>
                                    <?php    }

                                    ?>
                            </span>
                                <?php } else { ?>
                            <span class="highlight__value"> -/- </span>
                                <?php } ?>

                        <!--span class="highlight__value"> //!empty($model['median_salary']) ? '₹' . CollegeHelper::feesFormat(floor($model['median_salary'])) : '-/-' ?></span!-->
                        </div>
                        <div class="highlight__div"></div>
                    </div>
                    <div class="cta__grid">
                        <div
                            class="cta__div lead-cta-college-filter-2 leadFilterData download__brochure"
                            data-entity="college" data-filter="college-listing" data-lead_cta="21"
                            data-slug="<?= $model['college_id'] ?>"
                            data-stateId="<?= $model['state_id'] ?>"
                            data-cityId="<?= $model['city_id'] ?>"
                            data-course="<?= $courseId ?? '' ?>"
                            data-stream="<?= $streamSlug ?>"
                            data-ctaLocation="<?= $ctaLocation2 ?>"
                            data-title="<?= $dynamicCta['cta_position_1']['lead_form_title'] ?>"
                            data-description="<?= $dynamicCta['cta_position_1']['lead_form_description'] ?>"
                            data-image="<?= Url::getCollegeLogo($model['logo']) ?>">
                        </div>
                        <div
                            class="cta__div lead-cta-college-filter-1 leadFilterData primaryBtn"
                            data-stateId="<?= $model['state_id'] ?>"
                            data-cityId="<?= $model['city_id'] ?>"
                            data-entity="college"
                            data-filter="college-listing"
                            data-lead_cta="23"
                            data-slug="<?= $model['college_id'] ?>"
                            data-course="<?= $courseId ?? '' ?>"
                            data-stream="<?= $streamSlug ?>"
                            data-ctaLocation="<?= $ctaLocation1 ?>"
                            data-title="<?= $dynamicCta['cta_position_0']['lead_form_title'] ?>"
                            data-description="<?= $dynamicCta['cta_position_0']['lead_form_description'] ?>"
                            data-image="<?= Url::getCollegeLogo($model['logo']) ?>">
                        </div>
                    </div>
                </div>
            </div>
            <?php } ?>
            <?php if ($widget == 2 && $isWidget == 'true'): ?>
                <?php
                echo $this->render('_college-filter-widget-one', [
                    'filters' => $filters,
                    'selectedFilters' => $selectedFilters
                ]);
                ?>
            <?php endif; ?>
            <?php if ($widget == 4 && $isWidget == 'true'): ?>
                <div class="sponser-list hide__widget">
                </div>
                <div class="cta__banner hide__widget">
                    <p>Confused about which college or exam to opt for?</p>
                    <p>Chat with our counselor Get your personalised list of
                        colleges &amp; exams </p>
                    <div
                        class="cta__div primaryBtn brochureBtn  leadFilterData predict-my-college-cta"
                        data-stateId="<?= $model['state_id'] ?>"
                        data-cityId="<?= $model['city_id'] ?>"
                        data-entity="college"
                        data-filter="college-listing"
                        data-lead_cta="22"
                        data-slug="<?= $model['college_id'] ?>"
                        data-course="<?= $courseId ?? '' ?>"
                        data-stream="<?= $streamSlug ?>"
                        data-ctaLocation="<?= $ctaLocation4 ?>"
                        data-title="<?= $dynamicCta['cta_position_4']['lead_form_title'] ?>"
                        data-description="<?= $dynamicCta['cta_position_4']['lead_form_description'] ?>"
                        data-image="<?= Url::getCollegeLogo($model['logo']) ?>">
                    </div>

                    <img src="../../yas/images/cta-banner.png" alt="banner image">
                </div>
            <?php endif; ?>
            <?php if ($widget == 8 && $isWidget == 'true'): ?>
                <div class="feedback__section hide__widget">
                    <p>Your opinion matter to us!</p>
                    <p>Rate your experience using this page so far.</p>
                    <ul>
                        <li data-value="1">1</li>
                        <li data-value="2">2</li>
                        <li data-value="3">3</li>
                        <li data-value="4">4</li>
                        <li data-value="5">5</li>
                        <li data-value="6">6</li>
                        <li data-value="7">7</li>
                        <li data-value="8">8</li>
                        <li data-value="9">9</li>
                        <li data-value="10">10</li>
                    </ul>
                    <img class="feedback__image" src="../../yas/images/feedback-image.png" alt="Feedback image">
                </div>
            <?php endif; ?>
            <?php if ($widget % 4 == 0 && !empty($sponsorCollegeList)): ?>
                <?php
                echo $this->render('_sponsor-college-view', [
                    'sponsorCollegeList' => $sponsorCollegeList[$h],
                    'isMobile' => $isMobile,
                    'selectedFilters' =>$selectedFilters,
                    'educationBody'=>$educationBody,
                    'urlFilter' =>   $urlFilter,
                    'streamName'=>$streamName,
                    'courseName' =>  $courseName,
                    'courseSlug'=> $courseSlug,
                    'courseFieldName' =>  $courseFieldName,
                ]);
                ?>
                <?php unset($sponsorCollegeList[$h]);
                $h++;
            endif; ?>
        <?php endforeach;
    endforeach; ?>
     <?php if ($totalCollegeCount >0 &&  $totalCollegeCount < 3  &&  !empty($sponsorCollegeList)): ?>
                <?php
                echo $this->render('_sponsor-college-view', [
                    'sponsorCollegeList' => $sponsorCollegeList,
                    'isMobile' => $isMobile,
                    'selectedFilters' =>$selectedFilters,
                    'educationBody'=>$educationBody,
                    'urlFilter' =>   $urlFilter,
                    'streamName'=>$streamName,
                    'courseName' =>  $courseName,
                    'courseSlug'=> $courseSlug,
                    'courseFieldName' =>  $courseFieldName,
                ]);
                ?>
     <?php  endif; ?>
</div>
<?php if ($hasNext): ?>
    <div class="load__more__row">


        <div class="load__more__button loadMoreList" hasnesxt="<?= $hasNext; ?>" data-irank="<?= $i; ?>" data-page="<?= $page ?>">
            Load More Colleges
            <span class="spriteIcon__2 red__angle__icon"></span>
        </div>
    </div>
<?php endif; ?>