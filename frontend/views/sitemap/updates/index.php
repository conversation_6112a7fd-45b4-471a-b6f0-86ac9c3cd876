<?php

use frontend\helpers\Url; ?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
    <?php
    foreach ($data as $val): ?>
        <url>
            <?php if ($val['entity'] == 'college'): ?>
                <loc><?= $val['domain'] . '/college/' . $val['slug'] ?></loc>
            <?php else: ?>
                <loc><?= $val['domain'] . '/' . $val['slug'] ?></loc>
            <?php endif; ?>
            <priority><?= $val['priority'] ?></priority>
            <changefreq><?= $val['change_freq'] ?></changefreq>
            <lastmod><?= date(DATE_ATOM, strtotime($val['lastmod'])) ?></lastmod>
        </url>
    <?php endforeach; ?>
</urlset>