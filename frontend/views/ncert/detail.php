<?php

use common\helpers\ArticleDataHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\NcertArticles;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\Schema;

// utils
$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = $author ? ContentHelper::getUserProfilePic($author->slug) : '';

// meta key
$this->title = $ncert->title . ' - Getmyuni' ?? '';
$this->context->description = $ncert->meta_description ?? '';
$this->context->ogType = 'article';

if (!empty($ncert->cover_image)) {
    $this->registerMetaTag(['property' => 'og:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'og:image:height', 'content' => '667']);
    $this->registerMetaTag(['property' => 'og:image:alt', 'content' => $ncert->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:alt', 'content' => $ncert->h1]);
    $this->registerMetaTag(['property' => 'twitter:image:type', 'content' => 'image/jpeg']);
    $this->registerMetaTag(['property' => 'twitter:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'twitter:image:height', 'content' => '667']);
    $this->registerLinkTag(['href' => ArticleDataHelper::getImage($ncert->cover_image), 'rel' => 'preload',  'fetchpriority' => 'high', 'as' => 'image', 'imagesrcset' => ArticleDataHelper::getImage($ncert->cover_image) . ' 300w', 'imagesizes' => '50vw']);
    $this->context->ogImage = ArticleDataHelper::getImage($ncert->cover_image);
}
if (!empty($authorImage)) {
    $this->registerLinkTag(['href' => $authorImage, 'rel' => 'preload', 'as' => 'image', 'imagesrcset' => $authorImage . ' 300w', 'imagesizes' => '50vw']);
}
$this->registerMetaTag(['name' => 'robots', 'content' => 'max-image-preview:large']);
$this->registerMetaTag(['property' => 'article:published_time', 'content' => date(DATE_ATOM, strtotime($ncert->created_at))]);

$this->registerMetaTag(['property' => 'article:modified_time', 'content' => date(DATE_ATOM, strtotime($ncert->updated_at))]);
// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => 'NCERT', 'url' => Url::toNcert(), 'title' => 'NCERT'];
if (!empty($parent)) {
    foreach ($parent as $key => $val) {
        $this->params['breadcrumbs'][] = ['label' => $val, 'url' => Url::toNcertDetail($key), 'title' => $val];
    }
}

$this->params['breadcrumbs'][] = ContentHelper::htmlDecode(stripslashes($ncert->h1), false);
$this->params['entity_name'] = addslashes($ncert->title) ?? '';
$this->params['entityDisplayName'] = addslashes($ncert->title) ?? '';
$this->params['entitySlug'] = $ncert->slug ?? '';
$this->params['entity_id'] = $ncert->id ?? 0;
$this->params['entity'] = NcertArticles::ENTITY_NCERT;
$this->params['product_mapping_entity'] = empty($entityName) ? 'Ncert' : $entityName;
$this->params['product_mapping_entity_id'] = empty($entityId) ? 0 : $entityId;
$this->params['canonicalUrl'] = $currentUrl;
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;

$this->registerCssFile(Yii::$app->params['cssPath'] . 'ncert-article-details.css', ['depends' => [AppAsset::class]]);



$this->params['schema'] = Schema::ncertSchema($ncert, $currentUrl, $this->title);

?>

<div class="containerMargin">
    <div class="row">
        <div class="col-md-12">
            <div class="articleHeader">
                <section class="pageDescription">
                    <div class="row">
                        <div class="col-md-12">
                            <!--span class="spriteIcon liveIcon"></span-->
                            <h1><?= ContentHelper::htmlDecode(stripslashes($ncert->h1), false) ?></h1>
                            <div class="authorInfoAndTranslateBtn">
                                <div class="updated-info row">

                                    <img class="lazyload" loading="lazy" width="60" height="60" data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= $author ? $author->name . ' Image' : '' ?>" />
                                    <span class="updatedDetails">
                                        <?php if ($author): ?>
                                            <div class="updatedBy">
                                                <p><a href="<?= $author ? '/author/' . $author->slug : '#' ?>"><?= $author ? $author->name : ucfirst(str_replace('-', ' ', ($author ? $author->username : ''))) ?></a>, </p>
                                            </div>
                                            <p><span><?= Yii::$app->formatter->asDate($ncert->updated_at ?? 'today') ?> </span>
                                        <?php endif; ?>
                                            </p>
                                            <ul>
                                                <p><?= Yii::t('app', 'Share it on') ?>:</p>
                                                <li>
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $currentUrl ?>" target="_blank" rel="noopener nofollow" class="spriteIcon greyFbIcon"></a>
                                                </li>
                                                <li>
                                                    <a href="https://twitter.com/share?url=<?= $currentUrl ?>" class="spriteIcon greyTwitterIcon" rel="noopener nofollow" target="_blank"></a>
                                                </li>
                                            </ul>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <div class="col-md-8">
            <main>
                <article>
                    <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                        <div class="horizontalRectangle">
                            <div class="appendAdDiv" style="<?= $isMobile ? 'height: 50px;' : '' ?>background:#EAEAEA;">
                                <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
                                ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="articelNote">
                        <p><?= $ncert->meta_description ?></p>
                    </div>
                    <?php if (!empty($ncert->cover_image)): ?>
                        <div class="bannerImg">
                            <picture>
                                <source media="(max-width: 500px)" srcset="<?= ArticleDataHelper::getImage($ncert->cover_image) ?>">
                                <img width="1200" height="675" src="<?= ArticleDataHelper::getImage($ncert->cover_image) ?>" alt="<?= $ncert->h1 ?>" />
                            </picture>
                        </div>
                    <?php endif; ?>
                    <div class="articleInfo">
                        <?= ContentHelper::removeStyleTag(stripslashes(
                            html_entity_decode(DataHelper::parseDomainUrlInContent($ncert->content))
                        )) ?>
                    </div>
                </article>
                <?php if (!empty($faqs)): ?>
                    <section class="faq_section">
                        <h2>FAQs</h2>
                        <div class="faqDiv">

                            <?php foreach ($faqs as $faq): ?>
                                <div>
                                    <p class="faq_question"><?= ContentHelper::htmlDecode($faq->question, true) ?></p>
                                    <div class="faq_answer" style="display: none;">
                                        <?= 'A: ' . ContentHelper::htmlDecode($faq->answer) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                        </div>
                    </section>
                <?php endif; ?>
            </main>
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_336x280', '__240x400 __336x280') ?>
                        <?php else: ?>
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <div class="col-md-4">
            <aside>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="getSupport">
                        <!--<div class="row">
                            <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                            <p>Get Expert Counseling and Student Scholarship</p>
                        </div>--->
                        <p class="getSupport__subheading">Are you Interested in this Ncert Article?</p>
                        <div class="button__row__container">
                            <div class="lead-cta" data-entity="ncert" data-lead_cta="2" data-sponsor=""></div>
                        </div>
                    </div>
                <?php endif; ?>
                <?= $this->render('partials/_sidebar-articles', [
                    'trendings' => $trendings,
                    'recentNcert' => $recentNcert,
                    'article' => $ncert
                ])
?>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="squareDiv">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__200x600')
                                ?>
                            <?php else: ?>
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x250')
                                ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php if (!$isMobile): ?>
                        <div class="verticleRectangle">
                            <div class="appendAdDiv" style="background:#EAEAEA;">
                                <?php echo Freestartads::unit('getmyuni-com_siderail_right_2', '__300x600')
                                ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </aside>
        </div>
    </div>
    <section class="commentSection">
        <?= $this->render('/partials/comment/_form', [
            'model' => $commentModel,
            'entity' => NcertArticles::ENTITY_NCERT,
            'entity_id' => $ncert->id
        ]) ?>
        <?= $this->render('/partials/comment/_comment', [
            'comments' => $comments,
            'entity' => NcertArticles::ENTITY_NCERT,
            'entityId' => $ncert->id
        ]) ?>
    </section>
    <div id="comment-reply-form-js"></div>
</div>