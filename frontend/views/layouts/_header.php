<?php

use frontend\helpers\Url;

$isMobile = \Yii::$app->devicedetect->isMobile();
$currentUrl = Url::current();
$createReviewPage = '/review/create';
?>
<div class="topHeader">
    <div class="container">
        <div class="row">
            <div class="mobileOnly">
                <p class="spriteIcon hambergerIcon"></p>
            </div>

            <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                <a href="/" class="spriteIcon headerLogo" title="Getmyuni"></a>
            <?php else:?>
                <div></div>
            <?php endif;?>

            <?php if (!$isMobile): ?>
                <ul>
                    <li><a href="<?= Url::toDomain() ?>all-colleges" title="Top Colleges in India" onclick="menuNavigate('<?= Url::toDomain() ?>all-colleges','Top Colleges','','college list')" class="main-link">Top Colleges</a></li>
                    <li><a href="<?= Url::toDomain() ?>courses" title="Courses in India"  onclick="menuNavigate('<?= Url::toDomain() ?>courses','Top Courses','','course')" class="main-link">Top Courses</a></li>
                    <li><a href="<?= Url::toDomain() ?>exams" title="Entrance Exams in India" onclick="menuNavigate('<?= Url::toDomain() ?>exams','Entrance Exams','','exam')">Entrance Exams</a></li>
                    <li><a href="<?= Url::toDomain() ?>boards" title="Education Boards in India" onclick="menuNavigate('<?= Url::toDomain() ?>boards','Boards','','board')">Boards</a></li>
                    <li><a href="<?= Url::toDomain() ?>college/admissions" title="College Admission in India" onclick="menuNavigate('<?= Url::toDomain() ?>admissions','Admission','','admission')">Admission <?= date('Y') ?></a></li>
                    <?php if (Url::toDomain() ==  Url::toGetmyuni()): ?>
                        <li><a href="<?= Url::toNewGetmyuni() ?>" title="News"  onclick="menuNavigate('<?= Url::toNewGetmyuni() ?>','News','','News')">News</a></li>
                    <?php else: ?>
                        <li><a href="<?= Url::toDomain() ?>news" title="News" onclick="menuNavigate('<?= Url::toNewGetmyuni() ?>','News','','News')">News</a></li>
                    <?php endif; ?>
                    <!-- <li><a href="javascript:;">Study Abroad</a></li> -->
                    <!-- <li class="sa_dropdown">
                        <a class="" href="javascript:;">Study Abroad<span class="spriteIcon whiteCaretIcon"></span></a>
                        <ul class="study-abroad-options study-abroad">
                            <li><a href="<?= Url::toDomain() ?>canada" title="Study in Canada">Study in Canada</a></li>
                            <li><a href="<?= Url::toDomain() ?>uk" title="Study in UK">Study in UK</a></li>
                            <li><a href="<?= Url::toDomain() ?>usa" title="Study in USA">Study in USA</a></li>
                            <li><a href="<?= Url::toDomain() ?>australia" title="Study in Australia">Study in Australia</a></li>
                            <li><a href="<?= Url::toDomain() ?>germany" title="Study in Germany">Study in Germany</a></li>
                            <li><a href="https://ieltsmaterial.com/" target="_blank">IELTS Material</a></li>
                        </ul>
                    </li> -->
                    <?php if (str_contains($currentUrl, $createReviewPage) == false): ?>
                        <li class="sa_dropdown">
                            <a class="" href="javascript:;">More<span class="spriteIcon whiteCaretIcon"></span></a>
                            <ul class="study-abroad-options study-abroad">
                                <!-- <li class="srcSpanIcon"></li> -->
                                <!-- <div class="row">
                                    <div class="col-md-12"> -->
                                <!-- <ul> -->
                                <li><a title="Articles" href="<?= Url::toDomain() ?>articles" onclick="menuNavigate('<?= Url::toDomain() ?>articles','Articles','','Articles')">Articles</a></li>
                                <!-- <li><a title="NCERT" href="<?= Url::toDomain() ?>ncert">NCERT</a></li> -->
                                <!-- <li><a title="Compare Colleges" class="submenulist" href="<?= Url::toDomain() ?>college-compare">Compare Colleges</a> -->
                                <li><a title="Scholarships" class="submenulist" href="<?= Url::toDomain() ?>scholarships" onclick="menuNavigate('<?= Url::toDomain() ?>scholarships','Scholarships','','Scholarships')">Scholarships</a></li>
                                <!-- <li><a title="Olympiads" class="submenulist" href="<?= Url::toDomain() ?>olympiad">Olympiads</a></li> -->
                                <!-- <li><a title="Sarkari Exam" class="submenulist" href="<?= Url::toDomain() ?>sarkari-exam/">Sarkari Exam</a></li> -->
                                <!-- <li><a title="Visual Stories" class="submenulist" href="https://webstories.getmyuni.com/">Visual Stories</a></li> -->
                                <!-- <li><a title="QnA" href="<?= Url::toDomain() ?>q-n-a">QnA</a></li> -->
                                <li><a title="College Compare" class="submenulist" href="<?= Url::toDomain() ?>college-compare" onclick="menuNavigate('<?= Url::toDomain() ?>college-compare','College Compare','','College Compare')">College Compare</a></li>
                                <!-- </ul> -->
                                <!-- </div>
                                </div> -->
                            </ul>
                        </li>
                        <?php if (Yii::$app->controller->id == 'site' && Url::toDomain() !=  Url::toBridgeU()): ?>
                            <li><a href="https://ieltsmaterial.com/" title="IELTSMaterial" onclick="menuNavigate('ieltsmaterial.com','IELTSMaterial','','IELTSMaterial')">IELTSMaterial</a></li>
                        <?php endif; ?>
                        <!-- <li><a href="<?= Url::toDomain() ?>college/reviews" title="College Reviews">Reviews</a></li> -->
                        <li><a href="javascript:;" class="spriteIcon searchIcon"></a></li>
                        <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                            <li><a class="writeReview" href="<?= Url::toDomain() ?>review/create" title="Write a Review" onclick="menuNavigate('<?= Url::toDomain() ?>review/create','Write a review','','Write a review')">Write a review</a></li>
                        <?php endif; ?>
                        <!-- <a class="registerNew" id="mobileRegisterNew" href="/site/login">Login/ Register</a> -->
                    <?php endif; ?>
                    <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                        <li id="registerNew"><a class="writeReview" href="/site/login" onclick="menuNavigate('<?= Url::toDomain() ?>/site/login','Login/ Register','','Login/ Register')">Login/ Register</a></li>
                        <li id="loggedInUser"></li>
                    <?php endif; ?>
                    <!-- <li><a class="register" href="<?= Url::toDomain() ?>login" title="Login / Register">Login / Register</a></li> -->
                </ul>
            <?php endif; ?>
            <?php if (str_contains($currentUrl, $createReviewPage) == false): ?>
                <div class="mobileOnly">
                    <a class="spriteIcon searchIcon"></a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php if (false == $isMobile):
    $words = ['contact-us', 'privacy-policy'];
    $headerClass = '';
    $foundWords = array_filter($words, function ($word) use ($currentUrl) {
        return str_contains($currentUrl, $word);
    });

    if (Yii::$app->controller->id == 'site' && empty($foundWords)) {
        $headerClass = 'homePageHeader';
    }


    ?>
    <div id="secondaryHeader-web" class="headerMegaMenu <?= $headerClass ?> "></div>
<?php endif; ?>

<?php if ($isMobile): ?>
    <div class="mobileMenu">
        <div class="slider-menu">
            <p class="spriteIcon closeMenu"></p>
            <div class="slide-top-clearfix"></div>
            <div class="slider-menu-option">
                <div class="guestDisplay">
                    <p class="guestPic spriteIcon">
                        <!-- <img src="/yas/images/userIcon.png" alt="User Icon" width="36" height="36"> -->
                    </p>
                    <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                    <p class="user-text-mobile"><?php
                    if (\Yii::$app->user->isGuest) {
                        echo 'Hi Guest!';
                    } else {
                        $studentName = explode(' ', \Yii::$app->user->identity->name)[0] ?? '';
                        echo 'Hi ' . $studentName;
                    }
                    ?></p>
                    <div class="row">
                        <a class="writeReview" href="<?= Url::toDomain() ?>review/create" title="Write a Review">Write a Review</a>
                        <!-- <span id="deskLogin"><li><a class="writeReview" href="/site/login">Login or Signup</a></li></span> -->
                        <?php if (Yii::$app->user->isGuest): ?>
                            <a class="registerNew" href="/site/login">Login/Register</a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <ul>

                    <!-- <li><a href="<?= Url::toDomain() ?>" title="Home">Home</a></li> -->
                    <li>
                        <a class="sliderCategory" title="Top Colleges in India">Colleges</a>
                        <!--slide submenu colleges-->
                        <div class="slider-submenu  collegesslider">
                            <div class="sub-menu-div">
                                <p class="backToMenu"><span class="spriteIcon angle_left"> </span> Back to Menu</p>
                                <!-- <p>ALL COLLEGES</p> -->
                            </div>
                            <div class="slider-menu-option">
                                <ul>
                                    <li><a title="Engineering" href="<?= Url::toDomain() ?>engineering-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>engineering-colleges','Engineering','Engineering','Engineering')">Engineering</a>
                                    </li>
                                    <li><a title="Management" href="<?= Url::toDomain() ?>management-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>management-colleges','Management','Management','Management')">Management</a>
                                    </li>
                                    <li><a title="Medical" href="<?= Url::toDomain() ?>medical-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>medical-colleges','Medical','Medical','Medical')">Medical</a></li>
                                    <li><a title="Science" href="<?= Url::toDomain() ?>science-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>science-colleges','Science','Science','Science')">Science</a></li>
                                    <li><a title="Commerce" href="<?= Url::toDomain() ?>commerce-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>commerce-colleges','Commerce','Commerce','Commerce')">Commerce</a></li>
                                    <li><a title="Arts" href="<?= Url::toDomain() ?>arts-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>arts-colleges','Arts','Arts','Arts')">Arts</a></li>
                                    <li><a title="Pharmacy" href="<?= Url::toDomain() ?>pharmacy-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>pharmacy-colleges','Pharmacy','Pharmacy','Pharmacy')">Pharmacy</a></li>
                                    <!-- <li><a title="Fashion" href="<?= Url::toDomain() ?>fashion-colleges">Fashion</a></li> -->
                                    <li><a title="Design" href="<?= Url::toDomain() ?>design-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>design-colleges','Design','Design','Design')">Design</a>
                                    </li>
                                    <li><a title="Law" href="<?= Url::toDomain() ?>law-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>law-colleges','Law','Law','Law')">Law</a></li>
                                    <li><a title="Education" href="<?= Url::toDomain() ?>education-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>education-colleges','Education','Education','Education')">Education</a>
                                    </li>
                                    <li><a title="All Colleges" href="<?= Url::toDomain() ?>all-colleges" onclick="menuNavigate('<?= Url::toDomain() ?>education-colleges','All Colleges','All Colleges','All Colleges')">All Colleges
                                            &gt;&gt;</a></li>
                                </ul>
                            </div>
                        </div>
                    </li>
                    <li><a href="<?= Url::toDomain() ?>college/admissions" title="College Admission in India" onclick="menuNavigate('<?= Url::toDomain() ?>college/admissions','Admission','Admission','Admission')">Admission <?= date('Y') ?></a></li>
                    <li>
                        <a href="<?= Url::toDomain() ?>courses" title="Courses in India" onclick="menuNavigate('<?= Url::toDomain() ?>courses','Top Courses','Top Courses','Top Courses')">Top Courses</a>
                        <!--slide submenu Courses-->
                        <!-- <div class="slider-submenu  coursesslider"> -->
                        <!-- <div class="sub-menu-div">
                                <p class="backToMenu"><span class="spriteIcon angle_left"> </span> Back to Menu</p> -->
                        <!-- <p>ALL COURSES</p> -->
                        <!-- </div>
                            <div class="slider-menu-option">
                                <ul>
                                    <li><a title="B.Tech" href="<?= Url::toDomain() ?>btech-course">B.Tech</a>
                                    </li>
                                    <li><a title="M.Tech" href="<?= Url::toDomain() ?>mtech-course">M.Tech</a>
                                    </li>
                                    <li><a title=" BBA" href="<?= Url::toDomain() ?>bba-course">BBA</a></li>
                                    <li><a title=" MBA" href="<?= Url::toDomain() ?>mba-course">MBA</a></li>
                                    <li><a title="Distance MBA" href="<?= Url::toDomain() ?>distance-mba-course">Distance
                                            MBA</a></li>
                                    <li><a title="B.Com" href="<?= Url::toDomain() ?>bcom-course">B.Com</a></li>
                                    <li><a title="M.Com" href="<?= Url::toDomain() ?>mcom-course">M.Com</a></li>
                                    <li><a title="All Courses" href="<?= Url::toDomain() ?>courses">All Courses
                                            &gt;&gt;</a>
                                    </li>
                                </ul>
                            </div>
                        </div> -->
                    </li>
                    <li>
                        <a href="<?= Url::toDomain() ?>exams" title="Entrance Exams in India" onclick="menuNavigate('<?= Url::toDomain() ?>exams','Entrance Exams','Entrance Exams','Entrance Exams')">Entrance Exams</a>
                        <!--slide submenu Exams-->
                        <!-- <div class="slider-submenu  examsslider">
                            <div class="sub-menu-div">
                                <p class="backToMenu parentMenu"><span class="spriteIcon angle_left"> </span> Back
                                    to Menu</p>

                            </div>
                            <div class="slider-menu-option">
                                <ul>
                                    <li><a title="Engineering" href="<?= Url::toDomain() ?>exams/engineering-exams-in-india">Engineering</a>
                                    </li>
                                    <li><a title="Management" href="<?= Url::toDomain() ?>exams/management-exams-in-india">Management</a>
                                    </li>
                                    <li><a title="Medical" href="<?= Url::toDomain() ?>exams/medical-exams-in-india">Medical</a>
                                    </li>
                                    <li><a title="Science" href="<?= Url::toDomain() ?>exams/science-exams-in-india">Science</a>
                                    </li>
                                    <li><a title="Commerce" href="<?= Url::toDomain() ?>exams/commerce-exams-in-india">Commerce</a>
                                    </li>
                                    <li><a title="Architecture" href="<?= Url::toDomain() ?>exams/architecture-exams-in-india">Architecture</a>
                                    </li>
                                    <li><a title="Pharmacy" href="<?= Url::toDomain() ?>exams/pharmacy-exams-in-india">Pharmacy</a>
                                    </li>
                                    <li><a title="Law" href="<?= Url::toDomain() ?>exams/law-exams-in-india">Law</a></li>
                                    <li><a title="Education" href="<?= Url::toDomain() ?>exams/education-exams-in-india">Education</a>
                                    </li>
                                    <li><a title="Dental" href="<?= Url::toDomain() ?>exams/dental-exams-in-india">Dental</a>
                                    </li>
                                    <li><a title="Government" href="<?= Url::toDomain() ?>exams/government-exams-in-india">Government</a>
                                    </li>
                                    <li><a title="Design" href="<?= Url::toDomain() ?>exams/design-exams-in-india">Design</a>
                                    </li>
                                    <li><a title="Paramedical" href="<?= Url::toDomain() ?>exams/paramedical-exams-in-india">Paramedical</a>
                                    </li>
                                    <li><a title="Agriculture" href="<?= Url::toDomain() ?>exams/agriculture-exams-in-india">Agriculture</a>
                                    </li>
                                    <li><a title="Arts" href="<?= Url::toDomain() ?>exams/arts-exams-in-india">Arts</a></li>
                                    <li><a title="All Exams" href="<?= Url::toDomain() ?>exams">All
                                            Exams&gt;&gt;</a></li>
                                </ul>
                            </div>
                        </div> -->

                    </li>
                    <li>
                        <a href="<?= Url::toDomain() ?>boards" title="Education Boards in India" onclick="menuNavigate('<?= Url::toDomain() ?>boards','Boards','Boards','Boards')">Boards</a>
                        <!--slide submenu Boards-->
                        <!-- <div class="slider-submenu  boardsslider">
                            <div class="sub-menu-div">
                                <p class="backToMenu parentMenu"><span class="spriteIcon angle_left"> </span> Back
                                    to Menu</p>

                            </div>
                            <div class="slider-menu-option">
                                <ul>
                                    <li><a title="CBSE 10th Board" href="<?= Url::toDomain() ?>cbse-10th-board/b">CBSE 10th Board</a>
                                    </li>
                                    <li><a title="CBSE 12th Board" href="<?= Url::toDomain() ?>cbse-12th-board/b">CBSE 12th Board</a>
                                    </li>
                                    <li><a title="CISCE 10th Board" href="<?= Url::toDomain() ?>icse-10th-board-cisce/b">CISCE 10th
                                            Board</a></li>
                                    <li><a title="CISCE 12th Board" href="<?= Url::toDomain() ?>isc-12th-cisce/b">CISCE 12th Board</a>
                                    </li>
                                    <li><a title="Bihar 10th Board" href="<?= Url::toDomain() ?>bihar-board-10th-matric-bseb/b">Bihar
                                            10th Board</a></li>
                                    <li><a title="Bihar 12th Board" href="<?= Url::toDomain() ?>bihar-board-12th-intermediate-bseb/b">Bihar
                                            12th Board</a></li>
                                    <li><a title="All Boards" href="<?= Url::toDomain() ?>boards">All Boards
                                            &gt;&gt;</a></li>
                                </ul>
                            </div>
                        </div> -->
                    </li>
                    <!-- <li>
                        <a class="sliderCategory" title="Study Abroad">Study Abroad</a>
                        <div class="slider-submenu studyAbroadslider">
                            <div class="sub-menu-div">
                                <p class="backToMenu parentMenu"><span class="spriteIcon angle_left"> </span> Back
                                    to Menu</p>

                            </div>
                            <div class="slider-menu-option">
                                <ul>
                                    <li><a href="<?= Url::toDomain() ?>canada" title="Study in Canada">Study in
                                            Canada</a></li>
                                    <li><a href="<?= Url::toDomain() ?>uk" title="Study in UK">Study in UK</a>
                                    </li>
                                    <li><a href="<?= Url::toDomain() ?>usa" title="Study in USA">Study in USA</a>
                                    </li>
                                    <li><a href="<?= Url::toDomain() ?>australia" title="Study in USA">Study in
                                            Australia</a></li>
                                    <li><a href="<?= Url::toDomain() ?>germany" title="Study in USA">Study in
                                            Germany</a></li>
                                    <li><a href="https://ieltsmaterial.com/" target="_blank">IELTS Material</a></li>
                                </ul>
                            </div>
                        </div>
                    </li> -->
                    <!-- <li><a href="<?= Url::toDomain() ?>reviews" title="Reviews">College Reviews</a></li> -->
                    <?php if (Url::toDomain() ==  Url::toGetmyuni()): ?>
                        <li><a href="<?= Url::toNewGetmyuni() ?>" title="News" onclick="menuNavigate('<?= Url::toNewGetmyuni() ?>News','News','News')">News</a></li>
                    <?php else: ?>
                        <li><a href="<?= Url::toDomain() ?>news" title="News" onclick="menuNavigate('<?= Url::toDomain() ?>News','News','News')">News</a></li>
                    <?php endif; ?>
                    <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                        <li>
                            <a class="sliderCategory">Go Abroad</a>
                            <!--slide submenu colleges-->
                            <div class="slider-submenu  collegesslider">
                                <div class="sub-menu-div">
                                    <p class="backToMenu"><span class="spriteIcon angle_left"> </span> Back to Menu</p>
                                    <!-- <p>ALL COLLEGES</p> -->
                                </div>
                                <div class="slider-menu-option">
                                    <ul>
                                        <li><a title="Study Abroad" href="https://admission.getmyuni.com/lp/study-abroad">Study Abroad</a></li>
                                        <li><span  onclick="gmu.url.goto('<?= Url::toDomain() ?>canada')" title="Study in Canada">Study in Canada</span></li>
                                        <li><span onclick="gmu.url.goto('<?= Url::toDomain() ?>uk')" title="Study in UK">Study in UK</span></li>
                                        <li><span onclick="gmu.url.goto('<?= Url::toDomain() ?>usa')" title="Study in USA">Study in USA</span></li>
                                        <li><span onclick="gmu.url.goto('<?= Url::toDomain() ?>australia')" title="Study in Australia">Study in Australia</span></li>
                                        <li><span onclick="gmu.url.goto('<?= Url::toDomain() ?>germany')" title="Study in Germany">Study in Germany</span></li>
                                        <!-- <li><span onclick="gmu.url.goto('https://ieltsmaterial.com/')" title="IELTS Prep">IELTS Prep</span></li> -->
                                        <!-- <li><span onclick="gmu.url.goto('https://getgis.org/')" title="Immigration">Immigration</span></li> -->

                                    </ul>
                                </div>
                            </div>
                        </li>
                    <?php endif;?>
                    <li>
                        <a class="sliderCategory" title="Resources">More</a>
                        <!--slide submenu resource-->

                        <div class="slider-submenu  resourcesslider">
                            <div class="sub-menu-div">
                                <p class="backToMenu parentMenu"><span class="spriteIcon angle_left"> </span> Back
                                    to Menu</p>

                            </div>
                            <div class="slider-menu-option">
                                <ul>
                                    <li><a title="Articles" href="<?= Url::toDomain() ?>articles" onclick="menuNavigate('<?= Url::toDomain() ?>articles','articles','articles')" >Articles</a>
                                    </li>
                                    <!-- <li><a title="Olympiads" href="<?= Url::toDomain() ?>olympiad">Olympiad</a> -->
                    </li>
                    <li><a title="Scholarships" href="<?= Url::toDomain() ?>scholarships" onclick="menuNavigate('<?= Url::toDomain() ?>scholarships','scholarships','scholarships')" >Scholarships</a></li>
                    <!-- <li><a title="Competitions" href="<?= Url::toDomain() ?>competitions/student-competitions">Competitions</a> -->
                    </li>
                    <!-- <li><a title="Compare Colleges" href="<?= Url::toDomain() ?>college-compare">Compare Colleges</a>
                                    </li> -->
                    <!-- <li><a title="Sarkari Exam" href="<?= Url::toDomain() ?>sarkari-exam/">Sarkari Exam</a></li> -->
                    <!-- <li><a title="Visual Stories" class="submenulist" href="https://webstories.getmyuni.com/">Visual Stories</a></li> -->
                    <li><a title="College Compare" class="submenulist" href="<?= Url::toDomain() ?>college-compare" onclick="menuNavigate('<?= Url::toDomain() ?>college-compare','scholarships','scholarships')">College Compare</a></li>
                </ul>
            </div>
        </div>
        </li>
        </ul>
        <?php
        if (!\Yii::$app->user->isGuest) {
            echo '<div class="hamburgerMenuOptions">';
            echo '<a href="/user-profile" class="myProfileOption"><div style="display: block"><span class="spriteIcon myProfileIcon"></span>My Profile</div></a>';
            echo '<a  href="/site/logout" title="Logout" data-method="post" class="logOutOption"><div><span class="spriteIcon logoutIcon"></span>Log Out</div></a>';
            echo '</div>';
        }
        ?>


    </div>
    <!--slide submenu Courses-->
    <div id="secondaryHeader-wap"></div>

    </div>
    </div>
<?php endif; ?>
<div class="advanceSearch">
    <div class="searchWrap active">
        <a class="close-layer desktopOnly"></a>
        <div class="searchContent">
            <div class="seachInner">
                <div class="sugstrbox">
                    <div class="inputDiv">
                        <span class="mobileOnly leftArrow">
                            <i class="spriteIcon backIcon"></i>
                        </span>
                        <input type="text" autocomplete="off" id="seachInput" placeholder="Search Colleges, Courses, Exams" class="form-control seachInput" tabindex="1" value="">
                        <div class="close-icone inputClose">
                            <span class="close-span"></span>
                        </div>
                        <p id="letterCount"></p>
                    </div>
                    <?php if (!$isMobile): ?>
                        <button type="submit" class="primaryBtn desktopOnly">Search</button>
                    <?php endif; ?>
                </div>
                <div class="showsuggestorBox"></div>
                <div class="rctSearchWrap" id="showSearchDiv"></div>
            </div>
        </div>
    </div>
</div>