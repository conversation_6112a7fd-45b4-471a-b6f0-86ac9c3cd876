<?php

use common\helpers\ContentHelper;
use common\models\LiveUpdate;
use common\models\News;
use common\services\v2\NewsService;
use frontend\helpers\Url;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Schema;
use yii\helpers\BaseStringHelper;
use common\helpers\DataHelper;

// utils
$currentUrl = Url::base(true) . Url::current();
$isMobile = \Yii::$app->devicedetect->isMobile();
$authorImage = !empty($author) ? ContentHelper::getUserProfilePic($author->slug) : '';
$liveTagID = LiveUpdate::LIVE_NEWS_TAG_ID;

//title
$this->title = !empty($content) && !empty($content->meta_title) ? $content->meta_title . ' - Getmyuni' : '';
$this->context->description = !empty($content) && !empty($content->meta_description) ? $content->meta_description : '';
$this->registerMetaTag(['name' => 'robots', 'content' => 'max-image-preview:large']);

// @todo
// add no index follow if modified is less than live date

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
if ($post->lang_code != 1) {
    $this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'News'), 'url' => [Url::toNews(DataHelper::getLangCode($post->lang_code))], 'title' => ''];
} else {
    $this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'News'), 'url' => [Url::toNews()], 'title' => ''];
}

if (!empty($category)) {
    $this->params['breadcrumbs'][] = ['label' => $category->name ?? '' . Yii::t('app', 'News'), 'url' => [Url::toNewsDetail($category->slug)], 'title' => ($category->name ?? '') . ' News'];
}
$this->params['breadcrumbs'][] = !empty($content) && !empty($content->meta_title) ? $content->meta_title : $post->name;

// top search
if (!empty($topsearchNews)) {
    $this->params['topsearchNews'] = $topsearchNews;
}

$this->registerCssFile(Yii::$app->params['cssPath'] . 'article-detail.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'news.css', ['depends' => [AppAsset::class]]);
if ($isMobile) {
    $this->registerCssFile(Yii::$app->params['cssPath'] . 'news_lead.css', ['depends' => [AppAsset::class]]);
}

// meta tags
$this->registerLinkTag(['rel' => 'amphtml', 'href' => !empty($post) ? Url::toAmp(Url::toNewsDetail($post->slug, DataHelper::getLangCode($post->lang_code))) : '']);
if (!empty($content->meta_keywords)) {
    $this->registerMetaTag(['name' => 'news_keywords', 'content' => $content->meta_keywords]);
    $this->registerMetaTag(['name' => 'Keywords', 'content' => $content->meta_keywords]);
}
$this->registerMetaTag(['name' => 'Googlebot-news', 'content' => 'index, follow']);

if (!empty($post) && !empty($post->banner_image)) {
    $this->registerMetaTag(['property' => 'og:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'og:image:height', 'content' => '667']);
    $this->registerMetaTag(['property' => 'og:image:alt', 'content' => $this->title]);
    $this->registerMetaTag(['property' => 'twitter:image:type', 'content' => 'image/jpeg']);
    $this->registerMetaTag(['property' => 'twitter:image:width', 'content' => '1200']);
    $this->registerMetaTag(['property' => 'twitter:image:height', 'content' => '667']);
    $this->registerLinkTag(['rel' => 'preload', 'as' => 'image', 'fetchpriority' => 'high', 'href' => $post->banner_image ? Url::toNewsImages($post->banner_image) : '']);
    $this->context->ogImage = $post->banner_image ? Url::toNewsImages($post->banner_image) : '';
}

if (!empty($translation_data)) {
    if (!empty($translation_data[0]['lang'])) {
        $msg = 'Switch to Hindi';
    } else {
        $msg = 'Switch to English';
    }
    $url = Url::toNewsDetail($translation_data[0]['slug'], $translation_data[0]['lang']);
    if (!empty($translation_data[0]['lang'])) {
        $this->registerLinkTag(['href' =>  Url::base(true) . $url, 'rel' => 'alternate', 'hreflang' => $translation_data[0]['lang']]);
    } else {
        $translation_data[0]['lang'] = 'en';
        $this->registerLinkTag(['href' =>  Url::base(true) . $url, 'rel' => 'alternate', 'hreflang' => $translation_data[0]['lang']]);
    }
} else {
    $url = '#';
}

$liveUpdateDate = !empty($post->liveUpdate) ? $post->liveUpdate[0]->updated_at : '';
// Schema.org
$this->params['schema'] = \yii\helpers\Json::encode([
    Schema::newsSchema($post, $liveUpdateDate)
], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

//Live Update Schema
if (!empty($post) && isset($post->is_live) && $post->is_live == News::IS_LIVE_YES) {
    if (!empty($post->liveUpdate)) {
        $this->params['schema1'] = \yii\helpers\Json::encode([
            Schema::liveNewsSchema($post, $post->liveUpdate)
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }
}

//autofetch lead form based product mapping
$leadValues = (new NewsService)->getNewsAutoFetchValues($post);

$ctaText = 'SUBSCRIBE';
$this->params['entity'] = News::ENTITY_NEWS;
$this->params['entity_id'] = $post->id;
$this->params['dynamicCta'] = $dynamicCta ?? [];
$this->params['entityDisplayName'] = $post->name ?? '';
$this->params['page_category'] = 'news-cta';
$this->params['entitySlug'] = $exam->slug ?? '';
$this->params['canonicalUrl'] = $currentUrl;
$this->params['streamWebengage'] = $post->stream_id ? $article->stream->name : null;
$this->params['levelWebengage'] = $post->highest_qualification ? $post->degree->name : null;
if (!empty($post->exam[0])) {
    $this->params['examWebengage'] = $post->exam[0]->name ;
} else {
    $this->params['examWebengage'] = null ;
}
?>

<header class="bannerDiv">
    <h1>
        <?php $tags = (new NewsService)->liveTagExpiredAt($post->expired_at, $post->is_live); ?>
        <?php
        if (!empty($tags) && ($tags == News::IS_LIVE_YES)) {
            echo $this->render('partials/_live-updates-icon', [
                'models' => $post->is_live,
                'isAmp'  => 0,
                'liveTagID' => $liveTagID,
                'smallIcone' => 0
            ]);
        }
        ?>
        <?= $content ? $content->h1 : '' ?>
    </h1>
    <?php if (!empty($content->meta_description)): ?>
        <h2><?= $content->meta_description ?></h2>
    <?php endif; ?>
    <div class="authorInfoAndTranslateBtn">
        <div class="updated-info row">
            <?php if (!empty($authorImage)): ?>
                <div class="updatedBy">
                    <img class="lazyload" loading="lazy" data-src="<?= $authorImage ?? '' ?>" src="<?= $authorImage ?? '' ?>" alt="">
                </div>
            <?php endif; ?>
            <?php
            $updatedAt = $content->updated_at ? new DateTime($content->updated_at) : '';
            $date = $updatedAt ? $updatedAt->format('M d, Y') : '';
            $time = $updatedAt ? $updatedAt->format('h:i A') : '';
            ?>
            <?php if (!empty($authorImage)): ?>
                <div class="authorAndDate">
                    <a href="<?= Url::toAllAuthorPost($author->slug, DataHelper::getLangCode($post->lang_code)) ?>" class="authorName"><?= $author->name ?? '' ?></a>
                    <span class="spriteIcon verifiedBlueTickIcon"></span>
                    <p>Updated on - <?= Yii::$app->formatter->asDate($date) . ' | ' . $time ?> IST
                        <!-- <a href="javascript::">Career Guide,</a>
                                        <a href="javascript::"> Full Form</a> -->
                    </p>
                </div>
            <?php endif; ?>
            <!--ul>
                            <p>Share: </p>
                            <li><a href="https://www.facebook.com/sharer/sharer.php?u=" target="_blank" rel="noopener nofollow" class="spriteIcon greyFbIcon" title="Share on Facebook"></a></li!>
                            <li><a href="https://twitter.com/share?url=" class="spriteIcon greyTwitterIcon" rel="noopener nofollow" target="_blank" title="Share on Twitter"></a></li>
                        </ul-->
        </div>
        <div class="col-md-6 cta-btn-row-fix">
            <div class="ctaColumn">
                <div class="ctaRow">
                    <div class="lead-cta" data-entity="news" data-lead_cta='0'></div>
                </div>
            </div>
        </div>
        <?php
        if (!empty($translation_data) && Url::toDomain() !=  Url::toBridgeU()) {
            ?>
            <a href="<?= $url ?>" class="translateBtn">
                <span class="webpSpriteIcon translateIcon1"></span>
                <?= $msg ?>
            </a>
            <?php
        }
        ?>
    </div>
</header>
<div class="row">
    <div class="col-md-8">
        <main>
            <article>
                <?php if (!empty($post->audio)): ?>
                    <div class="audio-container">
                        <span class="audio-text">Listen to this news</span>
                        <audio controls id="myAudio" class="center" preload="none">
                            <source src="<?php echo $post->audio ?>" type="audio/mpeg">
                            Your browser does not support the audio element.
                        </audio>
                    </div>
                <?php endif; ?>
                <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <aside>
                        <div class="horizontalRectangle top">
                            <div class="appendAdDiv" style="background:#EAEAEA;<?= $isMobile ? 'height: 50px;' : '' ?>">
                                <?php /*if ($isMobile): ?>
                                <?php echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WAP_ATF_300x50', '[300,50]') ?>
                            <?php else: */ ?>
                                <?php echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WEB_ATF_728x90', '[728,90]') ?>
                                <?php //endif;
                                ?>
                            </div>
                        </div>
                    </aside>
                <?php endif; ?>


                <?php if (!empty($post) && !empty($post->banner_image)): ?>
                    <div class="bannerImg">
                        <img width="794px" height="400px" title="<?= $post->name ?? '' ?>" data-src="<?= !empty($post->banner_image) ? Url::toNewsImages($post->banner_image) : Url::toNewsImages() ?>" src="<?= !empty($post->banner_image) ? Url::toNewsImages($post->banner_image) : Url::toNewsImages() ?>" alt="<?= $post->name ?? '' ?>">
                    </div>
                    <div class="figureCaption">
                        <?= !empty($post->banner_caption) ? ContentHelper::htmlDecode($post->banner_caption, true) : '' ?>
                    </div>
                <?php endif; ?>

                <div class="articleInfo">
                    <?= $content ? ContentHelper::removeStyleTag(stripslashes(
                        html_entity_decode(DataHelper::parseDomainUrlInContent($content->content))
                    )) : '' ?>

                    <?php if (!empty($post) && !empty($liveUpdate)): ?>
                        <?= $this->render('partials/_live-updates', [
                            'models' => $liveUpdate ?? ''
                        ]) ?>
                    <?php endif; ?>
                </div>
                <div class="shareSection">
                    <div>
                        <p>Share Via</p>
                        <?php if (!$isMobile) { ?>
                            <a target="_blank" href="https://web.whatsapp.com/send?text=<?= $currentUrl; ?>" data-action="share/whatsapp/share"><span class="spriteIcon shareIcon whatsappIcon"></span></a>
                        <?php  } else { ?>
                            <a target="_blank" href="https://api.whatsapp.com/send?text=<?= $currentUrl; ?>" data-action="share/whatsapp/share"><span class="spriteIcon shareIcon whatsappIcon"></span></a>
                        <?php } ?>
                        <a target="_blank" href="http://twitter.com/share?url=<?= $currentUrl; ?>"> <span class="spriteIcon shareIcon bigTwitterIcon"></span> </a>
                        <span class="spriteIcon shareIcon gmailIcon" onclick="shareViaGmail('<?= $currentUrl ?>')"></span>
                        <span onclick="copyToClipboard('<?= $currentUrl ?>')" title="Copy to clipboard" class="spriteIcon shareIcon copyClipboardIcon"></span>
                        <span id="custom-tooltip" style="display:none;">copied!</sapn>
                    </div>
                    <a class="followUs" target="_blank" href="https://news.google.com/publications/CAAqBwgKMKLYqAswlOPAAw?hl=en-IN&gl=IN&ceid=IN:en">
                        <p>Follow Us On <span>Google News</span></p><span class="spriteIcon shareIcon googleNewsIcon"></span>
                    </a>
                </div>
                <?php if (!empty($readNextFeature)): ?>
                    <?= $this->render('partials/_read-next-feature', [
                        'readNextNews' => $readNextFeature,
                        'isMobile' => $isMobile,
                        'post' => $post
                    ]) ?>
                <?php endif; ?>
            </article>
        </main>
        <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
            <aside>
                <div class="horizontalRectangle down">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WAP_MTF_2_300x250', '[300,250]') ?>
                        <?php else: ?>
                            <?php echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WEB_MTF_2_728x90', '[728,90]') ?>
                        <?php endif; ?>
                    </div>
                </div>
            </aside>
        <?php endif; ?>
    </div>
    <div class="col-md-4">
        <aside>
            <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="getSupport">
                    <!--div class="row">
                        <img src="<?= '/yas/images/bulbIcon.svg' ?>" width="80" height="80" alt="">
                        <p><!-?= //Yii::t('app', 'Get News Alerts and Guidance'); ?></p>
                    </div-->
                    <p class="getSupport__subheading"><?= Yii::t('app', 'Get News Alerts and Guidance'); ?></p>
                    <div class="button__row__container">
                        <div class="lead-cta" data-entity="news" data-lead_cta='6'></div>
                    </div>
                </div>
                <div class="squareDiv">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WAP_BTF_300x250', '[300,250]') ?>
                    </div>
                </div>
            <?php endif; ?>
            <?php if (!empty($featured)): ?>
                <?=
                $this->render('partials/_sidebar-tab-news', [
                    'featured' => $featured,
                    'recents' => $recents,
                    'post' => $post ?? '',
                    'isAmp'  => 0,
                    'liveTagID' => $liveTagID,
                    'smallIcone' => 1
                ]);
                ?>
            <?php endif; ?>
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="verticleRectangle">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WAP_MTF_1_300x250', '[300,250]') ?>
                        <?php else: ?>
                            <?php echo Ad::unit('GMU_NEWS_DETAIL_PAGE_WEB_MTF_1_300x600', '[300,600]') ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </aside>
    </div>
</div>

<section class="commentSection">
    <?= $this->render('/partials/comment/_form', [
        'model' => $commentForm,
        'entity' => News::ENTITY_NEWS,
        'entity_id' => $post->id
    ]) ?>
    <?= $this->render('/partials/comment/_comment', [
        'comments' => $comments,
        'entity' => News::ENTITY_NEWS,
        'entityId' => $post->id
    ]) ?>
</section>

<?php if (!empty($relatedNws) && empty($newsMapping) && count($relatedNws) > 1): ?>
    <section class="latestInfoSection">
        <h2 class="row">
            RELATED NEWS</h2>
        <div class="latestInfoListContainer four-cardDisplay">
            <!--i class="spriteIcon scrollLeft over"></i>
            <i class="spriteIcon scrollRight"></i-->
            <div class="latestInfoList row">
                <?php $relatedNwsIteration = 0; ?>
                <?php foreach ($relatedNws as $related): ?>
                    <?php if ($post->slug == $related['slug'] || $relatedNwsIteration > 3): ?>
                        <?php continue; ?>
                    <?php endif; ?>
                    <div class="latestInfoDiv">
                        <a href="<?= Url::toNewsDetail($related['slug'], DataHelper::getLangCode($related['lang_code'])) ?>" title="<?= $related['title'] ?? '' ?>">
                            <figure>
                                <img class="lazyload" width="274" height="207" loading="lazy" data-src="<?= !empty($related['banner_image']) ? Url::toNewsImages($related['banner_image']) : Url::toNewsImages() ?>" src="<?= !empty($related['banner_image']) ? Url::toNewsImages($related['banner_image']) : Url::toNewsImages() ?>" alt="">
                            </figure>
                            <div class="latestInfoTxt">
                                <p><?= !empty($related['title']) ? BaseStringHelper::truncate($related['title'] ?? '', 95) : '' ?></p>
                            </div>
                        </a>
                        <p class="articleAuthorName"><?= !empty($related['author']) ? $related['author'] : '' ?></p>
                    </div>
                    <?php $relatedNwsIteration++ ?>
                <?php endforeach;  ?>
            </div>
        </div>
    </section>

<?php endif; ?>

<!-- related news -->
<?php if (!empty($newsMapping)): ?>
    <?= $this->render('../partials/_productNewsCard', [
        'news' => $newsMapping,
    ]); ?>
<?php endif; ?>
<?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
    <div id="comment-reply-form-js"></div>
<?php endif; ?>