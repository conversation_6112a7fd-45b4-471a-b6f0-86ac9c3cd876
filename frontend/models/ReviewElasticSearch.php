<?php

namespace frontend\models;

use common\models\Review;
use yii\base\Model;
use yii\data\Pagination;
use yii\helpers\ArrayHelper;
use yii\elasticsearch\Query;
use yii\web\NotFoundHttpException;
use common\models\ReviewFilter;

class ReviewElasticSearch extends Review
{
    public $sort = 'review_created_at';
    
    public $city;
    
    public $state;
    
    public $course;
    
    public $stream;
    
    public $batch;
    
    public $selectedFilters = [];
    
    public $totalCount;
    
    public $availableFacets;
    
    public $currentPage = 1;
    
    protected $perPage = 10;
    
    public static $propertyMapping = [
        'City' => 'city',
        'State' => 'state',
        'Courses' => 'course',
        'Streams' => 'stream',
        'Batch' => 'batch',
    ];

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sort', 'city', 'state', 'course', 'stream'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied using Elasticsearch
     *
     * @param array $params
     * @param array $bodyParams
     *
     * @return array
     */
    public function search($params, $bodyParams = [])
    {
        if (isset($bodyParams['page']) && !empty($bodyParams['page']) && $bodyParams['page'] != 'undefined') {
            $this->currentPage = (int) $bodyParams['page'];
            unset($bodyParams['page']);
        }

        if (isset($bodyParams['sortBy']) && !empty($bodyParams['sortBy']) && $bodyParams['sortBy'] != 'undefined') {
            $this->sort = $bodyParams['sortBy'];
            unset($bodyParams['sortBy']);
        }

        $this->loadRequests($params, $bodyParams);
        
        // Build Elasticsearch query
        $query = new Query();
        $filters = [];
        
        // Base filter for active reviews
        $filters['bool']['must'][] = ['match' => ['status' => Review::STATUS_APPROVED]];
        
        // Apply filters
        if ($this->state) {
            $filters['bool']['must'][] = ['terms' => ['state_slug' => $this->state]];
        }
        
        if ($this->city) {
            $filters['bool']['must'][] = ['terms' => ['city_slug' => $this->city]];
        }
        
        if ($this->batch) {
            $batchValues = array_map('intval', $this->batch);
            $filters['bool']['must'][] = ['terms' => ['batch' => $batchValues]];
        }
        
        if ($this->course) {
            $filters['bool']['must'][] = ['terms' => ['course_slug' => $this->course]];
        }
        
        if ($this->stream) {
            $filters['bool']['must'][] = ['terms' => ['stream_slug' => $this->stream]];
        }
        
        $query->query($filters);
        
        // Apply sorting
        $sortField = $this->getSortField();
        if ($sortField) {
            $query->orderBy($sortField);
        }
        
        // Set pagination
        $offset = ($this->currentPage - 1) * $this->perPage;
        $query->offset($offset)->limit($this->perPage);
        
        // Set index
        $query->from('reviews', 'review');

        // Execute query
        $command = $query->createCommand();
        $result = $command->search();

        // Get accurate total count using a separate count query
        $totalCount = $this->getAccurateTotalCount($filters);

        $results = $this->formatResults($result['hits']['hits'] ?? []);
        
        // Load facets
        $this->loadElasticFacets();
        
        // Make unique selectedFilters
        $this->selectedFilters = ArrayHelper::index($this->selectedFilters, 'slug');
        
        $pagination = new Pagination([
            'totalCount' => $totalCount,
            'defaultPageSize' => $this->perPage,
            'page' => $this->currentPage - 1, // Pagination uses 0-based indexing
        ]);
        
        return [
            'results' => $results,
            'pagination' => $pagination,
            'sort' => $this->sort
        ];
    }
    
    /**
     * Get sort field for Elasticsearch
     */
    private function getSortField()
    {
        switch ($this->sort) {
            case 'lowest_rating':
                return ['review_overall_rating' => SORT_ASC];
            case 'highest_rating':
                return ['review_overall_rating' => SORT_DESC];
            case 'oldest_rating':
                return ['review_created_at' => SORT_ASC];
            case 'newest_rating':
            case 'review_created_at':
            default:
                return ['review_created_at' => SORT_DESC];
        }
    }
    
    /**
     * Get accurate total count using Elasticsearch _count API
     */
    private function getAccurateTotalCount($filters)
    {
        try {
            // Get Elasticsearch connection info
            $db = \common\models\ReviewElastic::getDb();
            $nodes = $db->nodes;
            $serverUrl = 'http://' . $nodes[0]['http_address'];

            // Build count query
            $countQuery = [
                'query' => $filters
            ];

            // Use cURL to call _count API directly
            $url = rtrim($serverUrl, '/') . '/reviews/_count';

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($countQuery));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if (isset($data['count'])) {
                    return $data['count'];
                }
            }

            // Fallback to 0 if count API fails
            return 0;

        } catch (\Exception $e) {
            // Fallback to 0 if count fails
            unset($e); // Suppress unused variable warning
            return 0;
        }
    }

    /**
     * Format Elasticsearch results
     */
    private function formatResults($hits)
    {
        $results = [];
        foreach ($hits as $hit) {
            $results[] = $hit['_source'];
        }
        return $results;
    }
    
    /**
     * Load facets using Elasticsearch aggregations
     */
    private function loadElasticFacets()
    {
        $this->loadElasticStateFacet();
        $this->loadElasticCityFacet();
        $this->loadElasticStreamFacet();
        $this->loadElasticCourseFacet();
        $this->loadElasticBatchFacet();
    }
    
    /**
     * Load state facets from Elasticsearch
     */
    private function loadElasticStateFacet()
    {
        $query = new Query();
        $filters = ['bool' => ['must' => []]];
        
        // Base filter
        $filters['bool']['must'][] = ['match' => ['status' => Review::STATUS_APPROVED]];
        
        // Apply other filters except state
        if ($this->city) {
            $filters['bool']['must'][] = ['terms' => ['city_slug' => $this->city]];
        }
        if ($this->batch) {
            $batchValues = array_map('intval', $this->batch);
            $filters['bool']['must'][] = ['terms' => ['batch' => $batchValues]];
        }
        if ($this->course) {
            $filters['bool']['must'][] = ['terms' => ['course_slug' => $this->course]];
        }
        if ($this->stream) {
            $filters['bool']['must'][] = ['terms' => ['stream_slug' => $this->stream]];
        }
        
        $query->query($filters);
        $query->addAggregate('states', ['terms' => ['field' => 'state_slug', 'size' => 100]]);
        $query->from('reviews', 'review');
        $query->limit(0); // We only want aggregations
        
        $command = $query->createCommand();
        $result = $command->search();
        
        $facets = [];
        if (isset($result['aggregations']['states']['buckets'])) {
            foreach ($result['aggregations']['states']['buckets'] as $bucket) {
                $facets[] = [
                    '_id' => $bucket['key'],
                    'count' => $bucket['doc_count']
                ];
            }
        }
        
        $this->availableFacets['state'] = $facets;
    }
    
    /**
     * Load city facets from Elasticsearch
     */
    private function loadElasticCityFacet()
    {
        $query = new Query();
        $filters = ['bool' => ['must' => []]];
        
        // Base filter
        $filters['bool']['must'][] = ['match' => ['status' => Review::STATUS_APPROVED]];
        
        // Apply other filters except city
        if ($this->state) {
            $filters['bool']['must'][] = ['terms' => ['state_slug' => $this->state]];
        }
        if ($this->batch) {
            $batchValues = array_map('intval', $this->batch);
            $filters['bool']['must'][] = ['terms' => ['batch' => $batchValues]];
        }
        if ($this->course) {
            $filters['bool']['must'][] = ['terms' => ['course_slug' => $this->course]];
        }
        if ($this->stream) {
            $filters['bool']['must'][] = ['terms' => ['stream_slug' => $this->stream]];
        }
        
        $query->query($filters);
        $query->addAggregate('cities', ['terms' => ['field' => 'city_slug', 'size' => 100]]);
        $query->from('reviews', 'review');
        $query->limit(0); // We only want aggregations
        
        $command = $query->createCommand();
        $result = $command->search();
        
        $facets = [];
        if (isset($result['aggregations']['cities']['buckets'])) {
            foreach ($result['aggregations']['cities']['buckets'] as $bucket) {
                $facets[] = [
                    '_id' => $bucket['key'],
                    'count' => $bucket['doc_count']
                ];
            }
        }
        
        $this->availableFacets['city'] = $facets;
    }

    /**
     * Load stream facets from Elasticsearch
     */
    private function loadElasticStreamFacet()
    {
        $query = new Query();
        $filters = ['bool' => ['must' => []]];

        // Base filter
        $filters['bool']['must'][] = ['match' => ['status' => Review::STATUS_APPROVED]];

        // Apply other filters except stream
        if ($this->state) {
            $filters['bool']['must'][] = ['terms' => ['state_slug' => $this->state]];
        }
        if ($this->city) {
            $filters['bool']['must'][] = ['terms' => ['city_slug' => $this->city]];
        }
        if ($this->batch) {
            $batchValues = array_map('intval', $this->batch);
            $filters['bool']['must'][] = ['terms' => ['batch' => $batchValues]];
        }
        if ($this->course) {
            $filters['bool']['must'][] = ['terms' => ['course_slug' => $this->course]];
        }

        $query->query($filters);
        $query->addAggregate('streams', ['terms' => ['field' => 'stream_slug', 'size' => 100]]);
        $query->from('reviews', 'review');
        $query->limit(0); // We only want aggregations

        $command = $query->createCommand();
        $result = $command->search();

        $facets = [];
        if (isset($result['aggregations']['streams']['buckets'])) {
            foreach ($result['aggregations']['streams']['buckets'] as $bucket) {
                $facets[] = [
                    '_id' => $bucket['key'],
                    'count' => $bucket['doc_count']
                ];
            }
        }

        $this->availableFacets['stream'] = $facets;
    }

    /**
     * Load course facets from Elasticsearch
     */
    private function loadElasticCourseFacet()
    {
        $query = new Query();
        $filters = ['bool' => ['must' => []]];

        // Base filter
        $filters['bool']['must'][] = ['match' => ['status' => Review::STATUS_APPROVED]];

        // Apply other filters except course
        if ($this->state) {
            $filters['bool']['must'][] = ['terms' => ['state_slug' => $this->state]];
        }
        if ($this->city) {
            $filters['bool']['must'][] = ['terms' => ['city_slug' => $this->city]];
        }
        if ($this->batch) {
            $batchValues = array_map('intval', $this->batch);
            $filters['bool']['must'][] = ['terms' => ['batch' => $batchValues]];
        }
        if ($this->stream) {
            $filters['bool']['must'][] = ['terms' => ['stream_slug' => $this->stream]];
        }

        $query->query($filters);
        $query->addAggregate('courses', ['terms' => ['field' => 'course_slug', 'size' => 100]]);
        $query->from('reviews', 'review');
        $query->limit(0); // We only want aggregations

        $command = $query->createCommand();
        $result = $command->search();

        $facets = [];
        if (isset($result['aggregations']['courses']['buckets'])) {
            foreach ($result['aggregations']['courses']['buckets'] as $bucket) {
                $facets[] = [
                    '_id' => $bucket['key'],
                    'count' => $bucket['doc_count']
                ];
            }
        }

        $this->availableFacets['course'] = $facets;
    }

    /**
     * Load batch facets from Elasticsearch
     */
    private function loadElasticBatchFacet()
    {
        $query = new Query();
        $filters = ['bool' => ['must' => []]];

        // Base filter
        $filters['bool']['must'][] = ['match' => ['status' => Review::STATUS_APPROVED]];

        // Apply other filters except batch
        if ($this->state) {
            $filters['bool']['must'][] = ['terms' => ['state_slug' => $this->state]];
        }
        if ($this->city) {
            $filters['bool']['must'][] = ['terms' => ['city_slug' => $this->city]];
        }
        if ($this->course) {
            $filters['bool']['must'][] = ['terms' => ['course_slug' => $this->course]];
        }
        if ($this->stream) {
            $filters['bool']['must'][] = ['terms' => ['stream_slug' => $this->stream]];
        }

        $query->query($filters);
        $query->addAggregate('batches', ['terms' => ['field' => 'batch', 'size' => 100]]);
        $query->from('reviews', 'review');
        $query->limit(0); // We only want aggregations

        $command = $query->createCommand();
        $result = $command->search();

        $facets = [];
        if (isset($result['aggregations']['batches']['buckets'])) {
            foreach ($result['aggregations']['batches']['buckets'] as $bucket) {
                $facets[] = [
                    '_id' => $bucket['key'],
                    'count' => $bucket['doc_count']
                ];
            }
        }

        $this->availableFacets['batch'] = $facets;
    }

    /**
     * Load parameters from URL and body params
     */
    public function loadRequests($params, $bodyParams)
    {
        if ((empty($this->loadParams($params)) && !empty($params))) {
            throw new NotFoundHttpException();
        }

        $this->loadParams($params);
        $this->loadBodyParams($bodyParams);

        return $this;
    }

    /**
     * Load parameters from URL params
     */
    public function loadParams(array $params)
    {
        if (empty($params)) {
            return [];
        }

        foreach ($params as $value) {
            $queries = ReviewFilter::find()->where(['in', 'slug', explode(',', $value)])->with('reviewFilterGroup')->all();

            if (!$queries) {
                return [];
            }

            foreach ($queries as $query) {
                $this->setProperty($query);
            }
        }

        return $this;
    }

    /**
     * Load parameters from body params
     */
    public function loadBodyParams(array $bodyParams)
    {
        if (empty($bodyParams['ReviewSearch'])) {
            return [];
        }

        foreach ($bodyParams['ReviewSearch'] as $value) {
            $queries = ReviewFilter::find()->where(['in', 'slug', $value])->with('reviewFilterGroup')->all();
            if (!$queries) {
                continue;
            }

            foreach ($queries as $query) {
                if ($query->parent_id) {
                    $parent = $query->parent;
                    $this->setProperty($parent);
                }

                $this->setProperty($query);
            }
        }

        return $this;
    }

    /**
     * Set selected filters for display
     */
    public function setSelectedFilters($model)
    {
        $this->selectedFilters[] = [
            'id' => $model->id,
            'name' => $model->name,
            'slug' => $model->slug,
            'mapped_field' => $model->reviewFilterGroup->mapped_field,
            'parent_id' => $model->parent_id,
            'filterGroup_name' => $model->reviewFilterGroup->name,
            'value_type' => $model->reviewFilterGroup->value_type,
            'rule' => json_decode($model->reviewFilterGroup->rule, true),
            'url_position' => $model->reviewFilterGroup->url_position
        ];

        return $this;
    }

    /**
     * Set property based on filter object
     */
    public function setProperty($filterObj)
    {
        if (strtolower($filterObj->reviewFilterGroup->value_type) == 'checkbox') {
            $this->{$filterObj->reviewFilterGroup->mapped_field}[] = $filterObj->slug;
        } else {
            $this->{$filterObj->reviewFilterGroup->mapped_field} = $filterObj->slug;
        }

        $this->setSelectedFilters($filterObj);

        if ($filterObj->parent_id) {
            $this->setProperty($filterObj->parent);
        }

        return $this;
    }
}
