<?php

namespace frontend\models;

use common\helpers\FilterHelper;
use common\models\College as ModelsCollege;
use common\models\documents\College;
use common\models\Filter;
use common\models\CollegeElastic;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use yii\data\Pagination;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;
use common\models\CollegeStreamRank;
use GuzzleHttp\Client as GuzzleHttpClient;
use yii\httpclient\Client;

class CollegeSearch extends College
{
    public $sort = 'rank';

    public $city;

    public $state;

    public $course;

    public $stream;

    public $approvals;

    public $affiliated_by;

    public $ownership;

    public $course_type;

    public $fees;

    public $specialization;

    public $exam;

    public $selectedFilters = [];

    public $totalCount;

    public $availableFacets;

    public $currentPage = 1;

    public $mode;

    protected $perPage = 27;
    public $size = 13095;
    public static $propertyMapping = [
        'City' => 'city',
        'State' => 'state',
        'Courses' => 'course',
        'Streams' => 'stream',
        'Approvals' => 'approvals',
        'Affiliated By' => 'affiliated_by',
        'Specialization' => 'specialization',
        'Ownership' => 'ownership',
        'Course Type' => 'course_type',
        'Total Fees' => 'fees',
        'Exams Accepted' => 'exam',
        'Program Mode' => 'mode'
    ];

       public $url = 'http://***********:9200/all-colleges/_search?pretty';
     //  public $url = 'http://***********:9200/all-colleges/_search?pretty';

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sort', 'city', 'state', 'course', 'stream'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    public function attributeLabels()
    {
        return array_merge(parent::attributeLabels(), [
            'stream' => 'Stream',
        ]);
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params, $bodyParams = [], $exclude = [])
    {
        // dd($params);
        if (isset($bodyParams['page']) && !empty($bodyParams['page']) && $bodyParams['page'] != 'undefined') {
            $this->currentPage = (int) $bodyParams['page'];
            unset($bodyParams['page']);
        }

        if (isset($bodyParams['sortBy']) && !empty($bodyParams['sortBy']) && $bodyParams['sortBy'] != 'undefined') {
            $this->sort = $bodyParams['sortBy'];
            unset($bodyParams['sortBy']);
        }

        $this->loadRequests($params, $bodyParams);

        $collection = Yii::$app->mongodb->getCollection(College::COLLECTION_NAME);
        $match['$and'] = [];
        $aggregate = [];

        $match['$and'][] = [
            'is_sponsored' => ModelsCollege::SPONSORED_NO
        ];

        if ($this->state) {
            $match['$and'][] = [
                'state_slug' => $this->state
            ];
        }

        if ($this->city) {
            $match['$and'][] = [
                'city_slug' => ['$in' => $this->city]
            ];
        }

        if ($this->approvals) {
            $match['$and'][] = [
                'approvals' => ['$in' => $this->approvals]
            ];
        }

        if ($this->exam) {
            $match['$and'][] = [
                'exams' => ['$in' => $this->exam]
            ];
        }

        if ($this->affiliated_by) {
            $match['$and'][] = [
                'affiliated_by' => ['$in' => $this->affiliated_by]
            ];
        }

        if ($this->ownership) {
            $match['$and'][] = [
                'type' => $this->ownership
            ];
        }

        $match['$and'][] = [
            'status' => 1
        ];

        $aggregate[] = ['$match' => $match];
        $match['$and'] = [];

        // if ($this->stream || $this->course || $this->specialization || $this->course_type || $this->mode || $this->fees) {
        $aggregate[] = [
            '$unwind' => [
                'path' => '$course',
                'preserveNullAndEmptyArrays' => true
            ]
        ];
        // }

        if ($this->stream) {
            $match['$and'][] = [
                'course.stream_slug' => $this->stream
            ];
        }

        if ($this->course) {
            $match['$and'][] = [
                'course.course_slug' => $this->course
            ];
        }

        if ($this->specialization) {
            $match['$and'][] = [
                'course.specialization' => $this->specialization
            ];
        }

        if ($this->course_type) {
            $match['$and'][] = [
                'course.degree' => ['$in' => $this->course_type]
            ];
        }

        if ($this->mode) {
            $match['$and'][] = [
                'course.mode' => ['$in' => $this->mode]
            ];
        }

        if ($this->fees) {
            $match['$and'][] = [
                'course.fees_range' => ['$in' => $this->fees]
            ];
        }

        if (!empty($exclude)) {
            $match['$and'][] = [
                'college_id' => ['$not' => ['$in' => $exclude]]
            ];
        }

        if (!empty($match['$and'])) {
            $aggregate[] = ['$match' => $match];
        }

        // if ($this->stream || $this->course || $this->specialization || $this->course_type || $this->mode || $this->fees) {
        $aggregate[] = [
            '$group' => [
                '_id' => '$college_id',
                'detail' => ['$first' => '$$ROOT'],
                'avgFees' => [
                    '$avg' => '$course.avg_fees'
                ],
                'courseCount' => [
                    '$sum' => 1
                ]
            ]
        ];

        $aggregate[] = [
            '$replaceRoot' => [
                'newRoot' => [
                    '$mergeObjects' => [
                        ['avgFeess' => '$avgFees'],
                        ['courseCount' => '$courseCount'],
                        '$detail'
                    ]
                ]
            ],
        ];
        // } else {
        //     $aggregate[] = [
        //         '$group' => [
        //             '_id' => '$college_id',
        //             'detail' => ['$first' => '$$ROOT'],
        //         ]
        //     ];

        //     $aggregate[] = [
        //         '$replaceRoot' => [
        //             'newRoot' => '$detail'
        //         ],
        //     ];
        // }
        if ($this->sort) {
            if ($this->sort == 'lowest_fee' || $this->sort == 'highest_fee') {
                $aggregate[] = [
                    '$match' => [
                        'avgFees' => [
                            '$ne' => 0
                        ]
                    ]
                ];
            }

            if ($this->sort == 'lowest_fee') {
                $sort = ['avgFeess' => 1];
            } else if ($this->sort == 'highest_fee') {
                $sort = ['avgFeess' => -1];
            } else if ($this->sort == 'position') {
                $sort = [
                    'is_popular' => -1,
                    'position' => 1
                ];
            } else {
                $sort = [$this->sort => 1];
            }

            $aggregate[] = [
                '$sort' =>  $sort
            ];
        }

        $totalCount = 0;
        if (!empty($aggregate)) {
            $countQuery = $aggregate;
        }

        $countQuery[] = [
            '$group' => [
                '_id' => 'college_id',
                'count' => [
                    '$sum' => 1
                ]
            ]
        ];

        $totalCountAggregate = $collection->aggregate($countQuery);
        $totalCount = isset($totalCountAggregate[0]['count']) ? ($totalCountAggregate[0]['count']) : 0;
        if ($this->currentPage > 1) {
            $skip = ($this->currentPage - 1) * $this->perPage;
            $aggregate[] = [
                '$skip' => $skip
            ];
        }

        $aggregate[] = [
            '$limit' => $this->perPage
        ];
        $results = $collection->aggregate($aggregate);

        $this->selectedFilters = ArrayHelper::index($this->selectedFilters, 'slug');
        $pagination = new Pagination([
            'totalCount' => $totalCount,
            'defaultPageSize' => $this->perPage,
            'page' => $this->currentPage
        ]);

        return [
            'results' => $results,
            'pagination' => $pagination,
            'sort' => $this->sort
        ];
    }

    public function loadParams(array $params)
    {
        $currentUrl = Yii::$app->request->url;
        if (empty($params)) {
            return [];
        }

        foreach ($params as $key => $value) {
            $queries = Filter::find()->where(['in', 'slug', explode(',', $value)])->active()->with('filterGroup')->all();

            if (!$queries) {
                return [];
            }
            if ($queries[0]->filterGroup->name == 'Exams Accepted') {
                $isExamUrl = FilterHelper::isExamPageUrl($currentUrl);
                if ($isExamUrl == 0) {
                    Yii::$app->response->redirect('/all-colleges', 302);
                }
            }

            foreach ($queries as $query) {
                $this->setProperty($query);
            }
        }

        return $this;
    }

    public function loadBodyParams(array $bodyParams)
    {
        if (empty($bodyParams['CollegeSearch'])) {
            return [];
        }

        foreach ($bodyParams['CollegeSearch'] as $key => $value) {
            $queries = Filter::find()->where(['in', 'slug', $value])->active()->with('filterGroup')->all();
            if (!$queries) {
                continue;
            }

            foreach ($queries as $query) {
                if ($query->parent_id) {
                    $parent = $query->parent;

                    $this->setProperty($parent);
                }

                $this->setProperty($query);
            }
        }

        return $this;
    }

    public function setSelectedFilters($model)
    {
        $this->selectedFilters[] = [
            'id' => $model->id,
            'name' => $model->name,
            'slug' => $model->slug,
            'mapped_field' => $model->filterGroup->mapped_field,
            'parent_id' => $model->parent_id,
            'filterGroup_name' => $model->filterGroup->name,
            'value_type' => $model->filterGroup->value_type,
            'rule' => json_decode($model->filterGroup->rule, true),
            'url_position' => $model->filterGroup->url_position
        ];

        return $this;
    }

    public function setProperty($filterObj)
    {
        if (strtolower($filterObj->filterGroup->value_type) == 'checkbox') {
            $this->{$filterObj->filterGroup->mapped_field}[] = $filterObj->slug;
        } else {
            $this->{$filterObj->filterGroup->mapped_field} = $filterObj->slug;
        }

        $this->setSelectedFilters($filterObj);

        if ($filterObj->parent_id) {
            $this->setProperty($filterObj->parent);
        }

        return $this;
    }

    public function loadFacets()
    {
        $this->loadStateFacet();
        $this->loadCityFacet();
        $this->loadStreamFacet();
        $this->loadCourseFacet();
        $this->loadSpecializationFacet();
        $this->loadProgramModeFacet();
        $this->loadExamAcceptedFacet();
        $this->loadDegreeFacet();
        $this->loadAffiliatedBy();
        $this->loadTotalFees();
        $this->loadOwnerShip();
        $this->loadApprovalFacet();

        return $this;
    }

    public function loadStateFacet()
    {

        $stateFilter = $this->loadFilter('state_slug', $skip = 'state');
        return $this->availableFacets['state'] = $stateFilter ?? [];
    }

    public function loadCityFacet()
    {

        $cityFilter = $this->loadFilter('city_slug', $skip = 'city');
        return $this->availableFacets['city'] = $cityFilter ?? [];
    }

    public function loadCourseFacet()
    {
        $facets = $this->loadFilter('course_slug', $skip = 'course');
        return $this->availableFacets['course'] = $facets ?? [];
    }

    public function loadStreamFacet()
    {
        $facets = $this->loadFilter('stream_slug', $skip = 'stream');
        return $this->availableFacets['stream'] = $facets ?? [];
    }

    public function loadSpecializationFacet()
    {
        $facets = $this->loadFilter('specialization', $skip = 'specialization');
        return $this->availableFacets['specialization'] = $facets ?? [];
    }

    public function loadProgramModeFacet()
    {
        $facets = $this->loadFilter('mode', $skip = 'mode');
        return $this->availableFacets['mode'] = $facets ?? [];
    }

    public function loadExamAcceptedFacet()
    {
        $facets = $this->loadFilter('exams', $skip = 'exams');
        return $this->availableFacets['exam'] = $facets ?? [];
    }

    public function loadDegreeFacet()
    {
        $facets = $this->loadFilter('degree', $skip = 'course_type');
        return $this->availableFacets['courseType'] = $facets ?? [];
    }

    public function loadAffiliatedBy()
    {

        $facets = $this->loadFilter('affiliated_by', $skip = 'affiliated_by');
        return $this->availableFacets['affiliated_by'] = $facets ?? [];
    }

    public function loadApprovalFacet()
    {
        $facets = $this->loadFilter('approvals', $skip = 'approvals');
        return $this->availableFacets['approvals'] = $facets ?? [];
    }

    public function loadTotalFees()
    {

        $facets = $this->loadFilter('fees_range', $skip = 'fees_range');

        return $this->availableFacets['fees'] = $facets ?? [];
    }

    public function loadOwnerShip()
    {
        $facets = $this->loadFilter('type', $skip = 'ownership');
        return $this->availableFacets['ownership'] = $facets ?? [];
    }



    public function loadRequests($params, $bodyParams)
    {
        if ((empty($this->loadParams($params)) && !empty($params))) {
            throw new NotFoundHttpException();
        }

        $this->loadBodyParams($bodyParams);
        $this->loadFacets();

        return $this;
    }

    public function elasticCollegeListing($queryParam, $bodayParam, $sponsorCollegeIDs, $pageAction)
    {

        $filterArray['bool']['must'] = [];
        $filterData =  $this->loadRequests($queryParam, $bodayParam);
        $query =  CollegeElastic::find();
        $count = 0;
        $this->selectedFilters = ArrayHelper::index($this->selectedFilters, 'slug');
        if (isset($filterData->state) && !empty($filterData->state)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::state($filterData->state);
            $count++;
        }
        if (isset($filterData->city) && !empty($filterData->city)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::city($filterData->city[0]);
            $count++;
        }
        if (isset($filterData->course) && !empty($filterData->course)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::course($filterData->course);
            $count++;
        }
        if (isset($filterData->approvals) && !empty($filterData->approvals)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::approval($filterData->approvals);
            $count++;
        }
        if (isset($filterData->affiliated_by) && !empty($filterData->affiliated_by)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::affliatedby($filterData->affiliated_by);
            $count++;
        }
        if (isset($filterData->ownership) && !empty($filterData->ownership)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::ownership($filterData->ownership);
            $count++;
        }
        if (isset($filterData->course_type) && !empty($filterData->course_type)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::courseType($filterData->course_type);
            $count++;
        }
        if (isset($filterData->fees) && !empty($filterData->fees)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::fees($filterData->fees);
            ;
            $count++;
        }
        if (isset($filterData->exam) && !empty($filterData->exam)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::exam($filterData->exam);
            $count++;
        }
        if (isset($filterData->specialization) && !empty($filterData->specialization)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::specialization($filterData->specialization);
            $count++;
        }

        if (isset($filterData->stream) && !empty($filterData->stream)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::stream($filterData->stream);
            $count++;
        }
        if (isset($filterData->mode) && !empty($filterData->mode)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::mode($filterData->mode);
            $count++;
        }

       // $filterArray['bool']['must'][$count] = CollegeElastic::isSponsored(0);
       
        if (!empty($filterArray['bool'])) {
            $query->query($filterArray);
        }

   
        if ($pageAction == 'all-colleges' || $pageAction == 'college-filter') {
            if (isset($filterData->stream) && !empty($filterData->stream) && $pageAction == 'college-filter') {
                $checkStreamExist =  CollegeStreamRank::find()->where(['stream'=>$filterData->stream])->count();
                if ($checkStreamExist) {
                    $query->limit(27)->orderBy([$filterData->stream . '_rank' => SORT_DESC])->all();
                     $sortArray['position'] =  'SORT_DESC';
                } else {
                    $query->limit(27)->orderBy(['rank' => SORT_ASC])->all();
                }
            } else {
                $query->limit(27)->orderBy(['rank' => SORT_ASC])->all();
            }
            $page = 0;
            $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 27,
                'page' => $page
            ],
            ]);
        } else {
            $sortArray = [];

            if ($bodayParam['sortBy'] == 'highest_fee') {
                $sortArray['avgFees'] =  'SORT_DESC';
                $query->limit(27)->orderBy(['avgFees' => SORT_DESC])->all();
            } elseif ($bodayParam['sortBy'] == 'lowest_fee') {
                $query->limit(27)->orderBy(['avgFees' => SORT_ASC])->all();
            } elseif ($bodayParam['sortBy'] == 'position') {
                if (isset($filterData->stream) && !empty($filterData->stream)) {
                    $checkStreamExist =  CollegeStreamRank::find()->where(['stream'=>$filterData->stream])->count();
                    if ($checkStreamExist) {
                        $query->limit(27)->orderBy([$filterData->stream . '_rank' => SORT_DESC])->all();
                         $sortArray['position'] =  'SORT_DESC';
                    } else {
                        $query->limit(27)->orderBy(['rank' => SORT_ASC])->all();
                    }
                } else {
                    $sortArray['position'] =  'SORT_DESC';
                    $query->limit(27)->orderBy(['position' => SORT_DESC])->all();
                }
            } else {
                if (isset($filterData->stream) && !empty($filterData->stream)) {
                    $checkStreamExist =  CollegeStreamRank::find()->where(['stream'=>$filterData->stream])->count();
                    if ($checkStreamExist) {
                        $query->limit(27)->orderBy([$filterData->stream . '_rank' => SORT_DESC])->all();
                         $sortArray['position'] =  'SORT_DESC';
                    } else {
                        $query->limit(27)->orderBy(['rank' => SORT_ASC])->all();
                    }
                } else {
                    $query->limit(27)->orderBy(['rank' => SORT_ASC])->all();
                    $sortArray['rank'] =  'SORT_ASC';
                }
            }


            $page = 0;
            if (isset($bodayParam['page']) && !empty($bodayParam['page']) && $bodayParam['page'] != 'undefined') {
                $page = $bodayParam['page'];
            }
            $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 27,
                'page' => $page
            ],
            ]);
        }
        return $dataProvider;
    }

    public function loadFilter($field, $skip)
    {
        $query =  CollegeElastic::find();
        $filterArray = [];
        $count = 0;
        if (isset($this->state) && !empty($this->state)  && $skip != 'state') {
            $filterArray['bool']['must'][$count] = CollegeElastic::state($this->state);
            $count++;
        }
        if (isset($this->city) && !empty($this->city)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::city($this->city[0]);
            $count++;
        }
        if (isset($this->course) && !empty($this->course)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::course($this->course);
            $count++;
        }
        if (isset($this->approvals) && !empty($this->approvals)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::approval($this->approvals);
            $count++;
        }
        if (isset($this->affiliated_by) && !empty($this->affiliated_by)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::affliatedby($this->affiliated_by);
            $count++;
        }
        if (isset($this->ownership) && !empty($this->ownership)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::ownership($this->ownership);
            $count++;
        }
        if (isset($this->course_type) && !empty($this->course_type)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::courseType($this->course_type);
            $count++;
        }
        if (isset($this->fees) && !empty($this->fees)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::fees($this->fees);
            ;
            $count++;
        }
        if (isset($this->exam) && !empty($this->exam)) {
            $filterArray['bool']['must'][$count] = CollegeElastic::exam($this->exam);
            $count++;
        }
        if (isset($this->specialization) && !empty($this->specialization)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::specialization($this->specialization);
            $count++;
        }

        if (isset($this->stream) && !empty($this->stream)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::stream($this->stream);

            $count++;
        }
        if (isset($this->mode) && !empty($this->mode)) {
            $filterArray['bool']['must'][$count]['nested']['path'] = 'course';
            $filterArray['bool']['must'][$count]['nested']['query']['bool']['must'] = CollegeElastic::mode($this->mode);
            $count++;
        }

        
        if (!empty($filterArray['bool'])) {
            $data_elastic['query'] = $filterArray;
        }
        if (in_array($skip, ['stream', 'mode', 'specialization', 'course_type', 'course', 'fees', 'fees_range'])) {
            $data = [
                'filter_filter' =>  [
                    'nested' => ['path' => 'course'],
                    'aggs' => [
                        'filter_filter' => [
                            'terms' =>  [
                                'field' => 'course' . '.' . $field,
                                'size' => $this->size
                            ],
                            'aggs' => ['reverse' => ['reverse_nested' => new \stdClass]]
                        ]
                    ]
                ]
            ];
        } else {
            $data = [

                'filter_filter' =>  [
                    'terms' =>  [
                        'field' => $field,
                        'size' => $this->size
                    ]
                ]

            ];
        }
        $data_elastic['aggs'] = $data;
        $data_elastic['size'] = 0;

        $elatic_data = $this->elasticSearchCurl($data_elastic);


        $data_elastic['size'] = 0;
        if (in_array($skip, ['stream', 'mode', 'specialization', 'course_type', 'course', 'fees', 'fees_range'])) {
            $filterData = ArrayHelper::map($elatic_data['aggregations']['filter_filter']['filter_filter']['buckets'], 'key', 'reverse');
            if (!empty($filterData)) {
                $i = 0;
                foreach ($filterData as $key => $fillter) {
                    $filterValue[$i]['_id'] =  $key;
                    $filterValue[$i]['count'] =  $fillter['doc_count'];
                    $i++;
                }
            }
        } else {
            $filterData = ArrayHelper::map($elatic_data['aggregations']['filter_filter']['buckets'], 'key', 'doc_count');
            if (!empty($filterData)) {
                $i = 0;
                foreach ($filterData as $key => $fillter) {
                    $filterValue[$i]['_id'] =  $key;
                    $filterValue[$i]['count'] =  $fillter;
                    $i++;
                }
            }
        }

        return $filterValue ?? [];
    }


    public function elasticSearchCurl($filterArray)
    {


        $url = $this->url;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json; charset=utf-8']);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($filterArray));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $return = curl_exec($ch) or die(curl_error($ch));
        curl_close($ch);
        $array_return = json_decode($return, true);
        return $array_return ?? [];
    }
}
