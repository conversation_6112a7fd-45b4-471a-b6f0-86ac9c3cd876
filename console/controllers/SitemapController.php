<?php

namespace console\controllers;

use Carbon\Carbon;
use common\event\SitemapEvent;
use common\models\Article;
use common\models\Board;
use common\models\Category;
use common\models\College;
use common\models\CollegeCourseContent;
use common\models\CollegeProgram;
use common\models\Course;
use common\models\documents\Sitemap;
use common\models\ExamContent;
use common\models\News;
use common\models\NewsCategory;
use common\models\NewsContentSubdomain;
use common\models\NewsSubdomain;
use common\models\NewsSubdomainLiveUpdate;
use common\services\SitemapService;
use common\services\v2\NewsService;
use Exception;
use frontend\helpers\Url;
use Yii;
use yii\console\Controller;
use yii\console\widgets\Table;

class SitemapController extends Controller
{

    const DOMAIN = 'https://www.getmyuni.com';

    const PER_PAGE_RESULT = 20;

    const CHANGE_FREQ_ALWAYS = 'always';
    const CHANGE_FREQ_HOURLY = 'hourly';
    const CHANGE_FREQ_DAILY = 'daily';
    const CHANGE_FREQ_WEEKLY = 'weekly';
    const CHANGE_FREQ_MONTHLY = 'monthly';
    const CHANGE_FREQ_YEARLY = 'yearly';
    const CHANGE_FREQ_NEVER = 'never';
    const SITEMAP_HEADER = '<?xml version="1.0" encoding="utf-8"?>' . '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
    const GOOGLE_SITEMAP_HEADER = '<?xml version="1.0" encoding="utf-8"?>' . '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">';

    protected $pagination = true;
    protected $page = 1;
    protected $sitemapService;
    protected $newsService;

    public function __construct(
        $id,
        $module,
        SitemapService $sitemapService,
        NewsService $newsService,
        $config = []
    ) {

        $this->sitemapService = $sitemapService;
        $this->newsService = $newsService;
        parent::__construct($id, $module, $config);
    }

    public function actionGenerate()
    {
        Sitemap::deleteAll(['!=', 'entity', Sitemap::ENTITY_LISTING]);
        $this->actionExams();
        $this->actionArticles();
        $this->actionColleges();
        $this->actionBoards();
        $this->actionCourses();
        $this->actionArticleCategory();
    }

    public function actionGenerateNewsSitemap()
    {
        $this->posts();
        $this->categories();
        $this->generateNewsFile();
        $this->generateGoogleNewsFile();
    }

    /**
     * Generate latest News Google Sitemap
     *
     * return xml
     */
    public function actionNewsLatestPost()
    {
        $date = Carbon::now()->subDay(2)->format('Y-m-d H:i:s');
        $news = News::find()->where(['>', 'updated_at', $date]);

        foreach ($news->batch() as $posts) {
            foreach ($posts as $post) {
                if (empty($post)) {
                    continue;
                }
                $model = $this->sitemapService->getData($post->slug, Sitemap::ENTITY_NEWS);
                $bannerImage = !empty($post['banner_image']) ? Url::toDomain() . 'assets/images/news-images/' . $post['banner_image'] : '';
                if (!$model) {
                    $model = new Sitemap();
                }
                $model->entity = Sitemap::ENTITY_NEWS;
                $model->domain = self::DOMAIN;
                $model->slug = $post->slug ?? '';
                $model->subPage = '';
                $model->priority = '0.6';
                $model->changeFreq = self::CHANGE_FREQ_HOURLY;
                $model->lastModified = $post->updated_at ?? '';
                $model->createdDate = $post->created_at ?? '';
                $model->image = $bannerImage ?? '';
                $model->status = $post->status ?? Sitemap::STATUS_ACTIVE;

                if (!empty($post->newsContent) && !empty($post->newsContent->meta_title)) {
                    $model->title = $post->newsContent->meta_title;
                } else {
                    continue;
                }

                try {
                    if ($model->save()) {
                        $model->save();
                        echo "{$model->slug} \t {$model->entity} - {$model->createdDate} \n";
                    } else {
                        print_r($model->getErrors());
                    }
                } catch (Exception $e) {
                    print_r($e->getMessage());
                }
            }
        }
        $this->generateGoogleNewsFile();
    }

    public function posts()
    {
        $query = News::find()->orderBy(['id' => SORT_DESC])->limit(200);

        foreach ($query->batch() as $news) {
            foreach ($news as $post) {
                if (empty($post)) {
                    continue;
                }
                $bannerImage = !empty($post['banner_image']) ? Url::toDomain() . 'assets/images/news-images' . $post['banner_image'] : '';

                $model = Sitemap::find()
                    ->where(['slug' => $post->slug])
                    ->andWhere(['entity' => Sitemap::ENTITY_NEWS])
                    ->one();

                if (!$model) {
                    $model = new Sitemap();
                }

                $model->entity = Sitemap::ENTITY_NEWS;
                $model->domain = self::DOMAIN;
                $model->slug = $post->slug ?? '';
                $model->subPage = '';
                $model->priority = '0.6';
                $model->changeFreq = self::CHANGE_FREQ_HOURLY;
                $model->lastModified = $post->updated_at ?? '';
                $model->createdDate = $post->created_at ?? '';
                $model->image = $bannerImage ?? '';
                $model->status = $post->status ?? Sitemap::STATUS_ACTIVE;

                if (!empty($post->newsContent) && !empty($post->newsContent->meta_title)) {
                    $model->title = $post->newsContent->meta_title;
                } else {
                    continue;
                }

                try {
                    $model->save();
                    echo "{$model->createdDate} \t {$model->lastModified} \t {$model->entity} \t {$model->slug} \n";
                } catch (Exception $e) {
                    print_r($e->getMessage());
                }
            }
        }
    }

    public function categories()
    {
        $newsCatgories = NewsCategory::find()->all();

        foreach ($newsCatgories as $category) {
            if (empty($category)) {
                continue;
            }

            $data = (object)[];
            $data->slug = $category->slug;
            $data->title = $category->name;
            $data->modified = !empty($category->updated_at) ? date(DATE_ATOM, strtotime($category->updated_at)) : '';
            $data->date = !empty($category->created_at) ? date(DATE_ATOM, strtotime($category->created_at)) : '';
            $data->status = $category->status;

            $this->persistToCollection(
                $data,
                Sitemap::ENTITY_CATEGORY,
                self::DOMAIN,
                self::CHANGE_FREQ_WEEKLY
            );
        }
    }

    private function persistToCollection($data, $entity, $domain, $frequency, $priority = '0.6')
    {
        $model = Sitemap::find()
            ->where(['slug' => $data->slug])
            ->andWhere(['entity' => $entity])
            ->one();

        if (!$model) {
            $model = new Sitemap();
        }

        $model->entity = $entity;
        $model->domain = $domain;
        $model->slug = $data->slug;
        $model->subPage = $data->page ?? '';
        $model->priority = $priority;
        $model->changeFreq = $frequency;
        $model->lastModified = $data->modified ?? '';
        $model->createdDate = $data->date ?? '';
        $model->status = $data->status ?? Sitemap::STATUS_ACTIVE;
        if (!empty($data->title)) {
            $model->title = $data->title;
        } else {
            return '';
        }


        try {
            $model->save();
            echo "{$model->slug} \t {$model->entity} \n";
        } catch (Exception $e) {
            print_r($e->getMessage());
        }
    }

    public function generateExamsFile()
    {
        $postData = Sitemap::find()
            ->where(['entity' => Sitemap::ENTITY_EXAM])
            ->andWhere(['status' => Sitemap::STATUS_ACTIVE])
            ->all();

        $postSitemap = $this->getXmlString($postData, Sitemap::ENTITY_EXAM);

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/exams-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }

    public function generateArticlesFile()
    {
        $postData = Sitemap::find()
            ->where(['entity' => Sitemap::ENTITY_ARTICLE])
            ->orWhere(['entity' => Sitemap::ENTITY_ARTICLE_CATEGORY])
            ->andWhere(['status' => Sitemap::STATUS_ACTIVE])
            ->all();

        $postSitemap = $this->getXmlString($postData, Sitemap::ENTITY_ARTICLE);

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/articles-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }

    public function generateNewsFile()
    {
        $postData = Sitemap::find()
            ->where(['entity' => Sitemap::ENTITY_NEWS])
            ->orWhere(['entity' => Sitemap::ENTITY_CATEGORY])
            ->andWhere(['status' => Sitemap::STATUS_ACTIVE])
            ->all();

        $postSitemap = $this->getXmlString($postData, Sitemap::ENTITY_NEWS);

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/news-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }

    public function generateGoogleNewsFile()
    {
        $date = Carbon::now()->subDay(2)->toDateTimeLocalString();
        $collection = Yii::$app->mongodb->getCollection('sitemap');
        $postData = $collection->aggregate([
            [
                '$match' =>
                [
                    'entity' => Sitemap::ENTITY_NEWS,
                    'lastModified' =>
                    [
                        '$gt' => $date
                    ],
                    'status' => Sitemap::STATUS_ACTIVE
                ]
            ],
            [
                '$sort' => ['createdDate' => -1]
            ]
        ]);

        $postSitemap = $this->getGoogleXmlString($postData, Sitemap::ENTITY_NEWS);

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/google-news-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }

    public function getXmlString($response, $entity)
    {
        $postSitemap = self::SITEMAP_HEADER;

        foreach ($response as $post) {
            $postSitemap .= '<url>';
            $postSitemap .= '<loc>' . self::DOMAIN . '/' . $entity . '/' . $post->slug . '</loc>';
            if (isset($post->lastModified)) {
                $postSitemap .= '<lastmod>' . date(DATE_ATOM, strtotime($post->lastModified)) . '</lastmod>';
            }
            $postSitemap .= '<changefreq> ' . $post->changeFreq . '</changefreq>';
            $postSitemap .= '<priority>' . $post->priority . '</priority>';
            $postSitemap .= '</url>';
        }

        $postSitemap .= '</urlset>';

        return $postSitemap;
    }

    public function getGoogleXmlString($response, $entity)
    {
        $postSitemap = self::GOOGLE_SITEMAP_HEADER;

        foreach ($response as $post) {
            if (empty($post['title']) || empty($post['image']) || empty($post['createdDate'])) {
                continue;
            }
            $postSitemap .= '<url>';
            $postSitemap .= '<loc>' . self::DOMAIN . '/' . $entity . '/' . $post['slug'] . '</loc>';
            $postSitemap .= '<news:news>';
            $postSitemap .= '<news:publication>';
            $postSitemap .= '<news:name>Getmyuni</news:name>';
            $postSitemap .= '<news:language>en</news:language>';
            $postSitemap .= '</news:publication>';

            if (isset($post['createdDate'])) {
                $postSitemap .= '<news:publication_date>' . date(DATE_ATOM, strtotime($post['createdDate'])) . '</news:publication_date>';
            } else {
                $postSitemap .= '<news:publication_date>' . date(DATE_ATOM, strtotime($post['lastModified'])) . '</news:publication_date>';
            }

            //if (isset($post['title'])) {
            $postSitemap .= '<news:title>' . htmlspecialchars($post['title']) . '</news:title>';
            $postSitemap .= '<news:keywords' . htmlspecialchars($post['keywords']) . '</news:keywords>';
            //}
            $postSitemap .= '</news:news>';
            $postSitemap .= '<lastmod>' . date(DATE_ATOM, strtotime($post['lastModified'])) . '</lastmod>';
            if (isset($post['image'])) {
                $postSitemap .= '<image:image>';
                $postSitemap .= '<image:loc>' . $post['image'] . '</image:loc>';
                $postSitemap .= '</image:image>';
            }
            $postSitemap .= '</url>';
        }

        $postSitemap .= '</urlset>';

        return $postSitemap;
    }

    public function actionExams()
    {
        $query = ExamContent::find()->select(['exam_id', 'slug', 'updated_at', 'status'])->with('exam');

        foreach ($query->batch() as $examPages) {
            foreach ($examPages as $examPage) {
                (new SitemapEvent())->UpdateExamSitemap($examPage->exam_id);
            }
        }
    }

    public function actionArticles()
    {
        $query = Article::find()->select(['slug', 'category_id', 'updated_at', 'status']);

        foreach ($query->batch() as $articles) {
            foreach ($articles as $article) {
                if ($article->category_id == 27) {
                    continue;
                }
                (new SitemapEvent())->updateArticleSitemap($article->slug);
            }
        }
    }

    public function actionArticleCategory()
    {
        $query = Category::find();

        foreach ($query->batch() as $categories) {
            foreach ($categories as $category) {
                if ($category->id == 27) {
                    continue;
                }

                $data = (object)[];
                $data->slug = $category->slug;
                $data->modified = date(DATE_ATOM, strtotime($category->updated_at));
                $data->status = $category->status;

                $this->persistToCollection($data, Sitemap::ENTITY_ARTICLE, self::DOMAIN, self::CHANGE_FREQ_WEEKLY);
            }
        }
    }

    public function actionColleges()
    {
        $query = College::find();

        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                (new SitemapEvent())->UpdateCollegeSitemap($college->id);
            }
        }
    }

    //pi sitemap
    public function actionUpdateCollegePrograms()
    {
        $query = CollegeProgram::find()
            ->where(['page_index' => 1])
            ->andWhere(['status' => CollegeProgram::STATUS_ACTIVE]);

        foreach ($query->batch() as $programs) {
            foreach ($programs as $program) {
                (new SitemapEvent())->updateCollegeProgramSitemap($program, $program->college->slug, $program->college->status);
            }
        }
    }

    //ci sitemap
    public function actionUpdateCollegeCourses()
    {
        $query = CollegeCourseContent::find()
            ->where(['page_index' => 1])
            ->andWhere(['status' => CollegeCourseContent::STATUS_ACTIVE]);

        foreach ($query->batch() as $collegeCourses) {
            foreach ($collegeCourses as $collegeCourse) {
                (new SitemapEvent())->updateCollegeCourseSitemapMysl($collegeCourse, $collegeCourse->college->slug, $collegeCourse->college->status);
                (new SitemapEvent())->updateCollegeCourseSitemap($collegeCourse, $collegeCourse->college->slug, $collegeCourse->college->status);
            }
        }
    }

    public function actionBoards()
    {
        $query = Board::find();

        foreach ($query->batch() as $boards) {
            foreach ($boards as $board) {
                (new SitemapEvent())->updateBoardSitemap($board->id);
            }
        }
    }

    public function actionCourses()
    {
        $query = Course::find();

        foreach ($query->batch() as $courses) {
            foreach ($courses as $course) {
                (new SitemapEvent())->UpdateCourseSitemap($course->id);
            }
        }
    }

    public function actionUpdateListingStatus()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'listing_slug.csv';

        $row = 0;
        $emptyModel = 0;
        $validationFailed = 0;

        if (!file_exists($file)) {
            echo "CSV File dosen't exists!" . PHP_EOL;
            echo 'Path: ' . $file . PHP_EOL;
            exit;
        }

        echo 'Import Started.' . PHP_EOL;

        $failedRecords = [];

        if (($handle = fopen($file, 'r')) !== false) {
            while (($data = fgetcsv($handle, 2000, ',')) !== false) {
                $row++;

                if ($row == 1) {
                    continue;
                }

                try {
                    $model = Sitemap::find()->where(['slug' => $data[0]])->one();
                    if ($model) {
                        $model->status = Sitemap::STATUS_INACTIVE;
                        if ($model->save()) {
                            // echo "{$model->slug} \t {$model->entity} \t";
                        } else {
                            $validationFailed += 1;
                            $data = array_filter($model->attributes, function ($value) {
                                return !is_null($value) && $value !== '';
                            });
                            $data['errors'] = $this->getValidationErrorsAsList($model->errors);
                            $failedRecords[] = $data;
                        }
                    } else {
                        $emptyModel += 1;
                    }

                    echo 'Processing Row ' . $row . ' from CSV. Total empty data Found: ' . $emptyModel . "\r";
                } catch (\Exception $e) {
                    print_r($e);
                    exit;
                }
            }

            $this->consoleTable($failedRecords);
            $row--;
            $data = [['Total Records' => $row, 'Inserted Records' => $row - $validationFailed - $emptyModel, 'Invalid Records' => $validationFailed, 'Duplicate Records' => $emptyModel]];
            $this->consoleTable($data);
            echo 'All done!' . PHP_EOL;

            fclose($handle);
        }
    }

    private function consoleTable($data)
    {

        if (count($data) < 1) {
            return;
        }

        $header = array_keys($data[0]);
        $body = [];
        foreach ($data as $lines) {
            $body[] = array_values($lines);
        }

        $table = new Table();
        echo $table->setHeaders($header)->setRows($body)->run();
        return;
    }

    public function actionNewsSubdomain()
    {
        $query = NewsSubdomain::find()->select(['id', 'slug', 'updated_at', 'published_at', 'status'])->where(['status' => News::STATUS_ACTIVE]);

        foreach ($query->batch() as $news) {
            foreach ($news as $news) {
                (new SitemapEvent())->updateNewsUpdateXml($news->id, $news->status, $news->updated_at, $news->published_at);
            }
        }
    }

    public function actionNewsSubdomainContent()
    {
        $query = NewsContentSubdomain::find()->select(['news_id', 'updated_at', 'status'])->where(['status' => News::STATUS_ACTIVE]);

        foreach ($query->batch() as $news) {
            foreach ($news as $news) {
                (new SitemapEvent())->updateNewsUpdateXml($news->news_id, $news->status, $news->updated_at, $news->updated_at);
            }
        }
    }

    public function actionNewsSubdomainLiveUpdate()
    {
        $query = NewsSubdomainLiveUpdate::find()->select(['news_id', 'updated_at', 'status'])->where(['status' => News::STATUS_ACTIVE]);

        foreach ($query->batch() as $news) {
            foreach ($news as $news) {
                (new SitemapEvent())->updateNewsUpdateXml($news->news_id, $news->status, $news->updated_at);
            }
        }
    }
}
