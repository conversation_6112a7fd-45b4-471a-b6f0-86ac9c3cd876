<?php

namespace console\controllers;

use common\helpers\DataHelper;
use common\helpers\LeadHelper;
use common\models\ChannelActivity;
use common\models\City;
use common\models\College;
use common\models\Lead;
use common\models\old\GmuCityStateNew;
use common\models\old\GmuCustomLandingpageLeads;
use common\models\old\GmuLead;
use common\models\Student;
use common\models\StudentActivity;
use common\models\StudentCollegeShortlist;
use common\models\StudentPreference;
use common\models\StudentPreferencePreferredLocation;
use common\models\StudentPreferenceSpecialization;
use common\models\Template;
use common\services\ChannelService;
use common\services\LeadService;
use yii\console\Controller;
use yii\db\Query;

class LeadController extends controller
{
    public function actionYiiToCi()
    {
        $query = Lead::find()->where(['status' => 0]);
        foreach ($query->batch() as $i => $leads) {
            foreach ($leads as $lead) {
                // send paid leads to gmu_custom_landingpage_leads table
                if ($lead->cta_location == 'campaign_auto_pop_up_freezed') {
                    $this->yiiToClp($lead);
                    continue;
                }

                $gmuLead = new GmuLead();
                $gmuLead->full_name = $lead->name;
                $gmuLead->email_id = $lead->email;
                $gmuLead->mobile_num = $lead->mobile;
                $gmuLead->url = $lead->url;
                $gmuLead->click_source = $lead->cta_location;
                $gmuLead->is_mobile_verified = $lead->is_mobile_verified;
                $gmuLead->created_on = $lead->created_at;
                $gmuLead->utm_source = $lead->utm_source;

                // @todo: move this in model
                $gmuLead = $this->setCourse($gmuLead, $lead->interested_course);
                $gmuLead = $this->setQualification($gmuLead, $lead->qualification);
                // $gmuLead = $this->setIntrestedLocation($gmuLead, $lead->interested_location);
                $gmuLead = $this->setCurrentLocation($gmuLead, $lead->current_location);
                $interested_location = empty($lead->interested_location) ? $lead->current_location : $lead->interested_location;
                $gmuLead = $this->setInterstedLocation($gmuLead, $interested_location);

                if ($lead->entity == Lead::ENTITY_EXAM) {
                    $gmuLead->exam_id = $lead->entity_id;
                } else if ($lead->entity == Lead::ENTITY_COLLEGE) {
                    $college = $this->getCollegeById($lead->entity_id);
                    if ($college) {
                        $gmuLead->gmu_clg_id = $college->old_id;
                    }
                }

                if ($gmuLead->save()) {
                    $lead->status = 1;
                    $lead->save(false);

                    // update channel for sms
                    //$this->updateSMSChannelActivity($gmuLead);
                    //$this->updateEmailChannelActivity($lead);

                    echo "Lead pushed, \t $lead->id \n";
                } else {
                    print_r($gmuLead->getErrors());
                }
            }
        }
    }

    public function updateSMSChannelActivity($lead)
    {
        $channelService = new ChannelService();

        // push the lead to channel activity
        $template = $channelService->getTemplateByName('transSms', Template::TYPE_TRANSACTIONAL);

        if ($template) {
            $channelService->insert(
                ChannelActivity::ENTITY_GMU_LEAD,
                $lead->id,
                ChannelActivity::ACTIVITY_TYPE_SMS,
                $template->id
            );

            return true;
        }

        return false;
    }

    public function updateEmailChannelActivity($lead)
    {
        $channelService = new ChannelService();
        // push the lead to channel activity
        $template = $channelService->getTemplateByName('transMail', Template::TYPE_TRANSACTIONAL);

        if ($template) {
            $channelService->insert(
                ChannelActivity::ENTITY_LEAD,
                $lead->id,
                ChannelActivity::ACTIVITY_TYPE_EMAIL,
                $template->id
            );

            return true;
        }

        return false;
    }

    /**
     * To map old data remove this after some day
     *
     * @return void
     */
    private function setCourse(GmuLead $lead, $course): GmuLead
    {
        $query = new Query();
        $query->select(['course.name', 'stream.lms_stream'])
            ->from('course course')
            ->leftJoin('stream as stream', 'stream.id = course.stream_id')
            ->where(['course.id' => $course]);

        $courses = $query->one();

        $lead->interested_course = $courses['name'];
        $lead->gmu_course = $courses['lms_stream'];
        return $lead;
    }

    private function setQualification(GmuLead $lead, $qualification): GmuLead
    {
        $qualifications = DataHelper::highestQualification();
        foreach ($qualifications as $qua) {
            if ($qua['name'] == $qualification || $qua['value'] == $qualification) {
                $lead->qualification = $qua['name'];
                $lead->gmu_qualification = $qua['value'];
                break;
            }
        }

        return $lead;
    }

    private function setCurrentLocation(GmuLead $lead, $location): GmuLead
    {
        if (!empty($location) && is_numeric($location)) {
            $newCitySlug = City::find()->select(['slug'])->where(['id' => $location])->one();
            if ($newCitySlug) {
                $location = (isset($newCitySlug['slug'])) ? $newCitySlug['slug'] : null;
            }
        }

        // @todo: Move this query into query class and cache this data
        $city = GmuCityStateNew::find()->where('city_slug =:citySlug', [':citySlug' => $location])->one();

        if ($city) {
            $lead->current_location = $city->city;
            $lead->current_location_state_id = $city->state_id;
        }

        return $lead;
    }

    private function setInterstedLocation(GmuLead $lead, $location): GmuLead
    {
        if (!empty($location) && is_numeric($location)) {
            $newCitySlug = City::find()->select(['slug'])->where(['id' => $location])->one();
            if ($newCitySlug) {
                $location = (isset($newCitySlug['slug'])) ? $newCitySlug['slug'] : null;
            }
        }

        // @todo: Move this query into query class and cache this data
        $city = GmuCityStateNew::find()->where('city_slug =:citySlug', [':citySlug' => $location])->one();

        if ($city) {
            $lead->location = $city->city;
            $lead->location_state_id = $city->state_id;
        }

        return $lead;
    }

    public function getCollegeById(int $id)
    {
        return College::findOne($id);
    }

    public function yiiToClp(Lead $lead)
    {
        $clpLead = new GmuCustomLandingpageLeads();
        $clpLead->url = $lead->url;
        $clpLead->name = $lead->name;
        $clpLead->email = $lead->email;
        $clpLead->phone = $lead->mobile;
        $clpLead->utm_campaign = $lead->utm_campaign;
        $clpLead->utm_medium = $lead->utm_medium;
        $clpLead->utm_source = $lead->utm_source;

        $clpLead = LeadHelper::setCourse($clpLead, $lead->interested_course);
        $clpLead = LeadHelper::setQualification($clpLead, $lead->qualification);
        $clpLead = LeadHelper::setInterestedLocation($clpLead, $lead->interested_location);
        $clpLead->created_on = $lead->created_at;

        if ($lead->entity == Lead::ENTITY_COLLEGE) {
            $college = $this->getCollegeById($lead->entity_id);
            if ($college) {
                $clpLead->college_id = $college->old_id;
            }
        }

        if ($clpLead->save()) {
            $lead->status = 3;
            $lead->save(false);
        } else {
            print_r($clpLead->getErrors());
        }
    }

    public function actionSendLeadSso()
    {
        $dateTime = new \DateTime();
        $dateTime->modify('-2 minutes');
        // $formattedTime = '2024-08-12 22:26:00';
        $formattedTime = $dateTime->format('Y-m-d H:i:s');

        $studentActivities = StudentActivity::find()
            ->where(['>=', 'created_at', $formattedTime])
            ->andWhere(['<>', 'cta_position', 'log-activity'])
            ->all();
        // dd($studentActivities);
        foreach ($studentActivities as $studentActivity) {
            $studentDetails = (new Query())
                ->select(['s.*'])
                ->from(['student s'])
                ->innerJoin('student_activity sa', 's.id = sa.student_id')
                ->where(['sa.id' => $studentActivity['id']])
                ->andWhere(['>=', 'sa.created_at', $formattedTime])
                ->one();
            $studentPreference = StudentPreference::find()
                ->where(['activity_id' => $studentActivity['id']])
                ->andWhere(['>=', 'created_at', $formattedTime])
                ->one();
            $shortlistCollegeIds = StudentCollegeShortlist::find()
                ->select(['college_id'])
                ->where(['student_id' => $studentDetails['id']])
                ->andWhere(['>=', 'created_at', $formattedTime])
                ->all();

            $collegeIds = [];

            // if (empty($shortlistCollegeIds)) {
            //     $shortlistedCollegeId['college_ids'] = $collegeOldId;
            // }

            // foreach ($shortlistCollegeIds as $collegeId) {
            //     $collegeModel = College::find()->select(['old_id'])->where(['id' => $collegeId])->one();
            //     $collegeOldId[] = $collegeModel->old_id;
            // }

            foreach ($shortlistCollegeIds as $collegeId) {
                $collegeIds[] = $collegeId->college_id;
            }
            
            if ($studentPreference) {
                $studentSpecialization = StudentPreferenceSpecialization::find()
                    ->select(['specialization_id'])
                    ->where(['student_preference_id' => $studentPreference['id']])
                    ->andWhere(['>=', 'created_at', $formattedTime])
                    ->all();
                $studentPreferredCity = StudentPreferencePreferredLocation::find()
                    ->select(['preferred_city_id'])
                    ->where(['student_preference_id' => $studentPreference['id']])
                    ->andWhere(['>=', 'created_at', $formattedTime])
                    ->all();
            }
                
            $shortlistedCollegeId['college_ids'] = !empty($collegeIds) ? array_values(array_unique(array_filter($collegeIds))) : [];
            $shortlistedCollegeId['inputSpecialization'] = !empty($studentSpecialization) ? array_column($studentSpecialization, 'specialization_id') : [];
            $shortlistedCollegeId['college_location'] = !empty($studentPreferredCity) ? array_column($studentPreferredCity, 'preferred_city_id') : [];
            
            (new LeadService)->sendLeadsToCld($studentDetails ?? [], $studentPreference ?? [], $studentActivity ?? [], $shortlistedCollegeId ?? []);
        }
    }
}
