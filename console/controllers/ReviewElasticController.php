<?php

namespace console\controllers;

use common\models\Review;
use common\models\ReviewElastic;
use common\models\ReviewContent;
use yii\console\Controller;
use yii\db\Query;
use Yii;

/**
 * Console controller for indexing reviews into Elasticsearch
 */
class ReviewElasticController extends Controller
{
    /**
     * Index all reviews into Elasticsearch
     */
    public function actionIndexAll()
    {
        echo "Starting review indexing...\n";
        
        // Create/update index mapping
        ReviewElastic::updateMapping();
        echo "Index mapping updated.\n";
        
        // Get all approved reviews with related data
        $query = new Query();
        $query->select([
            'r.id as review_id',
            'r.student_id',
            'r.college_id', 
            'r.course_id',
            'r.admission_year',
            'r.status',
            'r.created_at as review_created_at',
            'r.updated_at as review_updated_at',
            'r.slug as review_slug',
            'r.title as review_title',
            's.name as student_name',
            's.profile_pic as student_profile_pic',
            'c.name as college_name',
            'c.slug as college_slug',
            'c.display_name as college_display_name',
            'c.city_id as college_city_id',
            'city.name as college_city_name',
            'city.slug as city_slug',
            'state.id as college_state_id',
            'state.name as college_state_name',
            'state.slug as state_slug',
            'course.name as course_name',
            'course.slug as course_slug',
            'stream.id as stream_id',
            'stream.name as stream_name',
            'stream.slug as stream_slug'
        ])
        ->from('review r')
        ->leftJoin('student s', 's.id = r.student_id')
        ->leftJoin('college c', 'c.id = r.college_id')
        ->leftJoin('city', 'city.id = c.city_id')
        ->leftJoin('state', 'state.id = city.state_id')
        ->leftJoin('course', 'course.id = r.course_id')
        ->leftJoin('stream', 'stream.id = course.stream_id')
        ->where(['r.status' => Review::STATUS_APPROVED]);
        
        $totalCount = $query->count();
        echo "Found {$totalCount} reviews to index.\n";
        
        $batchSize = 100;
        $indexed = 0;
        
        foreach ($query->batch($batchSize) as $reviews) {
            foreach ($reviews as $reviewData) {
                $this->indexReview($reviewData);
                $indexed++;
                
                if ($indexed % 50 == 0) {
                    echo "Indexed {$indexed}/{$totalCount} reviews...\n";
                }
            }
        }
        
        echo "Completed indexing {$indexed} reviews.\n";
    }
    
    /**
     * Index a single review
     */
    public function actionIndexReview($reviewId)
    {
        $query = new Query();
        $reviewData = $query->select([
            'r.id as review_id',
            'r.student_id',
            'r.college_id', 
            'r.course_id',
            'r.admission_year',
            'r.status',
            'r.created_at as review_created_at',
            'r.updated_at as review_updated_at',
            'r.slug as review_slug',
            'r.title as review_title',
            's.name as student_name',
            's.profile_pic as student_profile_pic',
            'c.name as college_name',
            'c.slug as college_slug',
            'c.display_name as college_display_name',
            'c.city_id as college_city_id',
            'city.name as college_city_name',
            'city.slug as city_slug',
            'state.id as college_state_id',
            'state.name as college_state_name',
            'state.slug as state_slug',
            'course.name as course_name',
            'course.slug as course_slug',
            'stream.id as stream_id',
            'stream.name as stream_name',
            'stream.slug as stream_slug'
        ])
        ->from('review r')
        ->leftJoin('student s', 's.id = r.student_id')
        ->leftJoin('college c', 'c.id = r.college_id')
        ->leftJoin('city', 'city.id = c.city_id')
        ->leftJoin('state', 'state.id = city.state_id')
        ->leftJoin('course', 'course.id = r.course_id')
        ->leftJoin('stream', 'stream.id = course.stream_id')
        ->where(['r.id' => $reviewId])
        ->one();
        
        if ($reviewData) {
            $this->indexReview($reviewData);
            echo "Review {$reviewId} indexed successfully.\n";
        } else {
            echo "Review {$reviewId} not found.\n";
        }
    }
    
    /**
     * Delete review from Elasticsearch
     */
    public function actionDeleteReview($reviewId)
    {
        try {
            $reviewElastic = ReviewElastic::get($reviewId);
            if ($reviewElastic) {
                $reviewElastic->delete();
                echo "Review {$reviewId} deleted from Elasticsearch.\n";
            } else {
                echo "Review {$reviewId} not found in Elasticsearch.\n";
            }
        } catch (\Exception $e) {
            echo "Error deleting review {$reviewId}: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Index a single review data
     */
    private function indexReview($reviewData)
    {
        try {
            // Get review content
            $reviewContent = ReviewContent::find()
                ->where(['review_id' => $reviewData['review_id']])
                ->andWhere(['status' => ReviewContent::STATUS_APPROVED])
                ->all();
            
            $contentText = '';
            foreach ($reviewContent as $content) {
                $contentText .= strip_tags($content->content) . ' ';
            }
            
            // Get category ratings from review_content table
            $categoryRatingsQuery = new Query();
            $categoryRatings = $categoryRatingsQuery
                ->select(['review_category_id', 'rating'])
                ->from('review_content')
                ->where(['review_id' => $reviewData['review_id']])
                ->andWhere(['status' => ReviewContent::STATUS_APPROVED])
                ->all();
            
            // Calculate overall rating
            $overallRating = 0;
            if (!empty($categoryRatings)) {
                $totalRating = array_sum(array_column($categoryRatings, 'rating'));
                $overallRating = $totalRating / count($categoryRatings);
            }
            
            // Calculate batch from admission year
            $batch = $reviewData['admission_year'] ? (int)$reviewData['admission_year'] : 0;
            
            // Create or update Elasticsearch document
            try {
                $reviewElastic = ReviewElastic::get($reviewData['review_id']);
            } catch (\Exception $e) {
                $reviewElastic = null;
            }

            if (!$reviewElastic) {
                $reviewElastic = new ReviewElastic();
                $reviewElastic->_id = $reviewData['review_id'];
            }
            
            // Set attributes
            $reviewElastic->review_id = $reviewData['review_id'];
            $reviewElastic->student_id = $reviewData['student_id'];
            $reviewElastic->college_id = $reviewData['college_id'];
            $reviewElastic->course_id = $reviewData['course_id'];
            $reviewElastic->admission_year = $reviewData['admission_year'];
            $reviewElastic->batch = $batch;
            $reviewElastic->status = $reviewData['status'];
            $reviewElastic->review_created_at = $reviewData['review_created_at'];
            $reviewElastic->review_updated_at = $reviewData['review_updated_at'];
            $reviewElastic->review_overall_rating = $overallRating;
            $reviewElastic->student_name = $reviewData['student_name'];
            $reviewElastic->student_profile_pic = $reviewData['student_profile_pic'];
            $reviewElastic->college_name = $reviewData['college_name'];
            $reviewElastic->college_slug = $reviewData['college_slug'];
            $reviewElastic->college_display_name = $reviewData['college_display_name'];
            $reviewElastic->college_city_id = $reviewData['college_city_id'];
            $reviewElastic->college_city_name = $reviewData['college_city_name'];
            $reviewElastic->college_state_id = $reviewData['college_state_id'];
            $reviewElastic->college_state_name = $reviewData['college_state_name'];
            $reviewElastic->course_name = $reviewData['course_name'];
            $reviewElastic->course_slug = $reviewData['course_slug'];
            $reviewElastic->stream_id = $reviewData['stream_id'];
            $reviewElastic->stream_name = $reviewData['stream_name'];
            $reviewElastic->stream_slug = $reviewData['stream_slug'];
            $reviewElastic->city_slug = $reviewData['city_slug'];
            $reviewElastic->state_slug = $reviewData['state_slug'];
            $reviewElastic->review_slug = $reviewData['review_slug'];
            $reviewElastic->review_title = $reviewData['review_title'];
            $reviewElastic->review_content = trim($contentText);
            $reviewElastic->category_ratings = $categoryRatings;
            
            $reviewElastic->save();
            
        } catch (\Exception $e) {
            echo "Error indexing review {$reviewData['review_id']}: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Create index and mapping
     */
    public function actionCreateIndex()
    {
        ReviewElastic::createIndex();
        echo "Review index created successfully.\n";
    }
    
    /**
     * Update mapping
     */
    public function actionUpdateMapping()
    {
        ReviewElastic::updateMapping();
        echo "Review mapping updated successfully.\n";
    }
}
