<?php

namespace console\controllers;

use common\helpers\ArticleDataHelper;
use Exception;
use yii\console\widgets\Table;
use common\helpers\CollegeHelper;
use common\helpers\ConsoleHelper;
use common\helpers\ContentHelper;
use common\helpers\CourseHelper;
use common\helpers\DataHelper;
use common\models\AlternateCtaText;
use common\models\Article;
use common\models\Board;
use common\models\BoardContent;
use common\models\BoardSamplePaper;
use common\models\BoardSamplePaperFile;
use common\models\Brochure;
use common\models\Category;
use common\models\City;
use common\models\College;
use common\models\CollegeContent;
use common\models\CollegeCourse;
use common\models\BusinessUnit;
use common\models\CollegeCourseCompany;
use common\models\CollegeCourseContent;
use common\models\CollegeCourseExam;
use common\models\CollegeCourseOld;
use common\models\CollegeFees;
use common\models\CollegeProgram;
use common\models\CollegeProgramContent;
use common\models\CollegeProgramExam;
use common\models\CollegeProgramFees;
use common\models\Course;
use common\models\Exam;
use common\models\Comment;
use common\models\Company;
use common\models\Country;
use common\models\CourseContent;
use common\models\CutOff;
use common\models\CourseEligibility;
use common\models\CtaThankYou;
use common\models\CutoffCategory;
use common\models\Degree;
use common\models\documents\College as DocumentsCollege;
use common\models\documents\CollegeCourse as CollectionCollegeCourse;
use common\models\documents\CollegeProgram as DocumentsCollegeProgram;
use common\models\DsaStateMapping;
use common\models\EducationBodyType;
use common\models\ExamContent;
use common\models\ExamCourse;
use common\models\ExamDate;
use common\models\ExamType;
use common\models\Faq;
use common\models\Feature;
use common\models\FeatureGroup;
use common\models\FeatureValue;
use common\models\Gallery;
use common\models\GmuMetaCategory;
use common\models\HighlightAttribute;
use common\models\HighlightAttributeValue;
use common\models\Lead;
use common\models\LeadBucket;
use common\models\LeadBucketCta;
use common\models\LeadBucketCtaDetail;
use common\models\LeadBucketTagging;
use common\models\old\GmuExamTitle;
use yii\helpers\StringHelper;
use Yii;
use \yii\helpers\FileHelper;
use yii\db\Query;
use common\models\old\GmuAdminUser;
use common\models\old\GmuArticles;
use common\models\old\GmuArticlesComments;
use common\models\old\GmuBoardsKeywordTag;
use common\models\old\GmuCategoryArticle;
use common\models\old\GmuColleges;
use common\models\old\GmuCollegeImagesNew;
use common\models\old\GmuCoursesDetails;
use common\models\old\GmuExam;
use common\models\old\GmuFaq;
use common\models\old\GmuNews;
use common\models\Profile;
use common\models\Program;
use common\models\ProgramCourseMapping;
use common\models\SeoInfo;
use common\models\Specialization;
use common\models\SpecializationNew;
use common\models\SpecializationOld;
use common\models\State;
use common\models\Stream;
use common\models\Student;
use common\models\StudentActivity;
use common\models\StudentCollegeShortlist;
use common\models\StudentPreference;
use common\models\User;
use common\services\CollegeService;
use common\services\LeadService;
use common\services\UserService;
use common\models\Filter;
use common\models\FilterGroup;
use common\models\InstituteProgramRankMapping;
use common\models\LeadEducationBudget;
use common\models\LeadGmailDomain;
use common\models\LiveApplication;
use common\models\MediaDrive;
use common\models\MediaDriveCtaTypeMapping;
use common\models\MediaDriveUploadType;
use common\models\News;
use yii\BaseYii;
use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;
use yii\helpers\Inflector;
use common\models\old\GmuCustomLandingpageLeads;
use common\models\Redirection;
use common\models\SponsorCollege;
use common\models\TrendingPage;
use common\services\S3Service;
use DOMDocument;
use DOMXPath;
use frontend\helpers\Url;
use MatthiasMullie\Minify;
use yii\console\ExitCode;

class ImportController extends \yii\console\Controller
{

    public static $examDateFields = [
        'Application Form' => ['application_form_start', 'application_form_end'],
        'Admit Card' => ['admit_card_date'],
        'Exam Start' => ['from_date', 'to_date'],
        'Result Date' => ['result_date'],
        'Counselling Date' => ['counselling_date'],
    ];
    public static $_pageTypeArray = [
        'syllabus-subjects/1st-year' => '1st Year',
        'syllabus-subjects/2nd-year' => '2nd Year',
        'syllabus-subjects/3rd-year' => '3rd Year',
        'syllabus-subjects/4th-year' => '4th Year',
        'admission/kerala' => 'Kerala',
        'admission/gujarat' => 'Gujarat',
        'admission/maharashtra' => 'Maharashtra'
    ];
    public static $examContantFields = [
        'overview' => 'overview',
        'important_dates' => 'important-dates',
        'eligibility' => 'eligibility',
        'application_process' => 'application-process',
        'application_form_details' => 'form',
        'admit_card' => 'admit-card',
        'exam_centres' => 'exam-centres',
        'exam_pattern' => 'exam-pattern',
        // 'result_cutoff',
        // 'counselling_procedure',
        'syllabus' => 'syllabus',
        'results' => 'results',
        'cut_off' => 'cut-off',
        'counselling' => 'counselling',
        'ref_books' => 'reference-books',
        'notification' => 'notification',
        'vacancy' => 'vacancy',
        'recruitment' => 'recruitment',
        'sample_test_paper_content' => 'mock-sample-tests',
        'previous_year_paper_content' => 'previous-years-papers',
        'answer_key_content' => 'answer-key'
    ];

    public function actionAll()
    {
        //
    }

    public function actionAdminUser()
    {

        $errors = [];

        $query = GmuAdminUser::find()->groupBy('username');

        foreach ($query->batch() as $users) {
            foreach ($users as $user) {
                $userSlug = Inflector::slug($user->name);
                $model = User::find()->where(['username' => $userSlug])->one();

                if (!$model) {
                    $model = new User();
                }

                $model->entity = User::ENTITY_ADMIN_USER;
                $model->entity_id = $user->id;
                $model->name = $user->name;
                $model->slug = $userSlug;
                $model->username = $userSlug;
                $model->email = !empty($user->email) ? $user->email : null;
                $model->status = User::STATUS_DELETED;
                $model->contact_number = $user->contact_number;

                if ($model->isNewRecord) {
                    $model->setPassword($user->email);
                    $model->generateAuthKey();
                    $model->generateEmailVerificationToken();
                }

                try {
                    if ($model->save()) {
                        $profile = $model->profile;

                        if (!($profile instanceof Profile)) {
                            $profile = new Profile();
                        }
                        $profile->setAttributes($user->attributes, false);
                        $profile->user_id = $model->id;

                        $profile->save();

                        echo "$model->id \t $model->entity \t $model->entity_id  \t $model->username \n";
                    } else {
                        $errors[] = $model->getErrors();
                    }
                } catch (\Exception $e) {
                    echo $e->getMessage();
                }
            }
        }
    }

    // public function actionCourse()
    // {
    //     $query = GmuCoursesDetails::find();

    //     $errors = [];
    //     foreach ($query->batch() as $courses) {
    //         foreach ($courses as $course) {
    //             $model = Course::findOne($course->id);

    //             if (!$model) {
    //                 $model = new Course();
    //             }

    //             $model->id = $course->id;

    //             if ($course->id != $course->parent_id  && $course->parent_id !== 999) {
    //                 $model->parent_id = $course->parent_id;
    //             }

    //             $model->name = $course->course;
    //             $model->slug = $course->course_vanity;
    //             $model->short_name = $course->course_short;

    //             if ($model->save()) {
    //                 echo "{$model->id} \t {$model->parent_id} \t {$model->short_name} \n";
    //             } else {
    //                 $errors[$model->id] = [$model->getErrors(), $model->attributes];
    //             }
    //         }
    //     }
    //     print_r($errors);
    // }

    public function actionExam()
    {
        $query = GmuExam::find();

        $errors = [];
        $c = [];
        $s = [];
        foreach ($query->batch() as $exams) {
            foreach ($exams as $exam) {
                $model = Exam::find()->where(['id' => $exam->id])->one();

                if ($exam->is_active == 0) {
                    continue;
                }

                if (!$model) {
                    $model = new Exam();
                }

                $model->id = $exam->id;
                $model->name = $exam->title;
                $model->slug = $exam->exam_seo_url;
                $model->display_name = $exam->display_title;
                $model->cover_image = $exam->exam_image_url;
                $model->status = $exam->is_active;
                $model->created_at = $exam->posted_on;
                $model->updated_at = $exam->updated_on;

                if ($model->save()) {
                    // Dates
                    foreach (self::$examDateFields as $name => $dateField) {
                        if (empty($exam->{$dateField[0]})) {
                            continue;
                        }

                        $slug = Inflector::slug($name);
                        $flagField = $dateField[0] . '_flag';
                        $examDate = ExamDate::find()->where(['slug' => $slug, 'exam_id' => $model->id])->one();

                        if (!$examDate) {
                            $examDate = new ExamDate();
                        }

                        $examDate->exam_id = $model->id;
                        $examDate->name = $name;
                        $examDate->slug = $slug;
                        $examDate->start = trim($exam->{$dateField[0]});
                        $examDate->status = 1;
                        $examDate->end = isset($dateField[1]) ? trim($exam->{$dateField[1]}) : null;
                        $examDate->type = $exam->{$flagField};
                        try {
                            $examDate->save();
                            echo "\t\t $examDate->name \t $examDate->start $examDate->end \n";
                        } catch (\Throwable $th) {
                            print_r($examDate->getErrors());
                        }
                    }

                    // Contents
                    foreach (self::$examContantFields as $column => $contentSlug) {
                        // $examContentSlugs = DataHelper::examContentList();
                        // $name = $examContentSlugs[$contentSlug];
                        // $slug = $contentSlug;

                        $examContent = ExamContent::find()->where(['slug' => $slug, 'exam_id' => $model->id])->one();
                        $contentCount = str_word_count(trim(ContentHelper::htmlDecode($exam->{$column}, true)));

                        if ($exam->{$column} == null || $contentCount < 5) {
                            continue;
                        }
                        if (!$examContent) {
                            $examContent = new ExamContent();
                        }

                        $examContent->exam_id = $model->id;
                        $examContent->slug = $contentSlug;
                        $examContent->content = $exam->{$column};
                        $examContent->status = ExamContent::STATUS_ACTIVE;

                        if ($examContent->save()) {
                            echo "\t\t $examContent->name \t $examContent->slug \n";
                        } else {
                            print_r($examContent->getErrors());
                        }
                    }
                    echo "{$model->id} \t {$model->name} \n";
                } else {
                    $errors[] = [
                        'model_error' => $model->getErrors(),
                        'exam' => $exam->id
                    ];
                }
            }
        }
        print_r($errors);
        print_r($c);
        print_r($s);
    }

    public function actionSeoInfo()
    {
        $query = GmuExamTitle::find();

        $errors = [];
        foreach ($query->batch() as $examsTitles) {
            foreach ($examsTitles as $title) {
                $titleCategory = DataHelper::gmuExamTitle()[$title->category];

                $model = SeoInfo::find()->where(['entity' => SeoInfo::ENTITY_EXAM, 'entity_id' => $title->exam_id, 'page' => $titleCategory])->one();

                if (!$model) {
                    $model = new SeoInfo();
                }

                $model->entity = SeoInfo::ENTITY_EXAM;
                $model->entity_id = $title->exam_id;
                $model->page = StringHelper::truncate($titleCategory, 240);
                $model->h1 = StringHelper::truncate($title->h1, 240);
                $model->title = StringHelper::truncate($title->title, 240);
                $model->description = $title->description;

                if ($model->save()) {
                    echo "{$model->entity} \t {$model->entity_id} \t {$model->page} \n";
                } else {
                    $errors[] = $model->getErrors();
                }
            }
        }
        print_r($errors);
    }

    /**
     * Insert streams from csv
     *
     * @return void
     */
    public function actionStream()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'streams.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $stream = Stream::find()->where(['slug' => $fileop['1']])->one();

            if (!$stream) {
                $stream = new Stream();
            }

            $stream->name = Inflector::camel2words(Inflector::id2camel($fileop[1]));
            $stream->display_name = $stream->name;
            $stream->slug = $fileop[1];
            $stream->status = Stream::STATUS_ACTIVE;
            if ($stream->save()) {
                echo "$stream->name \n";
            }
        }

        fclose($handle);
    }

    /**
     * Insert courses from csv
     *
     * @return void
     */
    public function actionCourse()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'courses.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i = 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $course = Course::find()->where(['slug' => $fileop['3']])->one();
            $stream = Stream::find()->where(['slug' => $fileop['4']])->one();

            if (empty($stream)) {
                BaseYii::info($fileop['4'] . ' Stream not match');
                echo "{$fileop['4']} couldn't find \n";
                continue;
            }

            if (!$course) {
                $course = new Course();
            }

            if (!($fileop[5] == 0 || $fileop[5] == 999)) {
                $course->parent_id = (int) $fileop['5'];
            }

            $course->stream_id = $stream->id;
            $course->name = $fileop['1'];
            $course->slug = $fileop['3'];
            $course->short_name = $fileop['2'];
            $course->parent_id = (int) $fileop['5'];
            $course->status = 1;

            if ($course->save()) {
                echo "$course->name \n";
            } else {
                print_r($fileop);
                var_dump($course->getErrors());
            }
        }

        fclose($handle);
    }

    // public function actionCourseStreamMapping()
    // {
    //     $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_stream.csv';

    //     if (($handle = fopen($file, "r")) == false) {
    //         echo "Unable to open the file \n";
    //         die(__FILE__);
    //     }

    //     $i = 0;
    //     while (($fileop = fgetcsv($handle, 1000, ",")) !== false) {
    //         if ($i = 0 || empty($fileop[1])) {
    //             $i++;
    //             continue;
    //         }
    //         $stream = Stream::find()->where(['slug' => $fileop['0']])->one();
    //         if (!$stream) {
    //             continue;
    //         }

    //         $course = Course::find()->where(['slug' => $fileop['1']])->one();
    //         if (!$course) {
    //             continue;
    //         }
    //         $course->link('stream', $stream);
    //     }
    //     fclose($handle);
    // }

    /**
     * @todo: to be checked
     *
     * @return object
     * /
    public function actionUpdateUserArticle()
    {
        $query = GmuCategoryArticle::find();

        $errors = [];
        foreach ($query->batch() as $categoryArticles) {
            foreach ($categoryArticles as $article) {
                if ($article->author_id != null) {
                    continue;
                }

                $author = GmuMasterLogs::find()->with('user')->where(['record_id' => $article->id])->andWhere(['table_name' => 'gmu_category_articles'])->one();

                if (empty($author->user)) {
                    continue;
                }

                $article->author_id = $author->user->id;

                if ($article->save()) {
                    echo "{$article->author_id} \t {$article->h1} \n";
                } else {
                    $errors[] = $article->getErrors();
                }
            }
        }
    }

    /**
     * Undocumented function
     *
     * @param [type] $examId
     * @param [type] $page
     * @return void
     */
    private function getContentAuthorId($examId, $page)
    {
        $query = new Query;
        $query->select([])
            ->from('gmu_exam_log')
            ->orderBy(['created_on' => SORT_DESC])
            // ->innerJoin('user as u', 'u.entity_id = gel.operator_id')
            ->where(['target_id' => $examId])
            ->andWhere(['category' => trim($page)]);

        if ($author = $query->one(\Yii::$app->gmudb)) {
            return $author['operator_id'];
        }

        return null;
    }

    /**
     * One time will remove the code later
     *
     * @return void
     */
    public function actionUpdateAuthor()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'author.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $user = User::find()->where(['entity' => User::ENTITY_ADMIN_USER, 'entity_id' => $fileop[0]])->one();

            if (!$user) {
                $notMapped['userNotFound'][] = $fileop;
                continue;
            }

            $profile = $user->profile;

            if (!($profile instanceof Profile)) {
                $profile = new Profile();
                $profile->user_id = $user->id;
            }

            $profile->about = $fileop[5];
            $profile->job_role = $fileop[8];
            $profile->linkedin_url = $fileop[9];
            $profile->twitter_url = $fileop[10];
            if ($profile->save()) {
                echo "$user->id \t $user->entity \t $user->entity_id  \t $profile->job_role \n";
            } else {
                $notMapped['unableToSaveProfile'] = ['data' => $fileop, 'errors' => $profile->errors];
            }
        }

        print_r($notMapped);

        fclose($handle);
    }

    /**
     * One time will remove the code later
     *
     * @return void
     */
    public function actionUpdateExamAuthor()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam-author.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $examTitles = DataHelper::gmuExamTitle();

        $notMapped = [];

        $i = 0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }


            if (!isset($examTitles[$fileop[2]])) {
                $notMapped['missing-exam-slug'][$fileop[2]] = $fileop;
                continue;
            }

            $examContent = ExamContent::findOne(['exam_id' => $fileop[0], 'slug' => $examTitles[$fileop[2]]]);
            if (!$examContent) {
                $notMapped['missing-exam-content'][] = $fileop;
                continue;
            }


            $examContent->author_id = $fileop[3];

            if ($examContent->save()) {
                echo "{$fileop[1]} \n";
            } else {
                $notMapped['persistent-error'][] = [
                    'error' => $examContent->getErrors(),
                    'fileop' => $fileop
                ];
            }
        }
        print_r($notMapped);
        echo count($notMapped);
        fclose($handle);
    }

    /**
     * One time will remove the code later
     *
     * @return void
     */
    public function updateExamFilters()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam-filters.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
        }

        fclose($handle);
    }

    public function actionExamStreamMapping()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam_stream_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $exam = Exam::find()->where(['slug' => $fileop[2]])->one();
            if (!$exam) {
                continue;
            }
            $stream = Stream::find()->where(['slug' => $fileop[4]])->one();

            if (!$stream) {
                $notMapped[] = "$fileop[2] \t $fileop[4]";
                continue;
            }

            $exam->link('streams', $stream);

            echo "\t $exam->display_name \t $stream->name \n";
        }
        print_r($notMapped);
    }

    public function actionExamCourseMapping()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam_course_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $exam = Exam::find()->where(['slug' => $fileop[2]])->one();
            if (!$exam) {
                continue;
            }
            $stream = Stream::find()->where(['slug' => $fileop[4]])->with('courses')->one();
            if (!$stream) {
                $notMapped[] = "$fileop[2] \t $fileop[4] \t Stream not found \n";
                continue;
            }
            $courses = array_column($stream->courses, 'slug');
            $fileOpCourses = explode(',', $fileop[5]);

            foreach ($fileOpCourses as $fileOpCourse) {
                if (!in_array($fileOpCourse, $courses)) {
                    $notMapped[] = "$fileop[2] \t $fileop[4] \t $fileOpCourse \t course not match with stream \n";
                    continue;
                }

                $courseModel = Course::find()->where(['slug' => $fileOpCourse])->one();

                if (!$courseModel) {
                    $notMapped[] = "$fileop[2] \t $fileop[4] \t $fileOpCourse \t course not found \n";
                    continue;
                }

                $exam->link('courses', $courseModel);

                echo "$fileop[2] \t $fileop[5] \n";
            }
        }
        print_r($notMapped);
    }

    public function actionUpdateExamDate()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'gmu_exam_dates.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $exam = Exam::find()->byId($fileop[0])->one();

            if (!$exam) {
                $notMapped['exam'][] = "Exam not found with id {$fileop[0]}";
            }

            $model = ExamDate::find()
                ->where(['exam_id' => $fileop[0]])
                ->andWhere(['slug' => $fileop[2]])
                ->one();

            if (!$model) {
                $model = new ExamDate();
                $model->exam_id = $fileop[0];
                $model->slug = $fileop[2];
                $model->name = $fileop[1];
            }

            $model->start = $fileop[3];
            $model->end = $fileop[4];
            $model->type = $fileop[5];
            $model->status = $fileop[6];

            if ($model->save()) {
                echo "{$exam->display_name} \t $model->start \t $model->end \n";
            } else {
                $notMapped['error'][] = $model->getErrors();
            }
        }

        print_r($notMapped);
    }

    public function actionAllArticles()
    {
        $this->actionCategory();
        $this->actionCategoryArticles();
        $this->actionGeneralArticle();
        $this->actionComment();
        $this->actionFaq();
        $this->actionArticle();
    }

    public function actionCategory()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'article_category.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $category = Category::find()->where(['name' => $fileop[0]])->one();

            if (!$category) {
                $category = new Category();
            }

            $category->name = $fileop['0'];
            $category->display_name = $fileop['0'];
            $category->status = Category::STATUS_ACTIVE;
            $category->position = 0;

            if ($category->save()) {
                echo "{$fileop[0]} \t saved \n";
            } else {
                print_r($category->getErrors());
            }
        }
    }

    public function actionCategoryArticles()
    {
        $notMapped = [];
        $oldArticles = GmuCategoryArticle::find();

        foreach ($oldArticles->batch() as $articles) {
            foreach ($articles as $article) {
                // skip deleted articles
                if ($article->status == 3) {
                    continue;
                }

                $type = ArticleDataHelper::$categoryArticleType[$article->type] ?? null;

                if (!$type) {
                    $notMapped[] = "{$article->type} not matched for {$article->page_title}";
                    continue;
                }

                $model = Article::find()
                    ->where(['entity' => $type])
                    ->andWhere(['entity_id' => $article->id])
                    ->one();

                if (!$model) {
                    $model = new Article();
                }

                $model->entity = $type;
                $model->entity_id = $article->id;
                $model->author_id = 1;
                $model->category_id = 1;
                $model->title = $article->page_title;
                $model->slug = $article->seo_url;
                $model->cover_image = $article->banner_image;
                $model->description = $article->content;
                $model->h1 = $article->h1;
                $model->meta_title = $article->page_title;
                $model->meta_description = $article->meta_description;
                $model->view_count = 0;
                $model->is_popular = 0;
                $model->position = 0;
                $model->status = ArticleDataHelper::$oldArticleStatus[$article->status] ?? 0;

                if ($model->save()) {
                    echo "{$model->id} \t {$model->slug} \n";
                } else {
                    $notMapped[] = $model->getErrors();
                    $notMapped['id'][] = $article->seo_url;
                }
            }
        }

        print_r($notMapped);
    }

    public function actionGeneralArticle()
    {
        $notMapped = [];
        $query = GmuArticles::find();

        foreach ($query->batch() as $generalArticles) {
            foreach ($generalArticles as $article) {
                if ($article->status == 3) {
                    continue;
                }

                $model = Article::find()
                    ->where(['entity' => 'articles'])
                    ->andWhere(['entity_id' => $article->id])
                    ->one();

                if (!$model) {
                    $model = new Article();
                }

                $model->entity = 'articles';
                $model->entity_id = $article->id;
                $model->author_id = 1;
                $model->category_id = 1;
                $model->title = $article->page_title;
                $model->slug = $article->seo_url;
                $model->cover_image = $article->banner_image;
                $model->description = $article->content;
                $model->h1 = $article->h1;
                $model->meta_title = $article->page_title;
                $model->meta_description = $article->meta_description;
                $model->view_count = 0;
                $model->is_popular = 0;
                $model->position = 0;
                $model->status = ArticleDataHelper::$oldArticleStatus[$article->status] ?? 0;

                if ($model->save()) {
                    echo "{$model->id} \t {$model->slug} \n";
                } else {
                    $notMapped[] = $model->getErrors();
                }
            }
        }
        print_r($notMapped);
    }

    public function actionComment()
    {
        $notMapped = [];
        $query = GmuArticlesComments::find();

        foreach ($query->batch() as $oldComments) {
            foreach ($oldComments as $comment) {
                if (!isset(ArticleDataHelper::$oldCommentType[$comment->type])) {
                    continue;
                }
                $entity = ArticleDataHelper::$oldCommentType[$comment->type];
                $article = Article::find()->where(['entity' => $entity])->andWhere(['entity_id' => (int)$comment->article_id])->one();

                if (!$article) {
                    $notMapped[] = "{$entity} with id: {$comment->article_id} not found";
                    continue;
                }

                $model = Comment::find()->where(['old_id' => $comment->id])->one();

                if (!$model) {
                    $model = new Comment();
                }

                $model->old_id = $comment->id;
                $model->entity = Article::ENTITY_ARTICLE;
                $model->entity_id = (string) $article->id;
                if ($comment->comment_id) {
                    $model->parent_id = $comment->comment_id;
                }
                $model->name = $comment->user_name;
                $model->email = $comment->user_email;
                $model->comment = ContentHelper::htmlDecode($comment->comment_text, true);
                $model->status = $comment->status == 1 ? 1 : 0;
                $model->created_at = $comment->created_on;
                $model->updated_at = $comment->created_on;

                if ($model->save()) {
                    echo "{$model->id} \t {$model->email} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
        print_r($notMapped);
    }

    public function actionExamFaq()
    {
        $data = Exam::find();
        foreach ($data->batch() as $exams) {
            foreach ($exams as $exam) {
                $oldExam = GmuExam::find()->where(['exam_seo_url' => $exam->slug])->one();
                if (!$oldExam) {
                    continue;
                }
                $oldFaqs = GmuFaq::find()->where(['source_id' => $oldExam->id])->andWhere(['type' => 3])->all();
                if (!$oldFaqs) {
                    continue;
                }
                $items = [];
                foreach ($oldFaqs as $oldFaq) {
                    $items[] = [
                        'question' => ContentHelper::htmlDecode($oldFaq->question, true),
                        'answer' => ContentHelper::htmlDecode($oldFaq->answer, true)
                    ];
                }
                $model = Faq::find()->where(['entity' => Exam::ENTITY_EXAM])->andWhere(['entity_id' => $exam->id])->one();
                if (!$model) {
                    $model = new Faq();
                }
                $model->entity = Exam::ENTITY_EXAM;
                $model->entity_id = $exam->id;
                $model->page = $exam->slug;
                $model->sub_page = 'overview';
                $model->qnas = json_decode(json_encode($items), false);
                $model->status = Faq::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$model->entity} \t $exam->name";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionArticleFaq()
    {
        $data = Article::find();
        foreach ($data->batch() as $articles) {
            foreach ($articles as $article) {
                $oldArticle = GmuArticles::find()->where(['seo_url' => $article->slug])->one();
                if (!$oldArticle) {
                    continue;
                }
                $oldFaqs = GmuFaq::find()->where(['source_id' => $oldArticle->id])->andWhere(['type' => 1])->all();
                if (!$oldFaqs) {
                    continue;
                }
                $items = [];
                foreach ($oldFaqs as $oldFaq) {
                    $items[] = [
                        'question' => ContentHelper::htmlDecode($oldFaq->question, true),
                        'answer' => ContentHelper::htmlDecode($oldFaq->answer, true)
                    ];
                }
                $model = Faq::find()->where(['entity' => Article::ENTITY_ARTICLE])->andWhere(['entity_id' => $article->id])->one();
                if (!$model) {
                    $model = new Faq();
                }
                $model->entity = Article::ENTITY_ARTICLE;
                $model->entity_id = $article->id;
                $model->page = $article->slug;
                $model->sub_page = 'overview';
                $model->qnas = json_decode(json_encode($items), false);
                $model->status = Faq::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$model->entity} \t $article->title";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionArticle()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'article_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $category = Category::find()->where(['name' => $fileop[3]])->one();

            if (!$category) {
                $notMapped[] = $fileop[3] . ' category not found ' . $fileop[0];
                continue;
            }

            $model = Article::find()->where(['slug' => $fileop[2]])->orWhere(['slug' => $fileop[1]])->one();

            if (!$model) {
                $notMapped[] = 'No article found with ' . $fileop[1] . ' slug';
                continue;
            }

            $model->author_id = $fileop[7];
            $model->category_id = $category->id;
            $model->slug = $fileop[2];
            $model->status = Article::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->title} \t $model->id \n";
            } else {
                var_dump($model->getErrors());
            }
        }

        print_r($notMapped);
    }

    public function actionArticleImage()
    {
        $query = Article::find()->active()->select(['id', 'cover_image', 'entity']);

        foreach ($query->batch() as $articles) {
            foreach ($articles as $article) {
                if (!$article->cover_image) {
                    continue;
                }
                $azureImage = ArticleDataHelper::getImage($article->cover_image);

                if (file_exists(Yii::getAlias('@articleGeneral') . '/' . $article->cover_image)) {
                    echo "{$article->id} \t {$article->cover_image} file already exist. \n";
                    continue;
                }

                try {
                    copy($azureImage, Yii::getAlias('@articleGeneral' . '/' . $article->cover_image));
                    echo "{$article->id} \t {$article->cover_image} downloaded \n";
                } catch (\Throwable $th) {
                    print_r($th);
                }
            }
        }
    }

    public function actionGmuNewsToArticle()
    {
        $randomUser = [1173, 747, 541, 580, 1476, 1471, 724, 519, 133];
        $category = Category::find()->where(['slug' => 'others'])->one();
        if (!$category) {
            echo 'Category not found!';
            return;
        }

        $query = GmuNews::find();

        foreach ($query->batch(100, Yii::$app->gmudb) as $newsData) {
            foreach ($newsData as $news) {
                $model = Article::find()->where(['slug' => $news->title_vanity])->one();

                if (!$model) {
                    $model = new Article();
                    $model->author_id = $randomUser[array_rand($randomUser, 1)];
                }

                $model->entity = 'news';
                $model->entity_id = $news->id;
                $model->category_id = $category->id;
                $model->title = $news->title;
                $model->slug = $news->title_vanity;
                $model->cover_image = $news->image_url;
                $model->h1 = $news->title;
                $model->description = $news->content;
                $model->meta_title = $news->title;
                $model->meta_description = 'Find complete information for  including eligibility criteria, deadlines, application procedure for Indian Colleges';
                $model->status = Article::STATUS_ACTIVE;
                $model->view_count = 0;
                $model->is_popular = Article::POPULAR_NO;
                $model->position = 9999;

                if ($model->save()) {
                    echo "{$model->entity} \t {$model->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionCollegeData()
    {
        $this->actionCountry();
        $this->actionState();
        $this->actionCity();
        $this->actionUniversity();
        $this->actionCollege();
        // $this->actionCollegeContent();
        // $this->actionCollegeCutoffContent();
        // $this->actionDegree();
    }

    public function actionCountry()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'country.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $model = Country::find()->where(['slug' => $fileop[1]])->one();

            if (!$model) {
                $model = new Country();
            }

            $model->name = $fileop[0];
            $model->slug = $fileop[1];
            $model->code = $fileop[2];
            $model->iso_code = $fileop[3];
            $model->status = Country::STATUS_INACTIVE;

            if ($model->save()) {
                echo "{$model->name} \t {$model->slug} \t {$fileop[1]} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionState()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'state.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $country = Country::find()->where(['slug' => 'india'])->one();

            if (!$country) {
                echo "{$fileop[0]} \t not found";
                continue;
            }

            $model = State::find()->where(['slug' => $fileop[2]])->one();

            if (!$model) {
                $model = new State();
            }

            $model->country_id = $country->id;
            $model->old_id = $fileop[4];
            $model->name = $fileop[1];
            $model->slug = $fileop[2];
            $model->status = State::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->name} \t {$model->slug} \t {$fileop[1]} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionCity()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'city.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $errors = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $state = State::find()->where(['slug' => $fileop[0]])->one();

            if (!$state) {
                $errors['state'][] = "{$fileop[0]} \t state not found";
                continue;
            }

            $model = City::find()->where(['slug' => $fileop[2]])->one();

            if (!$model) {
                $model = new City();
            }

            $model->old_id = $fileop[3];
            $model->state_id = $state->id;
            $model->name = $fileop[1];
            $model->slug = $fileop[2];
            $model->is_capital = 0;
            $model->is_popular = 0;
            $model->position = 0;
            $model->status = City::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->slug} \n";
            } else {
                print_r($model->getErrors());
            }
        }

        print_r($errors);
    }

    public function actionUniversity()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $errorMsg = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            if ($fileop[13] != College::TYPE_UNIVERSITY) {
                continue;
            }
            $city = City::find()->where(['slug' => $fileop[7]])->one();
            if (!$city) {
                $errorMsg['city'][] = "{$fileop[0]} \t {$fileop[6]} \t city not found";
                continue;
            }

            $model = College::find()->where(['slug' => $fileop[2]])->one();

            if (!$model) {
                $model = new College();
            }

            $model->old_id = $fileop[0];
            $model->name = $fileop[1];
            $model->slug = $fileop[2];
            $model->city_id = $city->id;
            $model->address = $fileop[3];
            // $model->parent_id = 0;
            $model->type = $fileop[13];
            $model->country_code = $fileop[15];
            $model->image = "{$fileop[2]}.jpg";
            $model->url = $fileop[16];
            $model->is_popular = 0;
            $model->status = College::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->name} \t {$model->slug} \n";
            } else {
                $errorMsg['persistErr'][] = $model->getErrors();
            }
        }

        print_r($errorMsg);
    }

    public function actionCollege()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $errorMsg = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            if ($fileop[13] == College::TYPE_UNIVERSITY) {
                continue;
            }

            $city = City::find()->where(['slug' => $fileop[7]])->one();
            if (!$city) {
                $errorMsg['city'][] = "{$fileop[6]} \t city not found";
                continue;
            }

            $university = College::find()->where(['old_id' => $fileop[12]])->one();
            $model = College::find()->where(['slug' => $fileop[2]])->one();

            if (!$model) {
                $model = new College();
            }

            $query = new Query();

            $query->from('gmu_college_images')
                ->where(['gmu_college_id' => $fileop[0]]);

            $imageQuery = $query->one(\Yii::$app->gmudb);

            $model->old_id = $fileop[0];
            $model->name = $fileop[1];
            $model->slug = $fileop[2];
            $model->city_id = $city->id;
            $model->address = $fileop[3];
            if ($university) {
                $model->parent_id = $university->id;
            }
            $model->type = $fileop[13];
            $model->country_code = $fileop[15];
            $model->image = "{$fileop[2]}.jpg";

            if (!empty($imageQuery)) {
                if ($imageQuery['is_banner_image'] == 1) {
                    $model->cover_image = $model->slug . '.jpg' ?? '';
                }

                if ($imageQuery['is_logo_image'] == 1) {
                    $model->logo_image = $model->slug . '.jpg' ?? '';
                }
            }

            $model->url = $fileop[16];
            $model->is_popular = 0;
            $model->status = College::STATUS_ACTIVE;

            try {
                if ($model->save()) {
                    // echo "{$model->name} \t {$model->slug} \n";
                } else {
                    echo "{$model->old_id} \t {$model->slug} \n";
                    print_r($model->getErrors());
                    $errorMsg['persistErr'][] = $model->getErrors();
                }
            } catch (\Throwable $th) {
                $errorMsg['persistErr'][] = $th->getMessage();
                print_r($th->getMessage());
            }
        }

        // print_r($errorMsg);
    }

    public function actionCollegeContent()
    {
        $error = [];
        $query = College::find();

        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                $oldCollege = GmuColleges::find()->where(['id' => $college->old_id])->one();

                if (!$oldCollege) {
                    $error['oldCollege'][] = $college->slug;
                    continue;
                }

                foreach (CollegeHelper::$subPagesColumnMapping as $key => $value) {
                    $model = CollegeContent::find()
                        // ->where(['entity' => College::ENTITY_COLLEGE])
                        ->andWhere(['entity_id' => $college->id])
                        ->andWhere(['sub_page' => $key])
                        ->one();

                    if (!$model) {
                        $model = new CollegeContent();
                    }

                    $status = $this->getStatus($value['content'], $college->old_id);

                    $model->category_id = 1;
                    $model->author_id = 10;
                    $model->entity = College::ENTITY_COLLEGE;
                    $model->entity_id = $college->id;
                    $model->sub_page = $key;
                    $model->meta_title = $oldCollege[$value['title']] ?? '';
                    $model->meta_description = $oldCollege[$value['meta_description']] ?? '';
                    $model->h1 = $oldCollege[$value['h1']] ?? '';

                    if ($key == 'info') {
                        $model->content = $this->getCollegeContent($college->old_id) ?? $oldCollege[$value['content']];
                    } else {
                        $model->content = $oldCollege[$value['content']] ?? '';
                    }

                    $model->status = $status ?? CollegeContent::STATUS_INACTIVE;

                    if ($model->save()) {
                        echo "{$college->id} \t {$college->slug} \t {$model->sub_page} \n";
                    } else {
                        print_r($model->getErrors());
                        $error['databaseErr'][] = $model->getErrors();
                    }
                }
            }
        }

        print_r($error);
    }

    /**
     * Get Info content by college id
     * @param int $collegeId
     *
     * @return string
     */
    private function getCollegeContent($collegeId)
    {
        $query = new Query();
        $query->select(['about', 'admission', 'cutoff', 'hostel'])->from('gmu_college_summary_data')
            ->where(['college_id' => $collegeId]);

        $data = $query->one(Yii::$app->gmudb);

        $content = [];
        $data = $query->one(Yii::$app->gmudb);
        if (!empty($data['about'])) {
            $content[] = $data['about'];
        }
        if (!empty($data['admission'])) {
            $content[] = $data['admission'];
        }
        if (!empty($data['cutoff'])) {
            $content[] = $data['cutoff'];
        }
        if (!empty($data['hostel'])) {
            $content[] = $data['hostel'];
        }

        return !empty($content) ? implode('<br>', $content) : '';
    }

    public function actionCutOff()
    {
        $errorMsg = [];
        $query = College::find();

        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                $cutOffContent = $this->getCutOffContent($college->old_id);
                // if (!$cutOffContent) {
                //     $errorMsg['cut-off'][] = "Cutoff not found for college \t {$college->slug}";
                //     continue;
                // }

                $model = CollegeContent::find()
                    // ->where(['entity' => College::ENTITY_COLLEGE])
                    ->andWhere(['entity_id' => $college->id])
                    ->andWhere(['sub_page' => 'cut-off'])
                    ->one();

                if (!$model) {
                    $errorMsg['cut-off'][] = "New Cutoff not found for college \t {$college->slug}";
                    continue;
                }

                $model->content = $cutOffContent;
                $model->status = $this->getStatus('cut-off', $college->old_id);

                try {
                    $model->save();
                    echo "{$college->slug} \n";
                } catch (\Throwable $th) {
                    $errorMsg['persistError'][] = $th->getMessage();
                }
            }
        }
        print_r($errorMsg);
    }

    public function actionCollegeCourse()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_course.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $errors = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[4])) {
                $i++;
                continue;
            }

            $college = College::find()->where(['old_id' => $fileop[1]])->one();

            if (!$college) {
                $errors['college'][] = "{$fileop[1]} college Id not found on program slug {$fileop[3]}";
                echo "{$fileop[1]} college Id not found on program slug {$fileop[3]} \n";
                continue;
            }

            $course = Course::find()->where(['slug' => $fileop[5]])->one();

            if (!$course) {
                $errors['course'][] = "{$fileop[5]} course not found on program slug {$fileop[3]}";
                echo "{$fileop[5]} course not found on program slug {$fileop[3]} \n";
                continue;
            }

            $model = CollegeCourse::find()->where(['slug' => $fileop[3]])->andWhere(['college_id' => $college->id])->one();

            if (!$model) {
                $model = new CollegeCourse();
            }

            $model->college_id = $college->id;
            $model->course_id = $course->id;
            $model->name = $fileop[2];
            $model->slug = $fileop[3];
            $model->degree = $fileop[6];
            $model->mode = $fileop[9] ?? '';
            $model->type = $fileop[10];
            $model->total_seat = $fileop[11] ?? '';
            $model->duration = $fileop[12] ?? '';
            $model->fee = $fileop[13] ?? '';
            $model->status = $fileop[14];
            $model->alias = $fileop[15] ?? '';
            $model->admission_fee = $fileop[16] ?? '';
            $model->eligiblity_criteria = $fileop[17] ?? '';
            $model->created_at = new \yii\db\Expression('NOW()');
            $model->updated_at = new \yii\db\Expression('NOW()');

            try {
                if ($model->save()) {
                    // echo "{$model->id} \t {$model->slug} \n";
                } else {
                    $errors['persistErr'][] = $model->getErrors();
                    print_r($model->getErrors());
                }
            } catch (\Throwable $th) {
                $errors['persistErr'][] = $th->getMessage();
                print_r($th->getMessage());
            }
        }

        echo "\n \n";
        echo "---------------------------------------- \n";
        print_r($errors);

        // if (!empty($errors)) {
        //     $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'error_college_course.csv';

        //     $fileWrite = fopen($file, 'wb');

        //     foreach ($errors as $rows) {
        //         fputcsv($fileWrite, $rows, "\n");
        //     }
        //     fclose($fileWrite);
        // }
    }

    public function actionCollegeExamMapping()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_exam.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $college = College::find()->where(['slug' => $fileop[0]])->one();

            if (!$college) {
                $notMapped['college'][] = "$fileop[0] \t $fileop[1]";
                continue;
            }


            $exam = Exam::find()->where(['slug' => $fileop[1]])->one();
            if (!$exam) {
                $notMapped['exam'][] = $fileop[1];
                continue;
            }

            $college->link('exams', $exam);

            echo "\t $exam->display_name \t $college->name \n";
        }
        print_r($notMapped);
    }

    /**
     * Get cutoff content by college id
     * @param int $collegeId
     * @return array|[]
     */
    public function getCutOffContent($collegeId)
    {

        $query = new Query();
        $query->from('gmu_cutoff_content')
            ->where(['college_id' => $collegeId]);

        if ($data = $query->one(Yii::$app->gmudb)) {
            return $data['page_info'];
        }

        return '';
    }

    public function actionFeatureGroup()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'distinct-features.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $featureGroup = FeatureGroup::find()->where(['slug' => Inflector::slug($fileop[0])])->one();
            if (!$featureGroup) {
                $featureGroup = new FeatureGroup();
            }
            $featureGroup->category_id = 1;
            $featureGroup->name = $fileop[0];
            $featureGroup->slug = Inflector::slug($fileop[0]);
            $featureGroup->status = FeatureGroup::STATUS_ACTIVE;

            if ($featureGroup->save()) {
                $feature = Feature::find()->where(['slug' => Inflector::slug($fileop[1])])->one();
                if (!$feature) {
                    $feature = new Feature();
                }
                $feature->feature_group_id = $featureGroup->id;
                $feature->name = $fileop[1];
                $feature->slug = Inflector::slug($fileop[1]);
                $feature->status = Feature::STATUS_ACTIVE;

                if ($feature->save()) {
                    echo "{$featureGroup->name} \t {$feature->name} \n";
                } else {
                    print_r($feature->getErrors());
                }
            } else {
                print_r($featureGroup->getErrors());
            }
        }
    }

    public function actionFeatureValues()
    {
        Yii::$app->db->createCommand('SET foreign_key_checks = 0;')->execute();
        Yii::$app->db->createCommand()->truncateTable('college_feature_value')->execute();
        Yii::$app->db->createCommand()->truncateTable('feature_value')->execute();
        Yii::$app->db->createCommand('SET foreign_key_checks = 1;')->execute();
        $items = ['accreditations', 'recognitions', 'approvals', 'facilities'];
        foreach ($items as $item) {
            $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . $item . '.csv';

            if (($handle = fopen($file, 'r')) == false) {
                echo "Unable to open the file \n";
                die(__FILE__);
            }

            $error = [];
            $i = 0;
            while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
                if ($i == 0 || empty($fileop[1])) {
                    $i++;
                    continue;
                }

                $feature = Feature::find()->where(['slug' => Inflector::slug($fileop[2])])->one();
                if (!$feature) {
                    $error['feature'][] = "{$fileop[2]} feature not found";
                    continue;
                }
                $college = College::find()->where(['old_id' => $fileop[1]])->one();
                if (!$college) {
                    $error['college'][] = "{$fileop[0]} not found";
                    continue;
                }
                $featureValue = new FeatureValue();
                $featureValue->feature_id = $feature->id;
                $featureValue->value = 'Yes';
                $featureValue->status = FeatureValue::STATUS_ACTIVE;
                if ($featureValue->save()) {
                    echo "{$college->name} \t {$feature->name} \n";
                    $college->link('featureValues', $featureValue);
                } else {
                    print_r($featureValue->getErrors());
                }
            }
        }
        $this->actionCollegeHighlights();
    }

    public function actionCollegeHighlights()
    {
        $featureGroup = FeatureGroup::find()->where(['slug' => 'highlights'])->one();
        if (!$featureGroup) {
            echo "Highlight feature group not found \n";
            exit;
        }

        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'highlights.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        $errors = [];
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            $college = College::find()->where(['old_id' => $fileop[0]])->one();
            if (!$college) {
                $errors['college'][] = "{$fileop[1]} not found \t";
                continue;
            }
            $highlightsFeatures = [2 => 'Institution Type', 3 => 'Faculty Student Ratio', 4 => 'Total Faculty'];
            foreach ($highlightsFeatures as $key => $value) {
                $feature = Feature::find()->where(['slug' => Inflector::slug($value)])->one();
                if (!$feature) {
                    $errors['features'][] = "{$value} not found \n";
                    continue;
                }
                if ($fileop[$key] == '-' || $fileop[$key] == '0') {
                    continue;
                }
                $featureValue = new FeatureValue();
                $featureValue->feature_id = $feature->id;
                $featureValue->value = $fileop[$key];
                $featureValue->status = FeatureValue::STATUS_ACTIVE;
                if ($featureValue->save()) {
                    echo "{$college->name} \t {$feature->name} \n";
                    $college->link('featureValues', $featureValue);
                } else {
                    print_r($featureValue->getErrors());
                }
            }
        }
    }

    private function getStatus($key, $collegeId)
    {
        $query = new Query();
        switch ($key) {
            case 'courses-fees':
                $key = 'fees';
                break;

            case 'cut-off':
                $key = 'cutoff';
                break;

            case 'qna':
                $key = 'forum';
                break;

            case 'abt_college':
                $key = 'info';
                break;

            case 'fees_page_info':
                $key = 'fees';
                break;

            case 'career':
                $key = 'placements';
                break;

            case 'abt_infra':
                $key = 'facilities';
                break;

            case 'scho':
                $key = 'scholarships';
                break;

            case 'gallery':
                $key = 'images';
                break;
        }

        if (!empty($key)) {
            $query->select($key)->from('gmu_college_page_menu_new')
                ->where(['college_id' => $collegeId]);

            if ($data = $query->one(Yii::$app->gmudb)) {
                if ($data[$key] >= 1) {
                    return CollegeContent::STATUS_ACTIVE;
                }
            }
        }

        return [];
    }

    /**
     * Getting College Images and Videos
     *
     */
    public function actionCollegeGallery()
    {
        $error = [];
        $query = College::find();

        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                $oldCollegeImages = GmuCollegeImagesNew::find()->where(['gmu_college_name' => $college->slug])->all();

                $oldCollegeVideos = GmuColleges::find()->select(['video1', 'video2', 'video3', 'video4', 'video5'])->where(['college_urlname' => $college->slug])->one();

                if (!$oldCollegeImages) {
                    continue;
                }

                foreach ($oldCollegeImages as $oldCollegeImage) {
                    $checkImage = $this->checkGalleryData($college->id, $oldCollegeImage['category_name'], $oldCollegeImage['image_name']);

                    if ($checkImage) {
                        continue;
                    }

                    $model = new Gallery();
                    $model->entity = College::ENTITY_COLLEGE;
                    $model->entity_id = $college->id;
                    $model->type = $oldCollegeImage['category_name'];
                    $model->file = $oldCollegeImage['image_name'];
                    $model->status = Gallery::STATUS_ACTIVE;

                    try {
                        $model->save();
                        echo "{$college->slug} \t {$model->type} \t {$model->file} \n";
                    } catch (\Throwable $th) {
                        $error['databaseErr'][] = $th->getMessage();
                    }
                }

                foreach ($oldCollegeVideos as $key => $video) {
                    if (!empty($video)) {
                        $checkVideo = $this->checkGalleryData($college->id, 'videos', $oldCollegeImage['image_name']);

                        if (!$checkVideo) {
                            $model = new Gallery();
                        }

                        $model->entity = College::ENTITY_COLLEGE;
                        $model->entity_id = $college->id;
                        $model->type = 'videos';
                        $model->file = $video;
                        $model->status = Gallery::STATUS_ACTIVE;

                        try {
                            $model->save();
                            echo "{$college->slug} \t {$model->type} \t {$model->file} \n";
                        } catch (\Throwable $th) {
                            $error['databaseErr'][] = $th->getMessage();
                        }
                    }
                }
            }
        }

        print_r($error);
    }

    public function actionCollegeFaq()
    {

        $faqMapping = [
            '8' => 'info',
            '9' => 'admission',
            '10' => 'courses-fees'
        ];

        $data = College::find();
        foreach ($data->batch() as $colleges) {
            foreach ($colleges as $college) {
                $oldCollege = GmuColleges::find()->where(['college_urlname' => $college->slug])->one();
                if (!$oldCollege) {
                    continue;
                }


                foreach ($faqMapping as $key => $value) {
                    $oldFaqs = GmuFaq::find()->where(['source_id' => $oldCollege->id])->andWhere(['type' => $key])->all();

                    if (!$oldFaqs) {
                        continue;
                    }
                    $items = [];
                    foreach ($oldFaqs as $oldFaq) {
                        $items[] = [
                            'question' => ContentHelper::htmlDecode($oldFaq->question, true),
                            'answer' => ContentHelper::htmlDecode($oldFaq->answer, true)
                        ];
                    }
                    $model = Faq::find()
                        ->where(['entity' => College::ENTITY_COLLEGE])
                        ->andWhere(['entity_id' => $college->id])
                        ->andWhere(['sub_page' => $faqMapping[$oldFaq->type]])
                        ->one();

                    if (!$model) {
                        $model = new Faq();
                    }

                    $model->entity = College::ENTITY_COLLEGE;
                    $model->entity_id = $college->id;
                    $model->page = $college->slug;
                    $model->sub_page = $faqMapping[$oldFaq->type];
                    $model->qnas = json_decode(json_encode($items), false);
                    $model->status = Faq::STATUS_ACTIVE;

                    if ($model->save()) {
                        echo "{$model->entity} \t $college->name \n";
                    } else {
                        print_r($model->getErrors());
                    }
                }
            }
        }
    }

    private function checkGalleryData($id, $type, $file)
    {
        return Gallery::find()
            ->where(['entity' => College::ENTITY_COLLEGE])
            ->andWhere(['entity_id' => $id])
            ->andWhere(['type' => $type])
            ->andWhere(['file' => $file])
            ->one();
    }

    public function actionCollegeCourseToElastic()
    {
        $query = CollegeCourse::find();
        $query->with(['college', 'course', 'exams', 'specialization_new']);

        foreach ($query->batch() as $collegeCourses) {
            foreach ($collegeCourses as $collegeCourse) {
                $collection = CollectionCollegeCourse::find()
                    ->where(['college_id' => $collegeCourse->college_id])
                    ->andWhere(['college_course_id' => $collegeCourse->id])
                    ->one();

                if (!$collection) {
                    $collection = new CollectionCollegeCourse();
                }

                if (empty($collegeCourse->course)) {
                    continue;
                }
                $collection->college_course_id = (int)$collegeCourse->id;
                $collection->college_id = (int)$collegeCourse->college_id;
                $collection->program = $collegeCourse->name;
                $collection->program_slug = $collegeCourse->slug;
                $collection->fees = (int)$collegeCourse->fee;
                $collection->duration = $collegeCourse->duration;
                $collection->total_seat = $collegeCourse->total_seat;
                $collection->type = $collegeCourse->type;
                $collection->degree = $collegeCourse->degree;
                $collection->mode = $collegeCourse->mode;
                $collection->stream_id = (int)$collegeCourse->course->stream_id ?? '';
                $collection->stream_slug = $collegeCourse->course->stream->slug ?? '';
                $collection->stream_name = $collegeCourse->course->stream->name ?? '';
                $collection->specialization_id = !empty($collegeCourse->specialization_id)  ? (int)$collegeCourse->specialization_id : '';

                if (!empty($collegeCourse->specialization)) {
                    $collection->specialization_slug = $collegeCourse->specialization->slug ?? '';
                    $collection->specialization_name = $collegeCourse->specialization->display_name ?? $collegeCourse->specialization->name;
                }

                $collection->course_id = $collegeCourse->course->id ?? '';
                $collection->course = $collegeCourse->course->name ?? '';
                $collection->course_short_name = $collegeCourse->course->short_name ?? '';
                $collection->course_slug = $collegeCourse->course->slug ?? '';
                $collection->isCiPage = CollegeService::getCiPageInfo($collegeCourse->college_id, $collegeCourse->course->id);

                //brochure
                $broucher = CollegeHelper::getCourseBrochure($collegeCourse->course->id, $collegeCourse->college_id);
                $collection->course_brochure = !empty($broucher) ? $broucher->pdf : '';

                $collegeCourseContent = CollegeService::getCourseContent($collegeCourse->course->id, $collegeCourse->college_id);
                $collection->course_eligibility =  !empty($collegeCourseContent) ? $collegeCourseContent->eligibility : '';
                $collection->course_position = $collegeCourse->course->position > 0 ? $collegeCourse->course->position : '';
                $collection->courseAvgFees = CollegeService::getAvgFees($collegeCourse->course->id, $collegeCourse->college_id);

                $exam = ArrayHelper::map($collegeCourse->exams, 'id', 'display_name');
                $collection->exams = implode(',', $exam);
                $collection->status = (int)$collegeCourse->status;

                if ($collection->save()) {
                    echo "{$collection->program_slug} \n";
                } else {
                    print_r($collection->getErrors());
                }
            }
        }
    }

    public function setAvgFees($courseId, $collegeId)
    {
        $query = new Query();
        $query->from('college_course')
            ->where(['college_id' => $collegeId, 'course_id' => $courseId]);

        $avgFees = $query->average('fee');

        return $avgFees ?? 0;
    }

    public function actionCollegeCourseExam()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'program_exam.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $errors = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[5])) {
                $i++;
                continue;
            }

            $college = College::find()->where(['old_id' => $fileop[1]])->one();

            if (!$college) {
                $errors['college'][] = "{$fileop[1]} college Id not found on program slug {$fileop[3]}";
                continue;
            }

            $exam = Exam::find()->where(['id' => $fileop[4], 'slug' => $fileop[5]])->one();

            if (!$exam) {
                $errors['exam'][] = "{$fileop[5]} exam not found on program slug {$fileop[3]}";
                continue;
            }

            $collegeCourse = CollegeCourse::find()->where(['slug' => $fileop[3]])->one();

            if (!$collegeCourse) {
                $errors['college-course'][] = "{$fileop[3]} collegeCourse not found.";
                continue;
            }

            $model = CollegeCourseExam::find()->where(['college_course_id' => $collegeCourse->id])->andWhere(['exam_id' => $exam->id])->one();

            if (!$model) {
                $model = new CollegeCourseExam();
                $model->college_course_id = $collegeCourse->id;
                $model->exam_id = $exam->id;
            }

            if ($model->save()) {
                echo "{$model->college_course_id} -- {$fileop[3]} \n";
            } else {
                print_r($model->getErrors());
            }
        }

        if (!empty($errors)) {
            $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'error_college_course_exam.csv';

            $fileWrite = fopen($file, 'wb');

            foreach ($errors as $rows) {
                fputcsv($fileWrite, $rows, "\n");
            }
            fclose($fileWrite);
        }

        print_r($errors);
    }

    public function admissionP1($slug)
    {
        $content = GmuColleges::find()->where(['college_urlname' => $slug])->select(['admissions'])->one();

        return $content->admissions ?? '';
    }

    public function admissionP2($oldId)
    {
        $query = (new Query())->from('gmu_admissions_new')->where(['college_id' => $oldId])->all(Yii::$app->gmudb);
        if (empty($query)) {
            return '';
        }

        $content = [];
        foreach ($query as $data) {
            if (!empty($data['eligibility'])) {
                $content[] = $data['eligibility'];
            }
            if (!empty($data['application_process'])) {
                $content[] = $data['application_process'];
            }
            if (!empty($data['admission_process'])) {
                $content[] = $data['admission_process'];
            }
        };

        return !empty($content) ? implode('<br>', $content) : '';
    }

    public function admissionP3($oldId)
    {
        $query = (new Query())->from('gmu_admissions')->where(['college_id' => $oldId])->all(Yii::$app->gmudb);
        if (empty($query)) {
            return '';
        }

        $content = [];
        foreach ($query as $data) {
            if (!empty($data['eligibility'])) {
                $content[] = $data['eligibility'];
            }
            if (!empty($data['application_process'])) {
                $content[] = $data['application_process'];
            }
            if (!empty($data['admission_process'])) {
                $content[] = $data['admission_process'];
            }
            if (!empty($data['results'])) {
                $content[] = $data['results'];
            }
        };

        return !empty($content) ? implode('<br>', $content) : '';
    }

    /**
     * Get admission content by college id
     * @param int $collegeId
     * @return array | string |[]
     */
    public function getAdmissionContent($college)
    {
        if (!empty($this->admissionP1($college->slug))) {
            return $this->admissionP1($college->slug);
        }
        if (!empty($this->admissionP2($college->old_id))) {
            return $this->admissionP2($college->old_id);
        }
        if (!empty($this->admissionP3($college->old_id))) {
            return $this->admissionP3($college->old_id);
        }

        return '';
    }

    public function actionUpdateAdmissionContent()
    {
        $errorMsg = [];
        $query = College::find();

        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                $admissionData = $this->getAdmissionContent($college);

                $model = CollegeContent::find()
                    ->andWhere(['entity_id' => $college->id])
                    ->andWhere(['sub_page' => 'admission'])
                    ->one();

                if (!$model) {
                    echo "{$college->id} \t {$college->slug} \t Admission model not found for college \n";
                    continue;
                }

                $model->content = $admissionData;
                $model->status = CollegeContent::STATUS_ACTIVE;

                try {
                    $model->save();
                    echo "{$college->id} \t {$college->slug} \n";
                } catch (\Throwable $th) {
                    print_r($th->getMessage());
                }
            }
        }
    }


    public function actionUpdateStatus()
    {
        $collegeColumn = CollegeHelper::$subPagesColumnMapping;
        $query = CollegeContent::find()->select(['id', 'entity_id', 'sub_page', 'status', 'author_id', 'entity']);
        foreach ($query->batch() as $collegeContents) {
            foreach ($collegeContents as $content) {
                $college = College::find()->select(['old_id'])->where(['id' => $content->entity_id])->one();
                if ($content->sub_page == 'cut-off') {
                    $key = 'cut-off';
                } else {
                    $key = $collegeColumn[$content->sub_page]['content'] ?? '';
                }

                $content->status = $this->getStatus($key, $college->old_id);

                if ($content->save()) {
                    echo "{$content->id} \t {$college->slug} \t {$content->sub_page} \n";
                } else {
                    print_r($content->getErrors());
                }
            }
        }
    }

    public function actionBoards()
    {
        $this->actionAllBoards();
        $this->actionBoardLogo();
        $this->actionBoardContent();
        $this->actionBoardAuthor();
        $this->actionBoardSamplePaper();
        $this->actionSamplePaperAuthor();
        $this->actionBoardExams();
        $this->actionBoardFaq();
        $this->actionBoardComment();
    }

    public function actionAllBoards()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'boards.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $model = Board::find()->where(['slug' => $fileop[2]])->one();
            if (!$model) {
                $model = new Board();
            }

            $model->id = $fileop[0];
            $model->state_id = $fileop[4] ?? '';
            $model->name = $fileop[1];
            $model->slug = $fileop[2];
            $model->display_name = $fileop[3];
            $model->type = $fileop[5] ?? '';
            $model->level = $fileop[6] ?? '';
            $model->status = $fileop[7];

            if ($model->save()) {
                echo "{$model->id} \t {$model->name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionBoardContent()
    {
        $boards = Board::find()->all();

        foreach ($boards as $board) {
            $query = new Query();
            $query->from('gmu_boards_keyword_tag gbkt')
                ->innerJoin('gmu_exam_boards_section as gebs', 'gbkt.section_id = gebs.id')
                ->where(['gbkt.board_id' => $board->id]);

            $data = $query->all(Yii::$app->gmudb);

            if (!$data) {
                echo "{$board->name} \t content not found \n";
                continue;
            }
            $page = [];
            foreach ($data as $value) {
                $model = BoardContent::find()
                    ->where(['board_id' => $board->id])
                    ->andWhere(['page_slug' => $value['section_slug']])
                    ->one();

                if (!$model) {
                    $model = new BoardContent();
                }

                $model->board_id = $board->id;
                $model->page = $value['section'];
                $model->page_slug = $value['section_slug'];
                $model->content = $value['data'];
                $model->cover_image = $value['article_schema_image'];
                $model->meta_title = $value['page_title'];
                $model->meta_description = $value['meta_description'];
                $model->h1 = $value['h1'];
                $model->status = BoardContent::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$board->name} \t $model->page \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionBoardAuthor()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'boards.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $boardContent = BoardContent::find()->where(['board_id' => $fileop[0]])->all();

            if (!$boardContent) {
                continue;
            }

            foreach ($boardContent as $content) {
                $model = BoardContent::find()
                    ->where(['board_id' => $content->board_id])
                    ->andWhere(['page_slug' => $content->page_slug])
                    ->one();

                if (!$model) {
                    continue;
                }

                $model->author_id = $fileop[8];

                if ($model->save()) {
                    echo "{$model->id} \t $model->page \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionBoardLogo()
    {
        $file = Yii::getAlias('@frontend') . DIRECTORY_SEPARATOR . 'web' . DIRECTORY_SEPARATOR . 'yas' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'board-logos';

        $files = FileHelper::findFiles($file);

        foreach ($files as $image) {
            $boardImage = explode('/', $image);

            $logo = str_replace(['.jpg', '.png', '.jpeg'], '', $boardImage[10]);

            $model = Board::find()
                ->where(['slug' => $logo])
                ->one();

            if (!$model) {
                continue;
            }

            if (strpos($boardImage[10], '.jpg') !== false) {
                $model->logo = "{$logo}.jpg";
            } elseif (strpos($boardImage[10], '.png') !== false) {
                $model->logo = "{$logo}.png";
            } else {
                $model->logo = "{$logo}.jpeg";
            }

            if ($model->save()) {
                echo "{$model->id} \t $model->name \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionBoardFaq()
    {
        $boards = Board::find()->all();
        foreach ($boards as $board) {
            $query = GmuBoardsKeywordTag::find()->where(['board_id' => $board->id])->one();
            if (!$query) {
                continue;
            }

            $faqs = GmuFaq::find()->where(['type' => 4])->andWhere(['source_id' => $query->id])->all();
            if (!$faqs) {
                continue;
            }

            $model = Faq::find()->where(['entity' => Board::ENTITY_BOARD])->andWhere(['entity_id' => $board->id])->one();
            if (!$model) {
                $model = new Faq();
            }

            $items = [];
            foreach ($faqs as $oldFaq) {
                $items[] = [
                    'question' => ContentHelper::htmlDecode($oldFaq->question, true),
                    'answer' => ContentHelper::htmlDecode($oldFaq->answer, true)
                ];
            }
            $model->entity = Board::ENTITY_BOARD;
            $model->entity_id = $board->id;
            $model->page = $board->slug;
            $model->sub_page = 'overview';
            $model->qnas = json_decode(json_encode($items), false);
            $model->status = Faq::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->entity} \t {$board->name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionBoardSamplePaper()
    {
        $papers = [
            'previous-year-question-papers' => 19,
            'solved-question-papers' => 20,
            'sample-papers' => 18
        ];

        $queries = BoardContent::find()
            ->andWhere(['in', 'page_slug', ['sample-papers', 'previous-year-question-papers', 'solved-question-papers']]);

        foreach ($queries->batch() as $boardsContent) {
            foreach ($boardsContent as $boardContent) {
                if (isset($papers[$boardContent->page_slug])) {
                    $oldBoardPapers = (new Query())->from('gmu_boards_paper_keyword_tag')
                        ->where(['board_id' => $boardContent->board_id])
                        ->andWhere(['section_id' => $papers[$boardContent->page_slug]])
                        ->all(Yii::$app->gmudb);

                    if (empty($oldBoardPapers)) {
                        continue;
                    }

                    foreach ($oldBoardPapers as $oldBoardPaper) {
                        $model = BoardSamplePaper::find()
                            ->where(['board_content_id' => $boardContent->id])
                            ->andWhere(['subject_slug' => $oldBoardPaper['subject_slug']])
                            ->andWhere(['slug' => $oldBoardPaper['seo_url']])
                            ->one();

                        if (!$model) {
                            $model = new BoardSamplePaper();
                        }

                        $model->board_content_id = $boardContent->id;
                        $model->subject_slug = $oldBoardPaper['subject_slug'];
                        $model->slug = $oldBoardPaper['seo_url'];
                        $model->h1 = $oldBoardPaper['h1'];
                        $model->meta_title = $oldBoardPaper['page_title'];
                        $model->meta_description = $oldBoardPaper['meta_description'];
                        $model->content = $oldBoardPaper['subject_content'];
                        $model->status = BoardContent::STATUS_ACTIVE;

                        if ($model->save()) {
                            echo "{$model->board_content_id} \t {$model->subject_slug} \t {$model->slug} \n";

                            $oldSamplePapers = (new Query())->from('gmu_exam_boards_papers')
                                ->where(['mapping_id' => $oldBoardPaper['id']])
                                ->all(Yii::$app->gmudb);

                            if (empty($oldSamplePapers)) {
                                continue;
                            }

                            foreach ($oldSamplePapers as $oldSamplePaper) {
                                $samplePaperModel = BoardSamplePaperFile::find()
                                    ->where(['board_sample_paper_id' => $model->id])
                                    ->andWhere(['year' => $oldSamplePaper['year']])
                                    ->andWhere(['set' => $oldSamplePaper['paper_set']])
                                    ->andWhere(['question_paper' => $oldSamplePaper['question_paper']])
                                    ->one();

                                if (!$samplePaperModel) {
                                    $samplePaperModel = new BoardSamplePaperFile();
                                }

                                $samplePaperModel->board_sample_paper_id = $model->id;
                                $samplePaperModel->year = $oldSamplePaper['year'];
                                $samplePaperModel->question_paper = $oldSamplePaper['question_paper'];
                                $samplePaperModel->answer_paper = $oldSamplePaper['solutions'];
                                $samplePaperModel->set = $oldSamplePaper['paper_set'];
                                $samplePaperModel->status = BoardSamplePaperFile::STATUS_ACTIVE;

                                if ($samplePaperModel->save()) {
                                    echo "{$model->slug} \t {$samplePaperModel->question_paper} \n";
                                } else {
                                    print_r($samplePaperModel->getErrors());
                                }
                            }
                        } else {
                            print_r($model->getErrors());
                        }
                    }
                }
            }
        }
    }

    public function actionSamplePaperAuthor()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'boards.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $boardContent = BoardContent::find()
                ->where(['board_id' => $fileop[0]])
                ->andWhere(['in', 'page_slug', ['sample-papers', 'previous-year-question-papers', 'solved-question-papers']])
                ->all();

            if (!$boardContent) {
                continue;
            }

            foreach ($boardContent as $content) {
                $query = BoardSamplePaper::find()
                    ->where(['board_content_id' => $content->id])
                    ->all();

                foreach ($query as $subject) {
                    $model = BoardSamplePaper::find()
                        ->where(['board_content_id' => $subject->board_content_id])
                        ->andWhere(['subject_slug' => $subject->subject_slug])
                        ->one();

                    if (!$model) {
                        continue;
                    }

                    $model->author_id = $fileop[8];

                    if ($model->save()) {
                        echo "{$model->board_content_id} \t $model->subject_slug \n";
                    } else {
                        print_r($model->getErrors());
                    }
                }
            }
        }
    }

    public function actionBoardExams()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'board_exam.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        $notMapped = [];

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $exam = Exam::find()->where(['slug' => $fileop[2]])->one();

            if (!$exam) {
                $notMapped['exam'][] = "$fileop[1] \t $fileop[2]";
                continue;
            }


            $board = Board::find()->where(['id' => $fileop[0], 'slug' => $fileop[1]])->one();
            if (!$board) {
                $notMapped['exam'][] = $fileop[1];
                continue;
            }

            $board->link('exams', $exam);

            echo "\t $exam->display_name \t $board->name \n";
        }
        print_r($notMapped);
    }

    public function actionBoardComment()
    {
        $boards = Board::find()->all();

        foreach ($boards as $board) {
            $oldQuery = (new Query())->from('gmu_boards_paper_keyword_tag')
                ->where(['board_id' => $board->id])
                ->all(Yii::$app->gmudb);

            if (!$oldQuery) {
                continue;
            }

            foreach ($oldQuery as $value) {
                $comments = GmuArticlesComments::find()
                    ->where(['type' => 3])
                    ->andWhere(['article_id' => $value['id']])
                    ->andWhere(['status' => Comment::STATUS_ACTIVE])
                    ->all();

                foreach ($comments as $comment) {
                    if (!$comment) {
                        continue;
                    }

                    $boardSamplePaper = BoardSamplePaper::find()->where(['slug' => $value['seo_url']])->all();

                    if (!$boardSamplePaper) {
                        continue;
                    }

                    foreach ($boardSamplePaper as $paper) {
                        $model = Comment::find()->where(['old_id' => $comment->id])
                            ->andWhere(['entity' => BoardSamplePaper::ENTITY_BOARD_SAMPLE_PAPER])
                            ->andWhere(['entity_id' => $paper->id])
                            ->one();

                        if (!$model) {
                            $model = new Comment();
                        }

                        $model->old_id = $comment->id;
                        $model->entity = BoardSamplePaper::ENTITY_BOARD_SAMPLE_PAPER;
                        $model->entity_id = (string)$paper->id;
                        if ($comment->comment_id) {
                            $model->parent_id = $comment->comment_id;
                        }
                        $model->name = $comment->user_name;
                        $model->email = $comment->user_email;
                        $model->comment = ContentHelper::htmlDecode($comment->comment_text, true);
                        $model->status = Comment::STATUS_ACTIVE;
                        $model->created_at = $comment->created_on;
                        $model->updated_at = $comment->created_on;

                        if ($model->save()) {
                            echo "{$model->id} \t {$model->email} \n";
                        } else {
                            print_r($model->getErrors());
                        }
                    }
                }
            }
        }
    }

    public function actionUpdateGoogleAdsStatus()
    {

        $data = College::find();
        foreach ($data->batch() as $colleges) {
            foreach ($colleges as $college) {
                $oldCollege = GmuColleges::find()->where(['college_urlname' => $college->slug])->one();
                if (!$oldCollege) {
                    continue;
                }

                $model = College::find()
                    ->where(['id' => $college->id])
                    ->one();

                if (!$model) {
                    echo "{$college->id} \t {$college->slug} \t not found \n";
                    continue;
                }

                $model->is_google_ads = $oldCollege->google_ads_display;

                try {
                    $model->save();
                    echo "{$college->id} \t {$college->slug} \n";
                } catch (\Throwable $th) {
                    print_r($th->getMessage());
                }
            }
        }
    }

    public function actionCollegeDisplayName()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_display_name.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        $notMapped = [];

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[2])) {
                $i++;
                continue;
            }

            $college = College::find()->where(['slug' => $fileop[2]])->one();

            if (!$college) {
                $notMapped['college'][] = "$fileop[1] \t $fileop[2] \n";
                continue;
            }

            $college->display_name = $fileop[3];
            $college->name = $fileop[1];

            if ($college->save()) {
                echo "\t $college->display_name \t $college->name \n";
            }
        }
        print_r($notMapped);
    }

    public function actionSaArticles()
    {
        // sa-articles.csv
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'sa-articles.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $query = new Query();
            $query->from('gmu_sa_articles')
                ->where(['seo_url' => $fileop[6]]);

            if (!$query = $query->one(Yii::$app->gmudb)) {
                $notMapped['slug-missing'][] = $fileop[6];
                continue;
            }

            $model = Article::find()->where(['slug' => $fileop[6]])->andWhere(['entity' => Article::ENTITY_STUDY_ABROAD])->one();

            if (!$model) {
                $model = new Article();
                $model->slug = $fileop[6];
            }

            $model->entity = Article::ENTITY_STUDY_ABROAD;
            $model->entity_id = $fileop[2];
            $model->category_id = 20;
            $model->author_id = $fileop[15];
            $model->country_slug = $fileop[8];
            $model->title = $fileop[3];
            $model->h1 = $fileop[3];
            $model->meta_title = $fileop[5];
            $model->meta_description = $fileop[4];
            $model->view_count = $fileop[12];
            $model->cover_image = $fileop[7];
            $model->description = $query['content'];
            $model->is_popular = $fileop[13];
            $model->position = $fileop[14];
            $model->status = $this->getStatusCode($fileop[11]);

            if ($model->save()) {
                echo "{$model->title} \n";
            } else {
                print_r($model->getErrors());
            }
        }
        print_r($notMapped);
    }

    public function getStatusCode($oldStatusCode)
    {
        switch ($oldStatusCode) {
            case '1':
                return 2;
                break;

            case '2':
                return 1;
                break;

            case '3':
                return 0;
                break;

            default:
                return 0;
                break;
        }
    }

    public function actionStudyAbroadComment()
    {
        $notMapped = [];
        $query = GmuArticlesComments::find()->where(['type' => 10]);

        foreach ($query->batch() as $oldComments) {
            foreach ($oldComments as $comment) {
                if (!isset(ArticleDataHelper::$oldCommentType[$comment->type])) {
                    continue;
                }

                $entity = ArticleDataHelper::$oldCommentType[$comment->type];
                $article = Article::find()->where(['entity' => $entity])->andWhere(['entity_id' => (int)$comment->article_id])->one();

                if (!$article) {
                    $notMapped[] = "{$entity} with id: {$comment->article_id} not found";
                    continue;
                }

                $model = Comment::find()->where(['old_id' => $comment->id])->one();

                if (!$model) {
                    $model = new Comment();
                }

                $model->old_id = $comment->id;
                $model->entity = Article::ENTITY_STUDY_ABROAD;
                $model->entity_id = (string) $article->id;
                if ($comment->comment_id) {
                    $model->parent_id = $comment->comment_id;
                }
                $model->name = $comment->user_name;
                $model->email = $comment->user_email;
                $model->comment = ContentHelper::htmlDecode($comment->comment_text, true);
                $model->status = $comment->status == 1 ? 1 : 0;
                $model->created_at = $comment->created_on;
                $model->updated_at = $comment->created_on;

                if ($model->save()) {
                    echo "{$model->id} \t {$model->email} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
        print_r($notMapped);
    }

    public function actionCourseMapping()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'courseMapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i = 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            if ($fileop[1] == 'NULL') {
                echo "{$fileop[4]} is not a child \n";
                continue;
            }

            $course = Course::find()->where(['slug' => $fileop[4]])->one();
            if (!$course) {
                echo "{ $fileop[4] } \ not found \n";
                continue;
            }

            if (!empty($fileop[1])) {
                $course->parent_id = (int) $fileop[1];
            }

            if ($course->save()) {
                echo "$course->name \n";
            } else {
                print_r($course->getErrors());
                echo "$course->name  is it self parent\n";
            }
        }

        fclose($handle);
    }

    public function actionCollegeRanking()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_ranking.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = College::find()->where(['id' => $fileop[0]])->one();
            if (!$model) {
                $notMapped['college'][] = "$fileop[0] \t $fileop[2]  \n";
                continue;
            }

            $model->rank = $fileop[3];
            if ($model->save()) {
                echo "{$model->slug} \n";
            } else {
                print_r($model->getErrors());
            }
        }

        print_r($notMapped);
    }

    public function actionCollegePopularity()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college-popularity.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $collegeSlug = explode('/', $fileop[0]);

            $model = College::find()->where(['slug' => $collegeSlug[4]])->one();
            if (!$model) {
                $notMapped['college'][] = "$collegeSlug[4] \t $fileop[1]  \n";
                continue;
            }

            $model->position = $fileop[1];
            $model->is_popular = College::POPULAR_YES;

            if ($model->save()) {
                echo "{$model->slug} \n";
            } else {
                print_r($model->getErrors());
            }
        }

        print_r($notMapped);
    }

    public function actionCollegeCourseSpecializationMapping()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_course_specialization_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            if (empty($fileop[5])) {
                continue;
            }

            $specialization = Specialization::find()->where(['slug' => $fileop[5]])->one();

            if (!$specialization) {
                $notMapped[] = "$fileop[5]";
                continue;
            }

            $course = CollegeCourse::find()->where(['id' => $fileop[0]])->one();

            if (!$course) {
                $notMapped[] = "$fileop[3]";
                continue;
            }

            $course->specialization_id = $specialization->id;

            if ($course->save()) {
                echo "{$course->slug} \n";
            }
        }

        print_r($notMapped);
    }

    public function actionCollegeCourseContent()
    {
        $data = College::find();
        foreach ($data->batch() as $colleges) {
            foreach ($colleges as $college) {
                $query = new Query();
                $query->from('gmu_college_courses_info')
                    ->select(['id', 'college_id', 'educational_qualification', 'concat(notes, scholarship, admission_process, age_limit) as content', 'course_vanity', 'is_deleted'])
                    ->where(['college_id' => $college->old_id]);

                $oldContent = $query->all(Yii::$app->gmudb);
                if (!$oldContent) {
                    continue;
                }

                foreach ($oldContent as $content) {
                    $course = Course::find()->where(['slug' => $content['course_vanity']])->active()->one();

                    if (!$course) {
                        echo $content['course_vanity'] . "\t {$college->slug} not found \n";
                        continue;
                    }

                    $model = CollegeCourseContent::find()
                        ->where(['college_id' => $college->id])
                        ->andWhere(['course_id' => $course->id])
                        ->one();

                    if (!$model) {
                        $model = new CollegeCourseContent();
                    }

                    $model->college_id = $college->id;
                    $model->course_id = $course->id;
                    $model->content = $content['content'] ?? null;
                    $model->qualification = json_decode($content['educational_qualification']) ?? null;
                    $model->status = $content['is_deleted'] == 0 ? CollegeCourseContent::STATUS_ACTIVE : CollegeCourseContent::STATUS_INACTIVE;

                    if ($model->save()) {
                        echo "{$college->name} \t {$course->slug} \n";
                    } else {
                        print_r($model->getErrors());
                    }
                }
            }
        }
    }

    public function actionCollegeProgramContent()
    {
        $data = College::find();
        foreach ($data->batch() as $colleges) {
            foreach ($colleges as $college) {
                $programs = CollegeCourse::find()->where(['college_id' => $college->id])->all();
                foreach ($programs as $program) {
                    $course = Course::find()->where(['id' => $program->course_id])->one();
                    $programId = explode('-', $program->slug);

                    if (!$course) {
                        continue;
                    }

                    if (!$programId) {
                        echo "program id not found for {$course->name}";
                        continue;
                    }

                    $query = new Query();
                    $query->from('gmu_college_courses')
                        ->select(['id', 'gmu_college_id', 'educational_qualification', 'concat(notes, scholarship, admission, eligibility, age_limit) as content', 'is_deleted'])
                        ->where(['gmu_college_id' => $college->old_id])
                        ->andWhere(['id' => end($programId)]);

                    $content = $query->one(Yii::$app->gmudb);

                    if (!$content) {
                        continue;
                    }

                    if (empty($content['educational_qualification']) && empty($content['content'])) {
                        echo "Not Found for {$content['gmu_college_id']} {$content['id']} \n ";
                        continue;
                    }

                    $model = CollegeProgramContent::find()
                        ->where(['college_course_id' => $program->id])
                        ->one();

                    if (!$model) {
                        $model = new CollegeProgramContent();
                    }

                    $model->college_course_id = $program->id;
                    $model->qualification = json_decode($content['educational_qualification'], true) ?? '';
                    $model->content = $content['content'] ?? '';
                    $model->status = ($content['is_deleted'] == 0) ? CollegeProgramContent::STATUS_ACTIVE : CollegeProgramContent::STATUS_INACTIVE;

                    if ($model->save()) {
                        echo "{$program->name} \t {$college->name} \n";
                    } else {
                        $model->getErrors();
                    }
                }
            }
        }
    }

    public function actionBrochure()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_coures_brochure.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = Brochure::find()
                ->where(['entity' => $fileop[0]])
                ->andWhere(['college_id' => $fileop[1]])
                ->andWhere(['course_id' => $fileop[2]])
                ->andWhere(['file_name' => $fileop[3]])
                ->andWhere(['pdf' => $fileop[4]])
                ->one();

            if (!$model) {
                $model = new Brochure();
            }

            $model->entity = $fileop[0] ?? '';
            $model->college_id = $fileop[1] ?? '';
            $model->course_id = $fileop[2] ?? '';
            $model->file_name = $fileop[3] ?? '';
            $model->pdf = $fileop[4] ?? '';
            $model->status = $fileop[5] ?? '';

            if ($model->save()) {
                echo "{$model->entity} \n";
            } else {
                $model->getErrors();
            }
        }

        print_r($notMapped);
    }

    public function actionUpdateCollegeAuthor()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_author_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }


            $college = CollegeContent::find()->where(['entity_id' => $fileop[0]])->all();

            if (!$college) {
                $notMapped['college'] = "$fileop[1] \t Not found \n";
                continue;
            }
            foreach ($college as $value) {
                if (!$value) {
                    $notMapped['value'] = "$fileop[1] \t Not found \n";
                    continue;
                }

                $author = User::find()->where(['slug' => $fileop[3]])->one();

                if (!$author) {
                    $notMapped['author'] = "$fileop[3] \t Not found \n";
                    continue;
                }

                $value->author_id = $author->id;

                if ($value->save()) {
                    echo "{$fileop[1]} \t {$author->slug} \t {$fileop[0]} \n";
                }
            }
        }

        print_r($notMapped);
    }

    /**
     * Courses Importer
     *
     */
    public function actionAllCourses()
    {
        $this->actionNewCourse();
        $this->actionCourseParent();
        $this->actionCourseDegree();
        $this->actionCourseFeatureGroup();
        $this->actionCourseFeatureValue();
        $this->actionCourseContent();
        $this->actionCourseAuthor();
        $this->actionCourseContentCreateAt();
        $this->actionCourseFaq();
    }

    /**
     * Insert courses from csv
     *
     * @return void
     */
    public function actionNewCourse()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_new.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i = 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $course = Course::find()->where(['slug' => $fileop['3']])->one();

            if (!$course) {
                $course = new Course();
            }

            $course->stream_id = (int)$fileop[1];
            $course->name = $fileop['0'];
            $course->slug = $fileop['3'];
            $course->short_name = $fileop['4'];
            $course->status = 1;

            if ($course->save()) {
                echo "$course->name \n";
            } else {
                print_r($fileop);
                var_dump($course->getErrors());
            }
        }

        fclose($handle);
    }

    private function getCourseContent($courseSlug)
    {
        $query = (new Query())->from('gmu_courses_details')->where(['course_vanity' => $courseSlug])->one(Yii::$app->gmudb);
        if (empty($query)) {
            return '';
        }
        return $query;
    }

    public function actionCourseContent()
    {
        $query = Course::find();

        foreach ($query->batch() as $courses) {
            foreach ($courses as $course) {
                $courseContent = CourseContent::find()->where(['course_id' => $course->id])->one();
                if ($courseContent) {
                    continue;
                }

                $content = $this->getCourseContent($course->slug);
                if (empty($content)) {
                    $errors['course'][] = "{$course->slug} course not found.";
                    continue;
                }

                if (!empty($content['about'])) {
                    $this->prepareContent($course, $content, 'about');
                }

                if (!empty($content['subject_syllabus'])) {
                    $this->prepareContent($course, $content, 'subject_syllabus');
                }

                if (!empty($content['jobs_scope_salary'])) {
                    $this->prepareContent($course, $content, 'jobs_scope_salary');
                }
            }
        }

        if (!empty($errors)) {
            print_r($errors);
        }
    }

    private function prepareContent($course, $content, $page)
    {
        $model = new CourseContent();
        $model->course_id = $course->id;
        $model->status = CourseContent::STATUS_ACTIVE;

        $explodeArr = explode('_', $page);
        $atrr = ($explodeArr[0] == 'subject') ? 'syllabus' : ($explodeArr[0] == 'jobs' ? 'job' : 'about');

        $model->h1 = $content[$atrr . '_h1'];
        $model->meta_title = $content[$atrr . '_title'];
        $model->meta_description = $content[$atrr . '_meta_des'];
        if ($page == 'about') {
            $model->top_content = $content['top_content'];
        }

        $model->page = ArrayHelper::getValue(CourseHelper::$fieldName, $page);
        $model->content = $content[$page];

        if (!$model->save()) {
            print_r($model->getErrors());
        } else {
            echo "{$model->page} \t {$course->slug} \n";
        }
    }

    public function actionCourseFaq()
    {
        $data = Course::find();
        foreach ($data->batch() as $courses) {
            foreach ($courses as $course) {
                $oldCourse = GmuCoursesDetails::find()->where(['course_vanity' => $course->slug])->one();

                if (!$oldCourse) {
                    continue;
                }

                $oldFaqs = GmuFaq::find()->where(['source_id' => $oldCourse->id])
                    ->andWhere(['status' => '1'])
                    ->andWhere(['type' => 6])
                    ->all();

                if (!$oldFaqs) {
                    continue;
                }

                $items = [];
                foreach ($oldFaqs as $faq) {
                    $items[] = [
                        'question' => ContentHelper::htmlDecode($faq->question, true),
                        'answer' => ContentHelper::htmlDecode($faq->answer, false)
                    ];
                }

                $faq = Faq::find()
                    ->where(['entity' => Course::ENTITY_COURSE])
                    ->andWhere(['entity_id' => $course->id])
                    ->andWhere(['sub_page' => 'about'])
                    ->one();

                if (!$faq) {
                    $faq = new Faq();
                }

                $faq->entity = Course::ENTITY_COURSE;
                $faq->entity_id = $course->id;
                $faq->page = $course->slug;
                $faq->sub_page = 'about';
                $faq->qnas = json_decode(json_encode($items), false);
                $faq->status = Faq::STATUS_ACTIVE;

                if ($faq->save()) {
                    echo "{$faq->entity} \t $course->name \n";
                } else {
                    print_r($faq->getErrors());
                }
            }
        }
    }

    public function actionCourseFeatureGroup()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_feature_group.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $highlightsGroup = HighlightAttribute::find()->where(['is_key_feature' => $fileop[1]])->one();
            if (!$highlightsGroup) {
                $highlightsGroup = new HighlightAttribute();
            }

            $highlightsGroup->name = $fileop[1];
            $highlightsGroup->is_key_feature = $fileop[2];
            $highlightsGroup->status = HighlightAttribute::STATUS_ACTIVE;

            if ($highlightsGroup->save()) {
                echo "{$highlightsGroup->name} \n";
            } else {
                print_r($highlightsGroup->getErrors());
            }
        }
    }

    public function actionCourseFeatureValue()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_feature_values.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $course = Course::find()->where(['slug' => $fileop[0]])->one();
            if (!$course) {
                continue;
            }
            $group = HighlightAttribute::find()->where(['id' => $fileop[1]])->one();
            if (!$group) {
                continue;
            }
            $highlights = HighlightAttributeValue::find()
                ->where(['is_key_feature_id' => $fileop[1]])
                ->andWhere(['entity_id' => $course->id])
                ->one();

            if (!$highlights) {
                $highlights = new HighlightAttributeValue();
            }

            $highlights->entity = 'course';
            $highlights->entity_id = $course->id;
            $highlights->is_key_feature_id = $fileop[1];
            $highlights->value = $fileop[2];
            $highlights->status = HighlightAttributeValue::STATUS_ACTIVE;

            if ($highlights->save()) {
                echo "{$course->short_name} \t {$group->name} \n";
            } else {
                print_r($highlights->getErrors());
            }
        }
    }

    public function actionCourseDegree()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_degree.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $course = Course::find()->where(['slug' => $fileop[0]])->one();
            if (!$course) {
                $course = new Course();
            }

            $course->degree = $fileop[1];
            if ($course->save()) {
                echo "{$course->name} \n";
            } else {
                print_r($course->getErrors());
            }
        }
    }

    public function actionCourseParent()
    {
        //Set parent_id to Null
        $connection = Yii::$app->db;
        $connection->createCommand()->update('course', ['parent_id' => null])->execute();

        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_parent.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $course = Course::find()->where(['slug' => $fileop[0]])->one();

            if (!$course) {
                echo "{ $fileop[0] } \ not found \n";
                continue;
            }

            if ($fileop[1] == 'NULL') {
                echo "{$fileop[0]} is not a child \n";
                continue;
            }

            $course->parent_id = (int) $fileop[1];

            if ($course->save()) {
                echo "{$course->name} \t {$course->slug} \t {$course->parent_id} \n";
            } else {
                print_r($course->getErrors());
            }
        }
        fclose($handle);
    }

    public function actionCourseAuthor()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_author.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $course =  Course::find()->where(['slug' => $fileop[0]])->one();
            if (!$course) {
                continue;
            }

            $courseContent = CourseContent::find()->where(['course_id' => $course->id])->all();

            if (!$courseContent) {
                continue;
            }

            foreach ($courseContent as $content) {
                $model = CourseContent::find()
                    ->where(['course_id' => $content->course_id])
                    ->andWhere(['page' => $content->page])
                    ->one();

                if (!$model) {
                    continue;
                }

                $model->author_id = $fileop[1];

                if ($model->save()) {
                    echo "{$model->id} \t $model->page \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionCourseContentCreateAt()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_created_at.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $course =  Course::find()->where(['slug' => $fileop[0]])->one();
            if (!$course) {
                continue;
            }

            $courseContent = CourseContent::find()->where(['course_id' => $course->id])->all();

            if (!$courseContent) {
                continue;
            }

            foreach ($courseContent as $content) {
                $model = CourseContent::find()
                    ->where(['course_id' => $content->course_id])
                    ->andWhere(['page' => $content->page])
                    ->one();

                if (!$model) {
                    continue;
                }

                $model->created_at = $fileop[1];

                if ($model->save()) {
                    echo "{$course->slug} \t $model->page \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionStudent()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'student.csv';

        $row = 0;
        $duplicates = 0;
        $validationFailed = 0;

        if (!file_exists($file)) {
            echo "CSV File dosen't exists!" . PHP_EOL;
            echo 'Path: ' . $file . PHP_EOL;
            exit;
        }

        echo 'Import Started.' . PHP_EOL;

        $failedRecords = [];

        if (($handle = fopen($file, 'r')) !== false) {
            while (($data = fgetcsv($handle, 2000, ',')) !== false) {
                $row++;

                if ($row == 1) {
                    continue;
                }

                try {
                    if (!Student::find()->where(['email' => $data[2], 'phone' => $data[3]])->exists()) {
                        $model = new Student;
                        $model->scenario = Student::SCENARIO_IMPORTER;
                        $model->id = $data[0] ?? '';
                        $model->name = $data[1] ?? '';
                        $model->email = $data[2] ?? '';
                        $model->phone = $data[3] ?? '';
                        $model->profile_pic = $data[4] ?? '';

                        if ($model->validate()) {
                            $model->save();
                        } else {
                            $validationFailed += 1;
                            $data = array_filter($model->attributes, function ($value) {
                                return !is_null($value) && $value !== '';
                            });
                            $data['errors'] = $this->getValidationErrorsAsList($model->errors);
                            $failedRecords[] = $data;
                        }
                    } else {
                        $duplicates += 1;
                    }

                    echo 'Processing Row ' . $row . ' from CSV. Total Duplicates Found: ' . $duplicates . "\r";
                } catch (\Exception $e) {
                    print_r($e);
                    exit;
                }
            }

            $this->consoleTable($failedRecords);
            $row--;
            $data = [['Total Records' => $row, 'Inserted Records' => $row - $validationFailed - $duplicates, 'Invalid Records' => $validationFailed, 'Duplicate Records' => $duplicates]];
            $this->consoleTable($data);
            echo 'All done!' . PHP_EOL;

            fclose($handle);
        }
    }

    private function consoleTable($data)
    {

        if (count($data) < 1) {
            return;
        }

        $header = array_keys($data[0]);
        $body = [];
        foreach ($data as $lines) {
            $body[] = array_values($lines);
        }

        $table = new Table();
        echo $table->setHeaders($header)->setRows($body)->run();
        return;
    }

    private function getValidationErrorsAsList($errors)
    {
        $validationErrros = [];
        foreach (array_keys($errors) as $error) {
            $validationErrros = array_merge($validationErrros, array_values($errors[$error]));
        }
        return $validationErrros;
    }

    public function actionUpdateOrCreateAuthorBio()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'Author_Bio.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $userInfo = User::find()->where(['slug' => $fileop[1]])->one();
            if (!$userInfo) {
                $notMapped['userNotFound'][] = $fileop;
                continue;
            }

            $profile = $userInfo->profile;

            if (!($profile instanceof Profile)) {
                $profile = new Profile();
                $profile->user_id = $userInfo->id;
            }

            $profile->about = $fileop[2];

            if ($profile->save()) {
                echo "$userInfo->id \t $userInfo->entity \t $userInfo->entity_id  \t $profile->job_role \n";
            } else {
                $notMapped['unableToSaveProfile'] = ['data' => $fileop, 'errors' => $profile->errors];
            }
        }

        print_r($notMapped);

        fclose($handle);
    }

    public function actionUpdateOldCreatedDate()
    {
        $this->actionUpdateExamCreatedDate();
        $this->actionUpdateArticleCreatedDate();
        $this->actionUpdateBoardCreatedDate();
        $this->actionUpdateBoardContentCreatedDate();
    }
    public function actionUpdateExamCreatedDate()
    {
        $notMapped = [];
        $oldExams = GmuExam::find()->all();

        foreach ($oldExams as $oldExam) {
            $newExam = Exam::find()->where(['id' => $oldExam->id])->one();

            if (empty($newExam)) {
                continue;
            }

            $newExam->created_at = $oldExam->posted_on;

            if ($newExam->save()) {
                $examContents = ExamContent::find()->where(['exam_id' => $newExam->id])->all();

                foreach ($examContents as $examContent) {
                    if (empty($examContent)) {
                        continue;
                    }

                    $examContent->created_at = $oldExam->posted_on;

                    if ($examContent->save()) {
                    } else {
                        $notMapped['missing-exam-slug'][$oldExam->id] = $oldExam->id;
                    }
                }
            } else {
                $notMapped['missing-exam-slug'][$oldExam->id] = $oldExam->id;
            }
        }

        print_r($notMapped);
    }

    public function actionUpdateArticleCreatedDate()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'article_created_date.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $model = Article::find()->where(['slug' => $fileop[0]])->one();

            if (!$model) {
                echo "{$fileop[0]} not found";
                continue;
            }

            $model->created_at = $fileop[1] ?? '';

            if ($model->save()) {
                echo "{$model->id} \t {$model->slug} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateBoardCreatedDate()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'board_created_at.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $model = Board::find()->where(['id' => $fileop[0]])->one();

            if (!$model) {
                echo "{$fileop[0]} not found";
                continue;
            }

            $model->created_at = $fileop[2] ?? '';

            if ($model->save()) {
                echo "{$model->id} \t {$model->slug} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateBoardContentCreatedDate()
    {

        $boards = Board::find()->all();

        foreach ($boards as $board) {
            $query = new Query();
            $query->from('gmu_boards_keyword_tag gbkt')
                ->innerJoin('gmu_exam_boards_section as gebs', 'gbkt.section_id = gebs.id')
                ->where(['gbkt.board_id' => $board->id]);

            $data = $query->all(Yii::$app->gmudb);

            if (!$data) {
                echo "{$board->name} \t content not found \n";
                continue;
            }

            foreach ($data as $value) {
                $model = BoardContent::find()
                    ->where(['board_id' => $board->id])
                    ->andWhere(['page_slug' => $value['section_slug']])
                    ->one();
                if (!$model) {
                    echo 'Content not found';
                    continue;
                }

                $model->created_at = $value['created_on'] ?? '';

                if ($model->save()) {
                    echo "{$board->name} \t $model->page \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionGmuMetaTopContent()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'gmu_category_meta_top_content.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $query = new Query();
            $query->select(['content'])
                ->from('gmu_olympiad_home')
                ->where(['id' => $fileop[0]]);
            $topContent = $query->one(\Yii::$app->gmudb);

            $query = new Query();
            $query->select(['h1', 'title', 'description'])
                ->from('gmu_exam_category_title_customized')
                ->where(['category' => $fileop[2]]);
            $metaData = $query->one(\Yii::$app->gmudb);

            $model = GmuMetaCategory::find()->where(['id' => $fileop[0]])->one();

            if (!$model) {
                $model = new GmuMetaCategory();
            }

            $model->id = $fileop[0];
            $model->category_name = ucfirst(strtolower($fileop[1]));
            $model->slug = $fileop[2];
            $model->parent_id = $fileop[3];
            $model->top_content = $topContent ? $topContent['content'] : '';
            $model->h1 = $metaData['h1'] ?? '';
            $model->title = $metaData['title'] ?? '';
            $model->description = $metaData['description'] ?? '';
            $model->status = $fileop[8];

            if ($model->save()) {
                echo "{$model->slug} \t {$model->category_name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionIncrement()
    {
        \Yii::$app->db->createCommand('set foreign_key_checks=0')->execute();
        $data = Student::find()->all();
        $count = 116064;
        foreach ($data as $model) {
            $exsitingStudent = Student::find()->where(['id' => $model->id])->one();
            $exsitingStudent->scenario = Student::SCENARIO_IMPORTER;
            $exsitingStudent->id = $count;
            if ($exsitingStudent->save()) {
                echo "{$exsitingStudent->id} \t {$model->id} \n";
                $studentActivity = StudentActivity::find()->where(['student_id' => $model->id])->all();
                if (empty($studentActivity)) {
                    echo 'Data Not found';
                    continue;
                }

                foreach ($studentActivity as $studentActivityId) {
                    $studentActivityId->scenario = Student::SCENARIO_IMPORTER;
                    $studentActivityId->student_id = $exsitingStudent->id;

                    if ($studentActivityId->save()) {
                        echo "{$exsitingStudent->id} \t Student ID Updated SA : {$studentActivityId->student_id} \t {$model->id} \n";

                        $studentCollegeShortlists = StudentCollegeShortlist::find()->where(['student_id' => $model->id])->all();

                        if (empty($studentCollegeShortlists)) {
                            continue;
                        }

                        foreach ($studentCollegeShortlists as $studentCollegeShortlist) {
                            $studentCollegeShortlist->scenario = Student::SCENARIO_IMPORTER;
                            $studentCollegeShortlist->student_id = $exsitingStudent->id;

                            if ($studentCollegeShortlist->save()) {
                                echo "{$exsitingStudent->id} \t Student ID Updated for SCS : {$studentCollegeShortlist->student_id} \t {$model->id} \n";
                            } else {
                                print_r($studentCollegeShortlist->getErrors());
                            }
                        }
                    } else {
                        print_r($studentActivityId->getErrors());
                    }
                }
            } else {
                print_r($model->getErrors());
            }
            $count++;
        }
    }

    //updating the course parent id to college_course table
    public function actionUpdateCollegeCourseParentId()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_course_parent_course_map.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $model = CollegeCourse::find()->where(['id' => $fileop[0]])->one();
            if (!$model) {
                $model = new CollegeCourse();
            }

            $model->course_id = $fileop[3];
            $model->college_id = $fileop[1];

            if ($model->save()) {
                echo "{$model->name} \t {$model->id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    //update course, stream and exam table for lead form
    public function actionUpdateTables()
    {
        $this->actionDegree();
        $this->actionCourseHighestQualificationDistanceMapping();
        $this->actionLmsStreamMapping();
        $this->actionExamPrimaryCourseMapping();
        $this->actionUpdateStudentTable();
        $this->actionUpdateStudentPreferenceTable();
    }

    public function actionCourseHighestQualificationDistanceMapping()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_highest_qualification_distance_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $model = Course::find()->where(['id' => $fileop[0]])->one();

            if (!$model) {
                $notMapped['course'][] = $fileop[0];
                continue;
            }

            $model->scenario = Course::SCENARIO_IMPORTER;
            $model->degree = $fileop[1];
            $model->highest_qualification = DataHelper::$highestQualificationIdMapping[$fileop[2]];
            $model->is_distance_course = $fileop[3];

            if ($model->save()) {
                echo "{$model->degree} \t {$model->highest_qualification} \t {$model->is_distance_course} \n";
            } else {
                print_r($model->getErrors());
            }
        }
        print_r($notMapped);
    }

    public function actionLmsStreamMapping()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'lms_stream_slug_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = Stream::find()->where(['id' => $fileop[0]])->one();

            if (!$model) {
                $notMapped['stream'][] = $fileop[0];
                continue;
            }

            $model->scenario = Stream::SCENARIO_IMPORTER;
            $model->lms_stream = $fileop[2];

            if ($model->save()) {
                echo "{$model->id} \t {$model->name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
        print_r($notMapped);
    }

    public function actionExamPrimaryCourseMapping()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam_primary_course_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $notMapped = [];
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = Exam::find()->where(['id' => $fileop[0]])->one();

            if (!$model) {
                $notMapped['exam'][] = $fileop[0];
                continue;
            }

            $model->primary_course_id = $fileop[4];

            if ($model->save()) {
                echo "{$model->id} \t {$model->name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
        print_r($notMapped);
    }

    public function actionDegree()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'degree.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = Degree::find()->where(['id' => $fileop[0]])->one();
            if (!$model) {
                $model = new Degree();
            }

            $model->id = $fileop[0];
            $model->name = $fileop[1];
            $model->slug = $fileop[2];
            $model->display_name = $fileop[3];
            $model->status = $fileop[4];

            if ($model->save()) {
                echo "{$model->id} \t {$model->name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateStudentTable()
    {
        $students = Student::find();

        foreach ($students->batch() as $student) {
            foreach ($student as $value) {
                if (empty($value) || empty($value->current_city) || is_numeric($value->current_city)) {
                    continue;
                }
                $cityId = City::find()->select(['id', 'state_id'])->where(['slug' => $value->current_city])->one();

                $value->scenario = Student::SCENARIO_IMPORTER;
                $value->current_city = !empty($cityId) ? $cityId['id'] : $value->current_city;
                $value->current_state = !empty($cityId) ? $cityId['state_id'] : $value->current_state;

                try {
                    if ($value->save()) {
                        echo "{$value->id} \t {$value->name} \n";
                    } else {
                        print_r(($value->getErrors()));
                    }
                } catch (\Exception $e) {
                    echo $e->getMessage();
                }
            }
        }
    }

    public function actionUpdateStudentPreferenceTable()
    {
        Yii::$app->db->createCommand('SET foreign_key_checks = 0;')->execute();
        $studentsPreferences = StudentPreference::find();

        foreach ($studentsPreferences->batch() as $studentsPreference) {
            foreach ($studentsPreference as $value) {
                if (empty($value)) {
                    continue;
                }

                if ($value['stream'] == 'design' || $value['stream'] == 'fashion-design') {
                    $streamSlug = 'fashion';
                } else if ($value['stream'] == 'animation-multimedia') {
                    $streamSlug = 'media-films';
                } else if ($value['stream'] == 'distance-learning-mba') {
                    $streamSlug = 'management-bba-mba';
                } else {
                    $streamSlug = $value['stream'];
                }

                $streamId = Stream::find()->select(['id'])->where(['lms_stream' => $streamSlug])->one();
                $courseId = Course::find()->select(['id'])->where(['slug' => $value['course']])->one();
                $degreeID = Degree::find()->select(['id'])->where(['slug' => $value['degree']])->one();

                $value->scenario = StudentPreference::SCENARIO_IMPORTER;
                $value->student_id = $value->studentActivity->student_id ?? null;
                $value->stream = empty($streamId) ? $streamSlug : $streamId['id'];
                $value->course = empty($courseId) ? $value['course'] : $courseId['id'];
                $value->degree = empty($degreeID) ? $value['degree'] : $degreeID['id'];

                try {
                    if ($value->save()) {
                        echo "{$value->id} \t {$value->stream} \t {$value->course} \n";
                    } else {
                        print_r(($value->getErrors()));
                    }
                } catch (\Exception $e) {
                    echo $e->getMessage();
                }
            }
        }
        Yii::$app->db->createCommand('SET foreign_key_checks = 1;')->execute();
    }

    public function actionAddNewSpecialization()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'specialization-new.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }


        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $model = Specialization::find()->where(['slug' => $fileop[1]])->one();

            if (!$model) {
                $model = new Specialization();
            }

            $model->name = $fileop[0];
            $model->slug = $fileop[1];
            $model->display_name = $fileop[0];
            $model->status = Specialization::STATUS_ACTIVE;


            if ($model->save()) {
                echo "{$model->name} \t {$model->id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionNewSpecializationMappWithOld()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'specilization_old_with_new_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }


        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $oldModel = SpecializationOld::find()->where(['slug' => $fileop[0]])->one();
            $newModel = Specialization::find()->where(['slug' => $fileop[1]])->one();

            if (!$newModel) {
                continue;
            }

            if (!$oldModel) {
                $oldModel = new SpecializationOld();
            }

            $oldModel->specialization_id_new = $newModel->id;

            if ($oldModel->save()) {
                echo "{$oldModel->name} \t {$oldModel->id} \n";
            } else {
                print_r($oldModel->getErrors());
            }
        }
    }

    public function actionCreateDyanamicRoutes()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_course_dynamic_routes.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }


        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $model = CollegeCourse::find()->where(['slug' => $fileop[1]])->andWhere(['college_id' => $fileop[0]])->one();

            $course =  Course::find()->where(['slug' => $fileop[2]])->one();
            if (!$course) {
                continue;
            }

            if (!empty($fileop[3])) {
                $specialization = Specialization::find()->where(['slug' => $fileop[3]])->one();
            }

            if (!$model) {
                $model = new CollegeCourse();
            }

            $model->college_id = $fileop[0];
            $model->course_id = $course->id;
            if (!empty($fileop[3])) {
                $model->specialization_id = $specialization->id ?? '';
                if ($fileop[4] == 'hons') {
                    $model->is_hons = 1;
                }
            }

            if ($fileop[4] == 'hons') {
                $model->is_hons = 1;
            } else {
                $model->is_hons = null;
            }

            if ($model->save()) {
                echo "{$model->slug} \t {$model->id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUserUpdate()
    {
        $arrTables = [
            'article',
            'board_content',
            'board_sample_paper',
            'college_content',
            'course_content',
            'exam_content',
            'news_content',
            'stream_content'
        ];
        $old_authors = [
            'yamini-sharma' => 'ayesha-taneem',
            'vanshika' => 'somya-jain',
            'v.-nandini-deshmukh' => 'ishani-mishra',
            'tejasri-nune' => 'priya-krishna',
            'surobhi-c' => 'kripal-t',
            'sumridhi-gulati' => 'ravika-gaja',
            'subrata-jena' => 'shreyeshri-mondal',
            'sriya-ganesh' => 'pratiki-patra',
            'siraam' => 'qureshi-hafsha-mohd-shakeel',
            'simran-chanda' => 'nivedha',
            'shubham-aggarwal' => 'dinesh-reddy',
            'shriya-pandey' => 'parvathy-krishnakumar',
            'sandhya-p' => 'saniya-qureshi',
            'sampreeth-kiran' => 'janki-sharma',
            'saloni-pareek' => 'debasree-banerjee',
            'sakshi-s.-harne' => 'rajalakshmi-bk',
            'riya-goyal' => 'janki-sharma',
            'ravi-kumar' => 'janki-sharma',
            'rahil-ahmed' => 'roumik-roy',
            'rahil' => 'roumik-roy',
            'prasheeth' => 'somya-jain',
            'prashanna-raju' => 'somya-jain',
            'pranav' => 'somya-jain',
            'prachi-arora' => 'somya-jain',
            'pawan-kumar' => 'somya-jain',
            'nitin-yaduvanshi' => 'somya-jain',
            'nishita-shetty' => 'sumitra-saha',
            'monica-rajguru' => 'qureshi-hafsha-mohd-shakeel',
            'monalisha' => 'qureshi-hafsha-mohd-shakeel',
            'monalisa-roy' => 'qureshi-hafsha-mohd-shakeel',
            'mohsin-khan' => 'saniya-qureshi',
            'minakshi-singh' => 'saniya-qureshi',
            'meghna-lama' => 'saniya-qureshi',
            'mayur-waghmare' => 'saniya-qureshi',
            'manvi-sahni' => 'saniya-qureshi',
            'jiwan' => 'saniya-qureshi',
            'janani-padmanabhan' => 'selva-kumar',
            'iswarya-thiyagarajan' => 'ayesha-taneem',
            'ilika-tewari' => 'selva-kumar',
            'divy-gupta' => 'ayesha-taneem',
            'arunima-purohit' => 'ayesha-taneem',
            'ankita-sanghvi' => 'selva-kumar',
            'ankita-s' => 'ayesha-taneem',
            'ankita-paul' => 'ayesha-taneem',
            'amrutha-n' => 'ayesha-taneem',
            'akriti-rastogi' => 'selva-kumar',
            'akansha-agarwal' => 'ayesha-taneem',
            'abhinav-kumar' => 'ayesha-taneem',
            'abdul-taiyeb' => 'ayesha-taneem',
            'abbaz' => 'selva-kumar',
            'aakarshika' => 'ayesha-taneem'
        ];
        foreach ($arrTables as $value) {
            foreach ($old_authors as $old => $new) {
                $query = new Query;
                $oldId = $query->select(['id'])
                    ->from('user')
                    ->where(['username' => $old])
                    ->one();
                $newId = $query->select(['id'])
                    ->from('user')
                    ->where(['username' => $new])
                    ->one();
                $connect = Yii::$app->getDb();
                $command = $connect->createCommand()
                    ->update($value, ['author_id' => $newId['id']], ['author_id' => $oldId['id']])
                    ->execute();
            }
        }
        return 'success';
    }

    public function actionUpdateEmptyValuesStudentPreference()
    {
        $this->actionUpdateDegreeEmptyValues();
        $this->actionUpdateExamEmptyValues();
        $this->actionUpdateStreamEmptyValues();
        $this->actionUpdateCourseEmptyValues();
        $this->actionUpdateSpecializationEmptyValues();
        $this->actionUpdateCityEmptyValues();
        $this->actionUpdateStateEmptyValues();
    }
    public function actionUpdateDegreeEmptyValues()
    {
        $studentPreferences = StudentPreference::find()->select(['degree', 'id'])->where(['degree' => ''])->all();

        foreach ($studentPreferences as $studentPreference) {
            if (empty($studentPreference)) {
                continue;
            }

            $studentPreference['scenario'] = StudentPreference::SCENARIO_IMPORTER;
            $studentPreference['degree'] = null;

            if ($studentPreference->save()) {
                echo "{$studentPreference['id']} \n";
            } else {
                print_r(($studentPreference->getErrors()));
            }
        }
    }

    public function actionUpdateExamEmptyValues()
    {
        $studentPreferences = StudentPreference::find()->select(['exam', 'id'])->where(['exam' => ''])->all();

        foreach ($studentPreferences as $studentPreference) {
            if (empty($studentPreference)) {
                continue;
            }

            $studentPreference['scenario'] = StudentPreference::SCENARIO_IMPORTER;
            $studentPreference['exam'] = null;

            if ($studentPreference->save()) {
                echo "{$studentPreference['id']} \n";
            } else {
                print_r(($studentPreference->getErrors()));
            }
        }
    }

    public function actionUpdateStreamEmptyValues()
    {
        $studentPreferences = StudentPreference::find()->select(['stream', 'id'])->where(['stream' => ''])->all();

        foreach ($studentPreferences as $studentPreference) {
            if (empty($studentPreference)) {
                continue;
            }

            $studentPreference['scenario'] = StudentPreference::SCENARIO_IMPORTER;
            $studentPreference['stream'] = null;

            if ($studentPreference->save()) {
                echo "{$studentPreference['id']} \n";
            } else {
                print_r(($studentPreference->getErrors()));
            }
        }
    }

    public function actionUpdateCourseEmptyValues()
    {
        $studentPreferences = StudentPreference::find()->select(['course', 'id'])->where(['course' => ''])->all();

        foreach ($studentPreferences as $studentPreference) {
            if (empty($studentPreference)) {
                continue;
            }

            $studentPreference['scenario'] = StudentPreference::SCENARIO_IMPORTER;
            $studentPreference['course'] = null;

            if ($studentPreference->save()) {
                echo "{$studentPreference['id']} \n";
            } else {
                print_r(($studentPreference->getErrors()));
            }
        }
    }

    public function actionUpdateSpecializationEmptyValues()
    {
        $studentPreferences = StudentPreference::find()->select(['specialization', 'id'])->where(['specialization' => ''])->orWhere(['specialization' => 0])->all();

        foreach ($studentPreferences as $studentPreference) {
            if (empty($studentPreference)) {
                continue;
            }

            $studentPreference['scenario'] = StudentPreference::SCENARIO_IMPORTER;
            $studentPreference['specialization'] = null;

            if ($studentPreference->save()) {
                echo "{$studentPreference['id']} \n";
            } else {
                print_r(($studentPreference->getErrors()));
            }
        }
    }

    public function actionUpdateCityEmptyValues()
    {
        $studentPreferences = StudentPreference::find()->select(['interested_city', 'id'])->where(['interested_city' => ''])->orWhere(['interested_city' => 0])->all();

        foreach ($studentPreferences as $studentPreference) {
            if (empty($studentPreference)) {
                continue;
            }

            $studentPreference['scenario'] = StudentPreference::SCENARIO_IMPORTER;
            $studentPreference['interested_city'] = null;

            if ($studentPreference->save()) {
                echo "{$studentPreference['id']} \n";
            } else {
                print_r(($studentPreference->getErrors()));
            }
        }
    }

    public function actionUpdateStateEmptyValues()
    {
        $studentPreferences = StudentPreference::find()->select(['interested_state', 'id'])->where(['interested_state' => ''])->orWhere(['interested_state' => 0])->all();

        foreach ($studentPreferences as $studentPreference) {
            if (empty($studentPreference)) {
                continue;
            }

            $studentPreference['scenario'] = StudentPreference::SCENARIO_IMPORTER;
            $studentPreference['interested_state'] = null;

            if ($studentPreference->save()) {
                echo "{$studentPreference['id']} \n";
            } else {
                print_r(($studentPreference->getErrors()));
            }
        }
    }

    //import bucket table
    public function actionImportBucket()
    {
        $this->actionImportBucketTable();
        $this->actionImportBucketTaggingProductPage();
        $this->actionImportBucketTaggingNewsArticlePage();
    }

    public function actionImportBucketTable()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'bucket.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $model = LeadBucket::find()->where(['id' => $fileop[0]])->one();

            if (!$model) {
                $model = new LeadBucket();
            }

            $model->scenario = LeadBucket::SCENARIO_IMPORTER;
            $model->id = $fileop[0];
            $model->template_id = $fileop[1] == 'NULL' ? null : $fileop[1];
            $model->entity_id = empty($fileop[2]) ? null : $fileop[2];
            $model->bucket = $fileop[3] == 'NULL' ? null : $fileop[3];
            $model->cta_title = $fileop[4] == '' ? null : $fileop[4];
            $model->cta_description = $fileop[5] == '' ? null : $fileop[5];
            $model->lead_form_title = $fileop[6] == 'NULL' ? null : $fileop[6];
            $model->lead_form_description = $fileop[7] == 'NULL' ? null : $fileop[7];
            $model->cta_text = $fileop[8] == 'NULL' ? null : $fileop[8];
            $model->page_event = $fileop[9] == 'Rediect' ? LeadBucketCta::LEAD_PAGE_REDIRECT : LeadBucketCta::LEAD_PAGE_POP_UP;
            $model->page_link = $fileop[10] == '' ? null : $fileop[10];
            $model->status = LeadBucket::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->id} \t {$model->entity_id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionImportBucketTaggingProductPage()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'bucket_tagging.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $model = LeadBucketTagging::find()->where(['entity' => $fileop[1]])->andWhere(['cta_position' => $fileop[4]])->one();

            if (!$model) {
                $model = new LeadBucketTagging();
            }

            $model->scenario = LeadBucketTagging::SCENARIO_IMPORTER;
            $model->entity = $fileop[1] == '' ? null : $fileop[1];
            $model->sub_page = empty($fileop[2]) ? null : $fileop[2];
            $model->bucket_id = $fileop[3] == '' ? null : $fileop[3];
            $model->cta_position = $fileop[4] == '' ? null : $fileop[4];
            $model->web = $fileop[5] == '' ? null : $fileop[5];
            $model->wap = $fileop[6] == '' ? null : $fileop[6];
            $model->status = LeadBucketTagging::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->entity} \t {$model->cta_position} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionImportBucketTaggingNewsArticlePage()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'news_article_bucket_tagging.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            if (empty($fileop[4])) {
                continue;
            }

            $model = LeadBucketTagging::find()->where(['entity_id' => $fileop[3]])->andWhere(['cta_position' => $fileop[5]])->one();

            if (!$model) {
                $model = new LeadBucketTagging();
            }

            $model->scenario = LeadBucketTagging::SCENARIO_IMPORTER;
            $model->entity = $fileop[1] == '' ? null : $fileop[1];
            $model->entity_id = empty($fileop[3])  ? null : $fileop[3];
            $model->bucket_id = $fileop[7] == '' ? null : $fileop[7];
            $model->cta_position = $fileop[4] == '' ? null : $fileop[4];
            $model->web = $fileop[5] == '' ? null : $fileop[5];
            $model->wap = $fileop[6] == '' ? null : $fileop[6];
            $model->status = LeadBucketTagging::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->entity} \t {$model->entity_id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    //change old to new specialization id on student preference
    public function actionUpdateSpecializationValuesSPTable()
    {
        $studentPreferences = StudentPreference::find();

        foreach ($studentPreferences->batch() as $studentPreference) {
            foreach ($studentPreference as $model) {
                if (empty($model)) {
                    echo "{$model->id}";
                    continue;
                }

                if (empty($model->specialization)) {
                    echo "{$model->id} Specialization is empty \n";
                    continue;
                }
                $specialization = SpecializationOld::find()->select(['specialization_id_new'])->where(['id' => $model->specialization])->one();

                $model->scenario = StudentPreference::SCENARIO_IMPORTER;
                $model->specialization = $specialization['specialization_id_new'] ?? null;
                if ($model->save()) {
                    echo "{$model->specialization} \t {$specialization->specialization_id_new} \t {$specialization->id} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    //change old to new specialization id on student preference
    public function actionUpdateSpecializationValuesLeadTable()
    {
        $leads = Lead::find();

        foreach ($leads->batch() as $lead) {
            foreach ($lead as $model) {
                if (empty($model)) {
                    echo "{$model->id}";
                    continue;
                }

                if (empty($model->specialization_id)) {
                    echo "{$model->id} Specialization is empty \n";
                    continue;
                }
                $specialization = SpecializationOld::find()->select(['specialization_id_new'])->where(['id' => $model->specialization_id])->one();

                $model->scenario = Lead::SCENARIO_IMPORTER;
                $model->specialization_id = $specialization['specialization_id_new'] ?? null;
                if ($model->save()) {
                    echo "{$model->specialization_id} \t {$specialization->specialization_id_new} \t {$specialization->id} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionCompanyList()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'companylist.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            if (empty($fileop[0])) {
                continue;
            }

            $model = Company::find()->where(['name' => $fileop[0]])->one();

            if (!$model) {
                $model = new Company();
            }

            $model->name = $fileop[0];
            $model->status = Company::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->id} \t {$model->name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateProgramQualification()
    {
        $data = [];
        $query = CollegeProgramContent::find();
        foreach ($query->batch() as $contents) {
            foreach ($contents as $content) {
                $model = CollegeProgramContent::find()->where(['id' => $content->id])->one();
                if (json_decode($model->qualification) == '') {
                    continue;
                }

                $arr = json_decode($model->qualification);
                $data['eligibility'] = $arr;
                $model->qualification = json_encode($data);
                if ($model->save()) {
                    echo "{$model->id}\n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionUpdateProgramMedianSalary()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'program_median_salary.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            if (empty($fileop[0])) {
                continue;
            }

            $college = College::find()->select('id')->where(['slug' => $fileop[0]])->one();
            if (!$college) {
                continue;
            }

            $course = Course::find()->select('id')->where(['slug' => $fileop[1]])->one();
            if (!$course) {
                continue;
            }

            $model = CollegeCourse::find()->where(['college_id' => $college->id])->andWhere(['course_id' => $course->id])->all();

            if (!empty($model)) {
                $response = CollegeCourse::updateAll(['salary' => $fileop[2]], 'college_id =' . $college->id . ' and course_id =' . $course->id);
                if ($response) {
                    echo "{$course->id} \t {$course->name} \n";
                } else {
                    print_r($response);
                }
            }
        }
    }

    public function actionUpdateCompanyList()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_company.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            if (empty($fileop[0])) {
                continue;
            }

            $college = College::find()->where(['slug' => $fileop[0]])->one();
            if (!$college) {
                continue;
            }

            $course = Course::find()->where(['slug' => $fileop[2]])->one();
            if (!$course) {
                continue;
            }

            $company = Company::find()->where(['name' => $fileop[1]])->one();
            if (!$company) {
                continue;
            }

            $model = CollegeCourseCompany::find()
                ->where(['college_id' => $college->id])
                ->andWhere(['course_id' => $course->id])
                ->andWhere(['company_id' => $company->id])
                ->one();

            if (!$model) {
                $model = new CollegeCourseCompany();
            }

            $model->college_id = (int)$college->id;
            $model->course_id = (int)$course->id;
            $model->company_id = (int)$company->id;

            if ($model->save()) {
                echo "{$model->company_id}  \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateOneTimePayment()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'one_time_payment.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            if (!empty($fileop[4])) {
                $stream = Stream::find()->where(['slug' => $fileop[4]])->one();
            }

            $model = Course::find()->where(['slug' => $fileop[2]])->one();

            if (!$model) {
                continue;
            }

            if (!empty($fileop[3])) {
                if ($fileop[3] == $model->slug) {
                    continue;
                }
                $parent = Course::find()->where(['slug' => $fileop[3]])->one();
            }

            $model->stream_id = !empty($stream) ? $stream->id : '';
            $model->parent_id = !empty($parent) ? $parent->id : null;


            $model->course_id = $model->id;
            $model->eligibility_title =  $fileop[1];
            $model->eligibility_description =  $fileop[2];
            if ($model->save()) {
                echo "{$model->eligibility_title} \t {$model->course->name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateExamType()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam_type.csv';

        $examTypeArr = ArrayHelper::map(ExamType::find()->all(), 'name', 'id');

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            if (empty($fileop[0])) {
                continue;
            }

            $model = Exam::find()->where(['slug' => $fileop[0]])->one();

            if (!$model) {
                continue;
            }

            $model->name = $model->name;
            $model->slug =  $model->slug;
            $model->exam_type_id =  $examTypeArr[$fileop[1]] ?? '';

            if ($model->save()) {
                echo "{$model->name} \t {$model->exam_type_id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateTuitionFees()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_tuition_fee.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            if (empty($fileop[0])) {
                continue;
            }

            $modelFee = CollegeFees::find()->where(['id' => $fileop[0]])->one();

            if (!$modelFee) {
                continue;
            }
            $modelFee->type =  'tuition_fees';
            $modelFee->status =  $modelFee->status;

            if ($modelFee->save()) {
                echo "{$modelFee->id} \t {$modelFee->type} \n";
            } else {
                print_r($modelFee->getErrors());
            }
        }
    }

    public function actionUpdateProgramWithNewSlug()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'program_with_slug.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            if (empty($fileop[0])) {
                continue;
            }

            $model = CollegeCourse::find()
                ->where(['id' => $fileop[0]])
                ->andWhere(['college_id' => $fileop[1]])->one();

            if (!$model) {
                continue;
            }
            $model->college_id =  $model->college_id;
            $model->course_id =  $model->course_id;
            $model->name =  $fileop[2];
            $model->slug =  Inflector::slug($fileop[2], '-');
            $model->status = CollegeCourse::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->id} \t {$model->name} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionCreateProgram()
    {
        // $typeArr = array_flip(CollegeHelper::$type);
        $modeArr = array_flip(DataHelper::$modeArr);
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'new_program.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = Program::find()->where(['slug' => $fileop[0]])->one();

            if (!$model) {
                continue;
            }

            $model->name = $fileop[0];
            $model->slug = $fileop[1];
            $model->is_hons = (int)$fileop[4];
            $model->is_lateral = (int)$fileop[5];
            if ($fileop[6] == 'Full time') {
                $model->type = 'full_time';
            } else {
                $model->type = 'part_time';
            }
            $model->mode = $modeArr[$fileop[7]];
            $model->status = SpecializationNew::STATUS_ACTIVE;
            $model->created_by = $model->updated_by = 1;
            if ($model->save()) {
                echo "{$model->slug} \t {$model->id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionCollegeCourseExamToCollegeProgramExam()
    {
        $query = CollegeCourseExam::find();
        foreach ($query->batch() as $exams) {
            foreach ($exams as $exam) {
                $model = CollegeProgramExam::find()
                    ->where(['exam_id' => $exam->exam_id])
                    ->andWhere(['college_program_id' => $exam->college_course_id])
                    ->one();

                if (!$model) {
                    $model = new CollegeProgramExam();
                }

                $model->exam_id = $exam->exam_id;
                $model->college_program_id = $exam->college_course_id;

                if ($model->save()) {
                    echo "{$model->exam->name} \t $model->exam_id \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionCollegeFeesToCollegeProgramFee()
    {
        $query = CollegeFees::find();
        foreach ($query->batch() as $exams) {
            foreach ($exams as $exam) {
                $model = CollegeProgramFees::find()
                    ->where(['college_program_id' => $exam->college_course_id])
                    ->andWhere(['id' => $exam->id])
                    ->one();

                if (!$model) {
                    $model = new CollegeProgramFees();
                }

                $model->college_program_id = $exam->college_course_id;
                $model->type = $exam->type;
                $model->fees = $exam->fees;
                $model->duration = $exam->duration;
                $model->duration_type = $exam->duration_type;
                $model->status = $exam->status;
                $model->fees_content = $exam->fees_content;

                if ($model->save()) {
                    echo "{$model->college_program_id} \t $model->id \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionUpdateProgramDetails()
    {
        $typeArr = array_flip(CollegeHelper::$programDurationType);

        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'new_college_program.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $college = College::find()->where(['slug' => $fileop[0]])->one();
            if (!$college) {
                continue;
            }

            $course = Course::find()->where(['slug' => $fileop[2]])->one();
            if (!$course) {
                continue;
            }

            $program = Program::find()->where(['slug' => $fileop[1]])->one();
            if (!$program) {
                continue;
            }

            $collegeProgram = CollegeProgram::find()
                ->where(['program_id' => $program->id])
                ->andWhere(['college_id' => $college->id])
                ->andWhere(['course_id' => $course->id])
                ->one();

            if (empty($collegeProgram)) {
                $collegeProgram = new CollegeProgram();
            }

            $collegeProgram->college_id = $college->id;
            $collegeProgram->program_id = $program->id;
            $collegeProgram->course_id = $course->id;
            $collegeProgram->duration = $fileop[3];
            if (!empty($fileop[3])) {
                $collegeProgram->duration = $fileop[3];
            } else {
                $collegeProgram->duration = null;
            }
            if (!empty($fileop[4])) {
                $collegeProgram->duration_type = $typeArr[$fileop[4]];
            } else {
                $collegeProgram->duration_type = null;
            }
            $collegeProgram->status = 1;
            $collegeProgram->salary = $fileop[5];
            $collegeProgram->seat = $fileop[6];
            $collegeProgram->created_by = $collegeProgram->updated_by = 1;

            if ($collegeProgram->save()) {
                echo "{$collegeProgram->college->slug} \t {$collegeProgram->program->slug} \n";
            } else {
                print_r($collegeProgram->getErrors());
            }
        }
    }

    /** filter data */
    public function actionSpecialization()
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Specialization'])->one();
        if (!$filterGroup) {
            echo 'filter group specialization does not exist';
            return;
        }

        $query = CollegeProgram::find();

        foreach ($query->batch() as $programs) {
            foreach ($programs as $program) {
                $collegeProgram = ProgramCourseMapping::find()->where(['program_id' => $program->program_id])->one();

                if (empty($collegeProgram)) {
                    continue;
                }

                if ($collegeProgram->specialization_id == null) {
                    continue;
                }

                if (!$collegeProgram->course->stream) {
                    continue;
                }

                $parent = Filter::find()->where(['slug' => $collegeProgram->course->stream->slug])->one();
                if (!$parent) {
                    continue;
                }

                if ($collegeProgram->program->is_hons == 1) {
                    if (str_contains($collegeProgram->course->slug, '-hons')) {
                        $arr = explode('-', $collegeProgram->course->slug);
                        $filterSlug = $arr[0] . '-' . $collegeProgram->specialization->slug . '-hons';
                        $filterName = $collegeProgram->course->short_name . ' ' . $collegeProgram->specialization->display_name . ' Hons';
                    } else {
                        $filterSlug = $collegeProgram->course->slug . '-' . $collegeProgram->specialization->slug . '-hons';
                        $filterName = $collegeProgram->course->short_name . ' ' . $collegeProgram->specialization->display_name . ' Hons';
                    }
                } else {
                    $filterSlug = $collegeProgram->course->slug . '-' . $collegeProgram->specialization->slug;
                    $filterName = $collegeProgram->course->short_name . ' ' . $collegeProgram->specialization->display_name;
                }

                $model = Filter::find()->where(['slug' => $filterSlug])->one();
                if (!$model) {
                    $model = new Filter();
                }

                $model->filter_group_id = $filterGroup->id;
                $model->name = $filterName;
                $model->slug = $filterSlug;
                $model->parent_id = $parent->id;
                $model->type = 'Checkbox';
                $model->status = Filter::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "{$model->id} \t {$model->name} \t {$collegeProgram->program->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionImportCourseSpecialization()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_specialization_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = Course::find()->where(['id' => $fileop[0]])->one();

            if (!$model) {
                continue;
            }

            $model->scenario = Student::SCENARIO_IMPORTER;
            $model->specialization_id = empty($fileop[6]) ? null : $fileop[6];
            $model->course_default_list = $fileop[4];
            $model->is_distance_course = $fileop[5];

            if ($model->save()) {
                echo "{$model->slug} \t {$model->id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }



    /* Function  for add CTA for detail subpage on banner  for exam,board and course */

    public function actionSaveLeadTagging()
    {

        $entityArray = [
            5 => 'exam',
            2 => 'boards',
            3 => 'courses'
        ];
        foreach ($entityArray as $key => $entity) {
            $entity_id =  $key;
            $model = new LeadBucket();
            $model->entity_id = $key;
            $model->template_id = 2;
            $model->bucket = 'Apply Now Banner';
            $model->cta_title = null;
            $model->cta_description = null;
            $model->lead_form_title = 'Register Now To Apply';
            $model->lead_form_description = 'Get latest updates and access to premium content';
            $model->cta_text = 'Apply Now {list}';
            $model->page_event = 0;
            $model->page_link = null;
            $model->status = 1;
            if ($model->save()) {
                echo "{$model->id} {$entityArray[$entity_id]} is save \n";
                $entitySubPage = LeadBucketTagging::find()->select(['sub_page'])->distinct()->where(['=', 'entity', $entity_id])->all();
                $bucket_id = $model->id;
                foreach ($entitySubPage as $page) {
                    $arrays[$entityArray[$entity_id]][] =  $page->sub_page;
                    $totalCount = LeadBucketTagging::find()
                        ->where(['=', 'sub_page',  $page->sub_page])
                        ->andWhere(['=', 'entity',  $entity_id])
                        ->count();
                    $pageNAme = str_replace('-', '_', $page->sub_page);
                    $modelLeadBucket = new LeadBucketTagging();
                    $modelLeadBucket->entity = $entity_id;
                    $modelLeadBucket->sub_page = $page->sub_page;
                    $modelLeadBucket->bucket_id = $bucket_id;
                    $modelLeadBucket->cta_position = $entityArray[$entity_id] . '_' . $pageNAme . '_' . ($totalCount + 1);
                    $modelLeadBucket->web = $entityArray[$entity_id] . '_' . $pageNAme . '_web_top_banner_cta' . ($totalCount + 1);
                    $modelLeadBucket->wap = $entityArray[$entity_id] . '_' . $pageNAme . '_wap_top_banner_cta' . ($totalCount + 1);
                    $modelLeadBucket->status = LeadBucketTagging::STATUS_ACTIVE;
                    $modelLeadBucket->save();
                    echo "{$page->sub_page} {$entityArray[$entity_id]} is save \n";
                }
            }
        }
        echo '<pre>';
        print_r($arrays);
        die;
    }

    /* Import Fees of courses*/

    public function actionCourseAvgFees()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'avg_fees.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 1000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            $courseDetail = Course::find()->select(['id'])->where(['slug' => $fileop[0]])->one();
            if (empty($courseDetail)) {
                echo 'Course not exits:' . $fileop[0] . PHP_EOL;
                continue;
            }
            $heiglightAttr = HighlightAttribute::find()->select(['id'])->where(['is_key_feature' => 'avg_fees'])->one();
            $getAttrValueData = HighlightAttributeValue::find()
                ->select(['value', 'id'])
                ->where(['entity' => 'course'])
                ->andWhere(['entity_id' => $courseDetail->id])
                ->andWhere(['is_key_feature_id' => $heiglightAttr->id])
                ->one();
            if (!empty($getAttrValueData)) {
                \Yii::$app->db->createCommand('UPDATE highlight_attribute_value SET value=:value WHERE id=:id')
                    ->bindValue(':id', $getAttrValueData->id)
                    ->bindValue(':value', $fileop[1])
                    ->execute();
                echo 'Fees Update:' . $fileop[0] . PHP_EOL;
            } else {
                $sqlInsert = "INSERT INTO highlight_attribute_value (entity, entity_id, is_key_feature_id,value,status)
                        VALUES ('course','" . $courseDetail->id . "','" . $heiglightAttr->id . "','" . $fileop[1] . "'," . HighlightAttributeValue::STATUS_ACTIVE . ')';
                \Yii::$app->getDb()->createCommand($sqlInsert)->execute();
                echo 'Fees Insert:' . $fileop[0] . PHP_EOL;
            }
        }
        fclose($handle);
    }

    public function actionProgramPageIndex()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_program_list.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $college = CollegeProgram::find()->where(['id' => $fileop[0]])
                ->andWhere(['status' => Program::STATUS_ACTIVE])
                ->andWhere(['page_index' => Program::STATUS_INACTIVE])
                ->one();

            if (!$college) {
                continue;
            }

            // $status = CollegeProgram::updateAll(['page_index' => 1], ['college_id'  => $college->id, 'status' => Program::STATUS_ACTIVE]);
            $college->page_index = Program::STATUS_ACTIVE;

            if ($college->save()) {
                echo "{$college->program->slug} \t {$college->id} \n";
            } else {
                print_r($college);
            }
        }
    }

    public function actionUpdateCollectionProgramPageIndex()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'collegeListforindexingPpage.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }


            $college = College::find()->where(['slug' => $fileop[0]])->one();
            if (!$college) {
                continue;
            }


            $collection = Yii::$app->mongodb->getCollection(DocumentsCollegeProgram::COLLECTION_NAME);

            $arr = ['pageIndex' => 1];
            $status = $collection->update(
                ['college_id'  => $college->id, 'status' => Program::STATUS_ACTIVE],
                $arr
            );

            if ($status > 0) {
                echo "{$college->slug} \t {$college->id} \n";
            } else {
                print_r($status);
            }
        }
    }

    public function actionUpdateProgramContent()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'collegeListforindexingPpage.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }


            $college = College::find()->where(['slug' => $fileop[0]])->one();
            if (!$college) {
                continue;
            }

            $programs = CollegeProgram::find()->where(['college_id' => $college->id])->andWhere(['page_index' => 1])->all();

            if (empty($programs)) {
                continue;
            }

            foreach ($programs as $program) {
                $model = CollegeProgramContent::find()->where(['college_course_id' => $program->id])->one();
                if (empty($model)) {
                    continue;
                }

                if ($model->save()) {
                    echo "{$college->slug} \t {$program->course->slug} \t {$program->program->slug} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionExamCoursePositionMapping()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam_course_position_mapping.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = ExamCourse::find()->where(['course_id' => $fileop[0]])->andWhere(['exam_id' => $fileop[1]])->one();

            if (!$model) {
                $model = new ExamCourse();
            }

            $model->course_id = $fileop[0];
            $model->exam_id = $fileop[1];
            $model->position = $fileop[2] ?? null;

            if ($model->save()) {
                echo "{$model->exam_id} \t {$model->course_id} \t {$model->position} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    /* Function  for update title,h1 and desc for course content*/

    public function actionUpdateCourseContent()
    {

        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'YearChange2023to2024-Courses.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        $totalrow = 0;
        $notupdateArray = [];
        $notupdateArrayCourse = [];
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $fileop[1] = preg_replace('/^-/', '', $fileop[1]);
            $courseData = Course::find()->select(['id'])->where(['slug' => $fileop[0]])->one();
            $totalrow++;
            if (!empty($courseData)) {
                if (isset(self::$_pageTypeArray[$fileop[1]])) {
                    $fileop[1] =  self::$_pageTypeArray[$fileop[1]];
                }
                $courseCountent = CourseContent::find()->select(['h1', 'meta_description', 'meta_title'])
                    ->where(['course_id' => $courseData->id])
                    ->andWhere(['page' => $fileop[1]])
                    ->one();
                if (!empty($courseCountent)) {
                    // $totalrow++;
                    \Yii::$app->db->createCommand('UPDATE course_content SET h1=:h1,meta_title=:meta_title,meta_description=:meta_description WHERE course_id=:course_id AND page=:page')
                        ->bindValue(':h1', $fileop[3])
                        ->bindValue(':meta_title', $fileop[2])
                        ->bindValue(':meta_description', $fileop[4])
                        ->bindValue(':course_id', $courseData->id)
                        ->bindValue(':page', $fileop[1])
                        ->execute();
                    echo 'Course Update:' . $totalrow . ' ' . $courseCountent->id . ' ' . $fileop[0] . ' ' . $courseData->id . ' ' . $fileop[1] . PHP_EOL;
                } else {
                    $notupdateArray[$totalrow] = ['slug' => $fileop[0], 'page' => $fileop[1]];
                    echo  'Course not Update:' . $totalrow . ' ' . $fileop[0] . ' ' . $courseData->id . ' ' . $fileop[1] . PHP_EOL;
                }
            } else {
                $notupdateArrayCourse[$totalrow] = ['slug' => $fileop[0], 'page' => $fileop[1]];
            }
        }
        if (!empty($notupdateArray)) {
            print_r($notupdateArray);
        }
        if (!empty($notupdateArrayCourse)) {
            print_r($notupdateArrayCourse);
        }
        echo 'Total Row' . $totalrow;
    }

    public function actionLeadActivity()
    {
        $date_1 = '2024-01-03';
        $date_2 =  '2024-01-08';
        $all_leads =  GmuCustomLandingpageLeads::find()
            ->where(['between', 'created_on',  $date_1,  $date_2])
            ->andWhere(['exam_id' => null])
            ->andWhere([
                'or',
                ['college_id' => 0],
                ['college_id' => null]
            ])
            ->all();
        foreach ($all_leads as $lead) {
            $student = Student::find()->select(['id'])
                ->where(['phone' => $lead->phone])
                ->andWhere(['source' => DataHelper::$leadSource['google_paid_ads']])
                ->one();
            if (!empty($student)) {
                $studentActivity = new StudentActivity();
                $studentActivity->student_id = $student->id;
                $studentActivity->url = $lead->url;
                $studentActivity->cta_text = 'clp';
                $studentActivity->cta_position = 'clp';
                $studentActivity->utm_source = $lead->utm_source;
                $studentActivity->utm_medium = $lead->utm_medium;
                $studentActivity->utm_campaign = $lead->utm_campaign;
                $studentActivity->source = 3;
                $studentActivity->category = 'clp';
                $studentActivity->category_sub_page = 'clp';
                $studentActivity->created_at = $lead->created_on;
                $studentActivity->updated_at = date('Y-m-d H:i:s');
                if ($studentActivity->save()) {
                    $getcourseId = empty($lead->course) ? (empty($lead->course_interested) ? ($lead->course ?? '') : $lead->course_interested) : $lead->course;
                    if ($getcourseId) {
                        $couseData =  Course::find()->select(['id'])->where(['slug' => $getcourseId])->one();
                        if (!empty($couseData)) {
                            $courseId =  $couseData->id;
                        }
                    }
                    $highestEducationByCourseId = empty($courseId) ? [] : (new LeadService)->getCourseData($courseId);
                    if (isset($highestEducationByCourseId['highest_qualification']) && !empty($highestEducationByCourseId)) {
                        $highestEducation = empty($highestEducationByCourseId) ? '' : ($highestEducationByCourseId['highest_qualification'] ?? '');
                    }
                    $courseData = empty($courseId) ? [] : (new LeadService)->getCourseData($courseId);
                    $degreeId = empty($courseData) && empty($courseData['degree']) ? null : Degree::find()->select('id')->where(['slug' => $courseData['degree']])->one();
                    $cityID = City::find()->select(['id'])->where(['slug' => $lead->city])->one();
                    $stateID =  State::find()->select(['id'])->where(['slug' => $lead->state])->one();
                    $studentPreference = new StudentPreference();
                    $studentPreference->activity_id = $studentActivity->id;
                    $studentPreference->student_id = $student->id;
                    $studentPreference->interested_city = $cityID->id ?  $cityID->id : null;
                    $studentPreference->interested_state = $stateID->id ? $stateID->id : null;
                    $studentPreference->course = isset($courseData) && !empty($courseData['parent_id']) ? $courseData['parent_id'] : $courseId;
                    $studentPreference->child_course_id = $courseId ? (int) $courseId : '';
                    $studentPreference->program_id =  '';
                    $studentPreference->degree = !empty($courseId) && ($courseId == 610) ? null : (empty($degreeId) ? null : $degreeId['id']);
                    $studentPreference->highest_qualification = $highestEducation ?? '';
                    $studentPreference->distance_education = 0;
                    $studentPreference->specialization =  (isset($courseData) && !empty($courseData['specialization_id']) ? $courseData['specialization_id'] : null);
                    if (!empty($lead->exam_id)) {
                        $studentPreference->exam = $lead->exam_id;
                    } else {
                        $studentPreference->exam = null;
                    }
                    if (!empty($courseData) && !empty($courseData['stream_id'])) {
                        $studentPreference->stream = $courseData['stream_id'] ?? null;
                    }
                    $studentPreference->scenario = StudentPreference::SCENARIO_IMPORTER;
                    if ($studentPreference->save()) {
                        echo "Preference Save {$lead->id} \t {$studentPreference->id}  \n";
                    } else {
                        print_r($studentPreference->getErrors());
                    }
                    echo "Activity Save {$lead->id} \t {$student->id}  \n";
                } else {
                    print_r($studentActivity->getErrors());
                }
            }
        }
    }

    public function actionGenerateAssets()
    {
        self::minifyAndCopyFiles(Yii::getAlias('@frontend') . '/web/yas/css/version2/', Yii::getAlias('@frontend') . '/web/yas/css/version2/min/');
        self::minifyAndCopyFiles(Yii::getAlias('@frontend') . '/web/yas/js/version2/', Yii::getAlias('@frontend') . '/web/yas/js/version2/min/');
        echo 'Assets Generated!';
    }

    public static function minifyAndCopyFiles($sourceDir, $targetDir)
    {
        $iterator = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($sourceDir, \RecursiveDirectoryIterator::SKIP_DOTS));

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filePath = $file->getPathname();
                $relativePath = str_replace($sourceDir, '', $filePath);
                $targetPath = $targetDir . $relativePath;

                if (strpos($filePath, 'min') !== false) {
                    continue;
                }

                // Determine file type (CSS or JS) and create minified version
                $extension = pathinfo($file->getFilename(), PATHINFO_EXTENSION);

                if ($extension === 'css') {
                    $minifier = new Minify\CSS($filePath);
                    $minifier->minify($targetPath);
                } elseif ($extension === 'js') {
                    $minifier = new Minify\JS($filePath);
                    $minifier->minify($targetPath);
                }
            }
        }
    }

    public function actionCoursePageIndex()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_college_page.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $college = College::find()->where(['slug' => $fileop[1]])->one();
            if (!$college) {
                continue;
            }

            $queries = CollegeProgram::find()
                ->where(['college_id' => $college->id])
                ->andWhere(['status' => 1])
                ->andWhere(['not', ['program_id' => null]])
                ->groupBy('course_id');
            foreach ($queries->batch() as $programs) {
                foreach ($programs as $program) {
                    $model = CollegeCourseContent::find()
                        ->where(['college_id' => $program->college_id])
                        ->andWhere(['course_id' => $program->course_id])
                        ->one();

                    if (!$model) {
                        $model = new CollegeCourseContent();
                    }
                    $model->college_id = $program->college_id;
                    $model->course_id = $program->course_id;
                    $model->page_index = 1;
                    $model->status = CollegeProgram::STATUS_ACTIVE;

                    if ($model->save()) {
                        echo "{$model->course->name} \t {$college->name} \n";
                    } else {
                        print_r($model->getErrors());
                    }
                }
            }
            $i++;
        }
    }

    //function to import wrong and corrected email domain
    public function actionImportEmailDomain()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'email_domain.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = LeadGmailDomain::find()->where(['id' => (int) $fileop[0]])->one();

            if (!$model) {
                $model = new LeadGmailDomain();
            }

            $model->wrong_domain = $fileop[1];
            $model->corrected_domain = $fileop[2];

            if ($model->save()) {
                echo "{$model->wrong_domain} \t {$model->corrected_domain} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionImportLeadBucketTaggingSubpage()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'lead_bucket_tagging_subpage.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $model = LeadBucketTagging::find()
                ->where(['sub_page' => $fileop[1]])
                ->andWhere(['entity' => $fileop[0]])
                ->andWhere(['cta_position' => $fileop[4]])
                ->one();

            if (!$model) {
                $model = new LeadBucketTagging();
            }

            $model->entity = $fileop[0];
            $model->sub_page = $fileop[1];
            $model->entity_id = $fileop[2] == 'NULL' ? null : $fileop[2];
            $model->bucket_id = $fileop[3] == 'NULL' ? null : $fileop[3];
            $model->cta_position = $fileop[4];
            $model->web = $fileop[5];
            $model->wap = $fileop[6];
            $model->status = $fileop[7];

            if ($model->save()) {
                echo "{$model->entity} \t {$model->sub_page} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionImportLeadBudget()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'lead_fees_budget.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = LeadEducationBudget::find()
                ->where(['id' => (int) $fileop[0]])->one();

            if (!$model) {
                $model = new LeadEducationBudget();
            }

            $model->stream_id = (int) $fileop[1];
            $model->degree_id = (int) $fileop[3];
            $model->display_name = $fileop[5];

            if ($model->save()) {
                echo "{$model->stream_id} \t {$model->degree_id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateCollegeListingBucket()
    {
        $model = LeadBucketTagging::find()->where(['id' => 253])->andWhere(['entity' => 7])->one();

        $model->cta_position = 'colleges_listing_card_normal_left_cta';
        $model->save();
    }

    public function actionUpdatePredictCollegeListingBucket()
    {
        $model = LeadBucketTagging::find()->where(['id' => 35837])->andWhere(['entity' => 7])->one();

        $model->web = 'colleges_listing_card_predict_my_college';
        $model->save();
    }

    public function actionUpdateCollegeListingBucketLeft()
    {
        $model = LeadBucketTagging::find()->where(['id' => 253])->andWhere(['entity' => 7])->one();

        $model->web = 'colleges_listing_{slug}_web_card_left_cta';
        $model->wap = 'colleges_listing_{slug}_wap_card_left_cta';
        $model->save();
    }

    public function actionUpdateCollegeListingBucketRight()
    {
        $model = LeadBucketTagging::find()->where(['cta_position' => 'colleges_listing_card_normal_right_cta'])->andWhere(['entity' => 7])->one();

        if (!$model) {
            $model = new LeadBucketTagging();
        }

        $model->entity = LeadBucketTagging::LEAD_ENTITY_COLLEGE_LISTING;
        $model->sub_page = 'college-listing';
        $model->entity_id = null;
        $model->bucket_id = 354;
        $model->cta_position = 'colleges_listing_card_normal_right_cta';
        $model->web = 'colleges_listing_{slug}_web_card_right_cta';
        $model->wap = 'colleges_listing_{slug}_wap_card_right_cta';
        $model->save();
    }

    public function actionUpdateArticlePublishedAt()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'article_published_at.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $model = Course::find()
                ->where(['short_name' => $fileop[0], 'parent_id' => null])
                ->one();

            if (!$model) {
                $model = new DsaStateMapping();
            }

            $model->campaign_id = (int) $fileop[0];
            $model->state_id = (int) $fileop[1];
            $model->status = DsaStateMapping::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->id} \t {$model->campaign_id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionCollegeContentNewsImport()
    {
        $query = College::find()->select(['id', 'display_name'])->where(['status' => 1]);
        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                $content =  CollegeContent::find()
                    ->where(['entity_id' => $college->id])
                    ->andWhere(['sub_page' => 'news'])
                    ->one();

                if (empty($content)) {
                    $articles = new Query();
                    $articles->select(['a.id'])
                        ->from(['article a'])
                        ->innerJoin('article_college ac', 'a.id = ac.article_id')
                        ->innerJoin('college c', 'c.id = ac.college_id')
                        ->where(['c.id' => $college->id])
                        ->andWhere(['a.status' => Article::STATUS_ACTIVE]);

                    $news = new Query();
                    $news->select(['ns.id'])
                        ->from(['news_subdomain ns'])
                        ->innerJoin('college_news_subdomain cns', 'ns.id = cns.news_id')
                        ->innerJoin('college c', 'cns.college_id = c.id')
                        ->where(['c.id' => $college->id])
                        ->andWhere(['ns.status' => News::STATUS_ACTIVE]);

                    if ($articles->count() >= 4 || $news->count() >= 4) {
                        $newContent = new CollegeContent();
                        $newContent->author_id = 1;
                        $newContent->entity = 'college';
                        $newContent->entity_id = $college->id;
                        $newContent->sub_page = 'news';
                        $newContent->status = 1;
                        if ($newContent->save()) {
                            echo $college->display_name . "\n";
                        } else {
                            print_r($newContent->getErrors());
                        }
                    }
                }
            }
        }
        exit('News College Content saved successfully!');
    }

    public function actionUpdateAuthorExamContent()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam_content_author.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = ExamContent::find()
                ->where(['id' => (int) $fileop[0]])
                ->one();

            if (!$model) {
                continue;
            }

            $model->scenario = ExamContent::SCENARIO_IMPORTER;
            $model->author_id = (int) $fileop[2];

            if ($model->save()) {
                echo "{$model->id} \t {$model->author_id} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateCoursePositions()
    {
        // UPDATE `gmu_yii_new`.`course` SET `position` = null WHERE (`id` >= '0') and parent_id is null;
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_positions.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $model = Course::find()
                ->where(['slug' => $fileop[0], 'parent_id' => null])
                ->one();

            if ($model) {
                $model->position = $fileop[1];
                if ($model->save()) {
                    $modelProgram = Program::find()
                        ->where(['slug' => $fileop[2]])
                        ->one();
                    if ($modelProgram) {
                        $modelProgram->position = $fileop[3];
                        if ($modelProgram->save()) {
                            echo "{$i} \t {$modelProgram->slug} \t {$modelProgram->position} \n";
                        }
                    }
                    // echo "{$i} \t {$model->short_name} \t {$model->position} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionUpdateCollectionProgramPosition()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'course_positions.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $program = Program::find()->where(['slug' => $fileop[2]])->one();
            if (!$program) {
                continue;
            }
            $collection = Yii::$app->mongodb->getCollection(DocumentsCollegeProgram::COLLECTION_NAME);

            //default setting
            // $update = $collection->update(
            //     ['status' => Program::STATUS_ACTIVE],
            //     ['program_position' => null]
            // );

            $arr = ['program_position' => (int)$fileop[3]];
            $status = $collection->update(
                ['program_id'  => $program->id, 'status' => Program::STATUS_ACTIVE],
                $arr
            );
            if ($status > 0) {
                echo "{$program->slug} \t {$program->id} \n";
            } else {
                print_r($status);
            }
        }
    }

    public function actionCreateNewsLeadBucketTagging()
    {
        try {
            Yii::$app->db->createCommand()->batchInsert(
                'lead_bucket_tagging',
                ['entity', 'sub_page', 'bucket_id', 'cta_position', 'web', 'wap', 'status', 'created_at', 'updated_at'],
                [
                    [4, 'news', 165, 'colleges_news_1', 'colleges_news_web_banner_cta1', 'colleges_colleges_news_1_wap_banner_cta1', 1, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')],
                    [4, 'news', 166, 'colleges_news_2', 'colleges_news_web_banner_cta2', 'colleges_colleges_news_1_wap_banner_cta2', 1, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')],
                    [4, 'news', 329, 'colleges_news_3', 'colleges_news_web_lead_capture_panel_left_cta3', 'colleges_colleges_news_1_wap_lead_capture_panel_left_cta3', 1, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')],
                    [4, 'news', 165, 'colleges_news_4', 'colleges_news_web_lead_capture_panel_right_cta4', 'colleges_colleges_news_1_wap_lead_capture_panel_right_cta4', 1, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')],
                ]
            )->execute();
            exit('Record inserted successfully!');
        } catch (\yii\db\Exception $e) {
            Yii::error('Error inserting into lead_bucket_tagging: ' . $e->getMessage(), __METHOD__);
            throw $e;
        }
    }

    public function actionImportLeadBucket()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'lead_bucket.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            $entity = ArrayHelper::getValue(DataHelper::getConstantList('LEAD_ENTITY', LeadBucketTagging::class), $fileop[0]);
            $lead_bucket = $entity . ' Data';
            if ($fileop[1] !== 'NULL') {
                $lead_bucket = $entity . ' ' . ucwords(str_replace('-', ' ', $fileop[1]));
            }

            $model = LeadBucket::find()->where(['bucket' => $lead_bucket])->one();

            if (!$model) {
                $model = new LeadBucket();
                $model->entity_id = $fileop[0];
                $model->template_id = 2;
                $model->bucket = $lead_bucket;
                $model->save();
            }
            if (!empty($model->id)) {
                $modelCta = new LeadBucketCta();
                $modelCta->bucket_id = $model->id;
                $modelCta->cta_title = '';
                $modelCta->cta_description = '';
                $modelCta->lead_form_title = $fileop[9];
                $modelCta->lead_form_description = $fileop[10];
                $modelCta->cta_text = $fileop[8];
                $modelCta->cta_position = $fileop[7];
                $modelCta->cta_slug = $fileop[6];
                $modelCta->platform = $fileop[5];
                $modelCta->web = null;
                $modelCta->wap = null;
                if ($fileop[5] == LeadBucketCta::PLATFORM_WEB) {
                    $modelCta->web = $fileop[6] . '_web';
                } else if ($fileop[5] == LeadBucketCta::PLATFORM_WAP) {
                    $modelCta->wap = $fileop[6] . '_wap';
                } else {
                    $modelCta->web = $fileop[6] . '_web';
                    $modelCta->wap = $fileop[6] . '_wap';
                }
                $modelCta->cta_new_name = $fileop[2];
                $modelCta->page_event = '';
                $modelCta->status = LeadBucketCta::STATUS_ACTIVE;
                $modelCta->save();
                if ($modelCta->save()) {
                    echo "{$modelCta->id} \t {$modelCta->bucket_id} \n";
                } else {
                    print_r($modelCta->getErrors());
                }
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionImportLeadBucketCta()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cta_text.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $modelCta = LeadBucketCta::find()->where(['cta_new_name' => $fileop[5]])->one();

            if (!empty($modelCta->id) && empty($modelCta->cta_text)) {
                // $modelCta->lead_form_title = $fileop[2];
                // $modelCta->lead_form_description = $fileop[3];
                // $modelCta->cta_text = $fileop[4];
                $modelCta->status = LeadBucketCta::STATUS_ACTIVE;
                $modelCta->save();
                if ($modelCta->save()) {
                    echo "{$modelCta->id} \t {$modelCta->bucket_id} \n";
                } else {
                    print_r($modelCta->getErrors());
                }
            }
        }
    }

    public function actionImportLeadBucketTagging()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'lead_tagging.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $entity = ArrayHelper::getValue(DataHelper::getConstantList('LEAD_ENTITY', LeadBucketTagging::class), $fileop[0]);
            $lead_bucket = $entity . ' Data';
            if ($fileop[1] !== 'NULL') {
                $lead_bucket = $entity . ' ' . ucwords(str_replace('-', ' ', $fileop[1]));
            }
            if ($fileop[0] == '7') {
                $lead_bucket = $entity . ' Data';
            }
            $model = LeadBucket::find()->where(['bucket' => $lead_bucket])->one();
            if ($fileop[0] !== '1' && $fileop[0] !== '6') {
                $modelTag = LeadBucketTagging::find()->where(['entity' => $fileop[0], 'sub_page' => $fileop[1]])->one();
            } else {
                $modelTag = LeadBucketTagging::find()->where(['entity' => $fileop[0], 'entity_id' => $fileop[2]])->one();
            }

            if (!empty($modelTag->id)) {
                $modelTag->bucket_id = $model->id;
                if ($fileop[0] !== '1' && $fileop[0] !== '6') {
                    $modelTag->sub_page = $fileop[1];
                    $modelTag->entity_id = null;
                } else {
                    $modelTag->sub_page = null;
                    $modelTag->entity_id = $fileop[2];
                }
                $modelTag->status = LeadBucketCta::STATUS_ACTIVE;
                $modelTag->save();
                if ($modelTag->save()) {
                    echo "{$modelTag->id} \t {$modelTag->bucket_id} \n";
                } else {
                    print_r($modelTag->getErrors());
                }
            } else {
                $modelTag = new LeadBucketTagging();
                $modelTag->entity = $fileop[0];
                if ($fileop[0] !== '1' && $fileop[0] !== '6') {
                    $modelTag->sub_page = $fileop[1];
                    $modelTag->entity_id = null;
                } else {
                    $modelTag->sub_page = null;
                    $modelTag->entity_id = $fileop[2];
                }
                if (empty($model)) {
                    echo 'model is empty';
                }
                $modelTag->bucket_id = $model->id;
                $modelTag->status = LeadBucketCta::STATUS_ACTIVE;
                $modelTag->save();
                if ($modelTag->save()) {
                    echo "{$modelTag->id} \t {$modelTag->bucket_id}\t {$modelTag->sub_page} \t {$model->id} \n";
                } else {
                    print_r($modelTag->getErrors());
                }
            }
        }
    }

    public function actionImportLeadBucketNewsArticles()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'leadbucket_newsarticle.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            if ($fileop[0] !== 'NULL') {
                $lead_bucket = $fileop[0];
            }

            $model = LeadBucket::find()->where(['bucket' => $lead_bucket])->one();

            if (!$model) {
                $model = new LeadBucket();
                $model->entity_id = 10;
                $model->template_id = 2;
                $model->bucket = $lead_bucket;
                $model->save();
            }
            if ((!empty($model->id)) && ($fileop[1] != 'popup_cta')) {
                $bucket_slug = $this->createSlug($lead_bucket);
                $modelCta = new LeadBucketCta();
                $modelCta->bucket_id = $model->id;
                $modelCta->cta_title = $fileop[4];
                $modelCta->cta_description = '';
                $modelCta->lead_form_title = '';
                $modelCta->lead_form_description = '';
                $modelCta->cta_text = $fileop[4];
                $modelCta->cta_position = $fileop[1];
                $cta_slug = 'news_article_{slug}_' . $bucket_slug . '_' . $fileop[1];
                $modelCta->cta_slug = $cta_slug;
                $modelCta->platform = 0;
                $modelCta->web = $cta_slug . '_web';
                $modelCta->wap = $cta_slug . '_wap';
                $modelCta->cta_new_name = '';
                $modelCta->page_event = '';
                $modelCta->status = LeadBucketCta::STATUS_ACTIVE;
                $modelCta->save();
                if ($modelCta->save()) {
                    echo "{$modelCta->id} \t {$modelCta->bucket_id} \n";
                } else {
                    print_r($modelCta->getErrors());
                }
            }
        }
    }

    public function actionImportLeadBucketCtaDetail()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cta_text_detail2.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            if ($fileop[0] !== 'NULL') {
                $lead_bucket = $fileop[0];
            }

            $modelCta = LeadBucketCtaDetail::find()->where(['cta_text' => $fileop[1]])->one();

            if ((empty($modelCta))) {
                $modelCta = new LeadBucketCtaDetail();
                $modelCta->entity = 10;
                $modelCta->cta_title = $fileop[0];
                $modelCta->cta_text = $fileop[1];
                $modelCta->lead_form_title = $fileop[2];
                $modelCta->lead_form_description = $fileop[3];
                $modelCta->status = 1;
                $modelCta->save();
                if ($modelCta->save()) {
                    echo "{$modelCta->id}\n";
                } else {
                    print_r($modelCta->getErrors());
                }
            }
        }
    }

    public function actionImportLeadBucketNewsArticleCta()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cta_text_icon.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            if ($fileop[0] !== 'NULL') {
                $lead_bucket = $fileop[0];
            }

            $modelCta = LeadBucketCta::find()->where(['id' => $fileop[0]])->one();
            // dd($modelCta);
            if ((!empty($modelCta->id))) {
                $ctaDetail = LeadBucketCtaDetail::find()->where(['cta_title' => $fileop[2]])->one();
                // dd($ctaDetail->createCommand()->getRawsql());
                if (!empty($ctaDetail)) {
                    echo 'success ----------- ' . $ctaDetail['cta_text'] . "\n";
                    $modelCta->cta_id = $ctaDetail['id'];
                    $modelCta->cta_text = $ctaDetail['cta_text'];
                    $modelCta->cta_title = $ctaDetail['cta_title'];
                    $modelCta->lead_form_title = $ctaDetail['lead_form_title'];
                    $modelCta->lead_form_description = $ctaDetail['lead_form_description'];
                    // $modelCta->save();
                    if ($modelCta->save()) {
                        echo "{$modelCta->id}\n";
                    } else {
                        print_r($modelCta->getErrors());
                    }
                } else {
                    echo 'error ----------- ' . $fileop[2] . "\n";
                }
            }
        }
    }

    public function actionImportLeadBucketNewsArticleCta1()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'tech_from_lead.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            if ($fileop[0] !== 'NULL') {
                $lead_bucket = $fileop[0];
            }

            $modelCta = LeadBucketCta::find()->where(['bucket_id' => $fileop[1]])->andWhere(['cta_position' => $fileop[2]])->one();
            // dd($modelCta);
            if ((!empty($modelCta->id))) {
                // $ctaDetail = LeadBucketCtaDetail::find()->where(['cta_title' => $fileop[2]])->one();
                // dd($ctaDetail->createCommand()->getRawsql());
                // if (!empty($ctaDetail)) {
                echo 'success ----------- ' . $fileop[7] . "\n";
                $modelCta->cta_id = (int)$fileop[6];
                $modelCta->cta_text = $fileop[7];
                $modelCta->cta_title = $fileop[5];
                $modelCta->lead_form_title = $fileop[8];
                $modelCta->lead_form_description = $fileop[10];
                // $modelCta->save();
                // dd($modelCta);
                if ($modelCta->save()) {
                    echo "{$modelCta->id}\n";
                    // die('hi');
                } else {
                    print_r($modelCta->getErrors());
                }
                // } else {
                //     echo 'error ----------- ' . $fileop[2] . "\n";
                // }
            }
        }
    }


    private function createSlug($string)
    {
        $string = str_replace(' ', '_', $string); // Replaces all spaces with hyphens.
        return str_replace('__', '_', strtolower(preg_replace('/[^A-Za-z0-9\_]+/', '', $string))); //removes special character
    }

    public function actionUpdateLeadBucketDetails()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'lead_cta_updated.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            if ($fileop[0] !== 'NULL') {
                $lead_bucket = $fileop[0];
            }

            $model = LeadBucket::find()->where(['id' => $fileop[0]])->one();
            if (!empty($model)) {
                $bucket_slug = $this->createSlug($model->bucket);
                $modelCta = LeadBucketCta::find()->where(['bucket_id' => $fileop[0]])->andWhere(['cta_position' => $fileop[2]])->one();
                if (empty($modelCta)) {
                    $modelCtaDetail = LeadBucketCtaDetail::find()->where(['id' => $fileop[5]])->one();
                    if ($modelCtaDetail) {
                        $modelCta = new LeadBucketCta();
                        $modelCta->bucket_id = $model->id;
                        $modelCta->cta_id = $modelCtaDetail->id;
                        $modelCta->cta_title = $modelCtaDetail->cta_title;
                        $modelCta->cta_description = $modelCtaDetail->cta_description;
                        $modelCta->lead_form_title = $modelCtaDetail->lead_form_title;
                        $modelCta->lead_form_description = $modelCtaDetail->lead_form_description;
                        $modelCta->cta_text = $modelCtaDetail->cta_text;
                        $modelCta->cta_position = $fileop[2];
                        $cta_slug = 'news_article_{slug}_' . $bucket_slug . '_' . $fileop[2];
                        $modelCta->cta_slug = $cta_slug;
                        $modelCta->platform = 0;
                        $modelCta->web = $cta_slug . '_web';
                        $modelCta->wap = $cta_slug . '_wap';
                        $modelCta->cta_new_name = '';
                        $modelCta->page_event = '';
                        $modelCta->status = LeadBucketCta::STATUS_ACTIVE;
                        $modelCta->save();
                        if ($modelCta->save()) {
                            echo "{$modelCta->id} \t {$modelCta->bucket_id} \n";
                        } else {
                            print_r($modelCta->getErrors());
                        }
                    }
                } else {
                    $modelCtaDetail = LeadBucketCtaDetail::find()->where(['id' => $fileop[5]])->one();
                    if ($modelCtaDetail) {
                        $modelCta->bucket_id = $model->id;
                        $modelCta->cta_id = $modelCtaDetail->id;
                        $modelCta->cta_title = $modelCtaDetail->cta_title;
                        $modelCta->cta_description = $modelCtaDetail->cta_description;
                        $modelCta->lead_form_title = $modelCtaDetail->lead_form_title;
                        $modelCta->lead_form_description = $modelCtaDetail->lead_form_description;
                        $modelCta->cta_text = $modelCtaDetail->cta_text;
                        $modelCta->cta_position = $fileop[2];
                        $cta_slug = 'news_article_{slug}_' . $bucket_slug . '_' . $fileop[2];
                        $modelCta->cta_slug = $cta_slug;
                        $modelCta->platform = 0;
                        $modelCta->web = $cta_slug . '_web';
                        $modelCta->wap = $cta_slug . '_wap';
                        $modelCta->cta_new_name = '';
                        $modelCta->page_event = '';
                        $modelCta->status = LeadBucketCta::STATUS_ACTIVE;
                        $modelCta->save();
                        if ($modelCta->save()) {
                            echo "exist - {$modelCta->id} \t {$modelCta->bucket_id} \n";
                        } else {
                            print_r($modelCta->getErrors());
                        }
                    }
                }
            }
        }
    }

    public function actionMediaDriveUpload()
    {
        $exam_upload_drive = [
            'Previous Year Question Paper',
            'Memory Based Question papers',
            'Notifications',
            'Candidate\'s Rejection List',
            'Exam Dates',
            'Accepted candidate\'s list',
            'Postponements',
            'Sample Papers',
            'Mock Tests',
            'Cutoff',
            'Merit Lists',
            'Syllabus',
            'Unofficial and Official Answer Keys',
            'Rank Lists',
            'Exam Day Guidelines',
            'Exam Date Notification',
            'Information Broucher',
            'Question Paper',
            'Preparation Tips',
            'Paper Analysis',
            'Seat Allotment',
            'Download Guide',
            'Counselling Schedule',
            'Previous Year Cutoff marks'
        ];
        $board_upload_drive = [
            'Answer keys',
            'Previous Year Question Paper',
            'Question paper',
            'PYQP',
            'Syllabus',
            'Date Sheet',
            'Sample papers',
            'Notification'
        ];
        $college_upload_drive = [
            'Brochure'
        ];
        foreach ($exam_upload_drive as $key => $value) {
            $model = new MediaDriveUploadType();
            $model->entity = 'exam';
            $model->upload_type = $value;
            $model->status = 1;
            if ($model->save()) {
                echo $model->upload_type . "\n";
            } else {
                print_r($model->getErrors());
            }
        }

        foreach ($board_upload_drive as $key => $value) {
            $model = new MediaDriveUploadType();
            $model->entity = 'board';
            $model->upload_type = $value;
            $model->status = 1;
            if ($model->save()) {
                echo $model->upload_type . "\n";
            } else {
                print_r($model->getErrors());
            }
        }

        foreach ($college_upload_drive as $key => $value) {
            $model = new MediaDriveUploadType();
            $model->entity = 'college';
            $model->upload_type = $value;
            $model->status = 1;
            if ($model->save()) {
                echo $model->upload_type . "\n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionMediaDriveUploadMapping()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cta-upload_mapping.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            $upload_type = MediaDriveUploadType::find()->select('id')->where(['upload_type' => $fileop[2], 'entity' => $fileop[3]])->one();
            if (!empty($upload_type['id'])) {
                $modelCta = new MediaDriveCtaTypeMapping();
                $modelCta->cta_id = $fileop[0];
                $modelCta->upload_type_id = $upload_type['id'];
                $modelCta->entity = $fileop[3];
                $modelCta->status = 1;
                if ($modelCta->save()) {
                    echo "{$modelCta->id}\n";
                } else {
                    print_r($modelCta->getErrors());
                }
            } else {
                echo "{$fileop[2]}\n";
            }
        }
    }

    public function actionAuthorArticleUpdate()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'article_author_update.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }
            if ($fileop[0] !== 'NULL') {
                $lead_bucket = $fileop[0];
            }
            $modelArticle = Article::find()->select('id')
                ->where(['slug' => $fileop[1]])->one();
            $modelUser = User::find('id')
                ->where(['email' => $fileop[2]])->one();
            if ((!empty($modelArticle['id'])) && !empty($modelUser['id'])) {
                $modelArticle->slug = $fileop[1];
                $modelArticle->author_id = $modelUser['id'];
                $modelArticle->status = 1;
                if ($modelArticle->save()) {
                    echo "{$modelArticle->id}\n";
                } else {
                    print_r($modelArticle->getErrors());
                }
            }
        }
    }

    public function actionUpdateBoardUrl()
    {
        $query = BoardContent::find();

        foreach ($query->batch() as $boards) {
            foreach ($boards as $b) {
                $data = BoardContent::find()->where(['id' => $b->id])
                    ->with(['board'])->one();
                // dd($data->board);
                if (!$data) {
                    continue;
                }

                if (empty($data->board->slug)) {
                    continue;
                }
                $data->content = $this->actionParseBoardUrl($data->content, $data->board->slug);

                if ($data->save()) {
                    // dd($data->board->slug);
                    echo "{$data->page_slug} \t {$data->board->id}" . "\n";
                }
            }
        }
    }

    public function actionParseBoardUrl(string $content, string $boardSlug): string
    {
        $data = [];

        $urls = [
            '{board_slug}/b' => 'boards/{board_slug}',
            '{board_slug}-time-table/b' => 'boards/{board_slug}-time-table',
            '{board_slug}-registration-form/b' => 'boards/{board_slug}-registration-form',
            '{board_slug}-admit-card/b' => 'boards/{board_slug}-admit-card',
            '{board_slug}-syllabus/b' => 'boards/{board_slug}-syllabus',
            '{board_slug}-results/b' => 'boards/{board_slug}-results',
            '{board_slug}-supplementary/b' => 'boards/{board_slug}-supplementary',
            '{board_slug}-sample-papers/b' => 'boards/{board_slug}-sample-papers',
            '{board_slug}-previous-year-question-papers/b' => 'boards/{board_slug}-previous-year-question-papers',
            '{board_slug}-exam-pattern/b' => 'boards/{board_slug}-exam-pattern',
            '{board_slug}-toppers/b' => 'boards/{board_slug}-toppers',
            '{board_slug}-registration-form/b' => 'boards/{board_slug}-registration-form',
            '{board_slug}-preparation/b' => 'boards/{board_slug}-preparation',
            '{board_slug}-answer-key/b' => 'boards/{board_slug}-answer-key',
            '{board_slug}-books/b' => 'boards/{board_slug}-books',
            '{board_slug}-preparation/b' => 'boards/{board_slug}-preparation',
            '{board_slug}-application-form/b' => 'boards/{board_slug}-application-form',
            '{board_slug}-hall-ticket/b' => 'boards/{board_slug}-hall-ticket',
            '{board_slug}-biology-answer-key/b' => 'boards/{board_slug}-biology-answer-key',
            '{board_slug}-geography-answer-key/b' => 'boards/{board_slug}-geography-answer-key',
            '{board_slug}-reference-books/b' => 'boards/{board_slug}-reference-books',
            '{board_slug}-supplementary-admit-card-smy/b' => 'boards/{board_slug}-supplementary-admit-card',
            '{board_slug}-supplementary-time-table-smy/b' => 'boards/{board_slug}-supplementary-time-table',
            '{board_slug}-supplementary-result-smy/b' => 'boards/{board_slug}-supplementary-result',
            '{board_slug}-supplementary-data-sheet-smy/b' => 'boards/{board_slug}-supplementary-data-sheet',
            '{board_slug}-supplementary-hall-ticket-smy/b' => 'boards/{board_slug}-supplementary-hall-ticket',
            '{board_slug}-mathematics-question-paper/b' => 'boards/{board_slug}-mathematics-question-paper',
            '{board_slug}-physics-question-paper/b' => 'boards/{board_slug}-physics-question-paper',
            '{board_slug}-chemistry-question-paper/b' => 'boards/{board_slug}-chemistry-question-paper',
            '{board_slug}-biology-question-paper/b' => 'boards/{board_slug}-biology-question-paper',
            '{board_slug}-computer-science-question-paper/b' => 'boards/{board_slug}-computer-science-question-paper',
            '{board_slug}-economics-question-paper/b' => 'boards/{board_slug}-economics-question-paper',
            '{board_slug}-english-question-paper/b' => 'boards/{board_slug}-english-question-paper',
            '{board_slug}-business-studies-question-paper/b' => 'boards/{board_slug}-business-studies-question-paper',
            '{board_slug}-accountancy-question-paper/b' => 'boards/{board_slug}-accountancy-question-paper',
            '{board_slug}-hindi-question-paper/b' => 'boards/{board_slug}-hindi-question-paper',
            '{board_slug}-home-science-question-paper/b' => 'boards/{board_slug}-home-science-question-paper',
            '{board_slug}-physical-education-question-paper/b' => 'boards/{board_slug}-physical-education-question-paper',
            '{board_slug}-history-question-paper/b' => 'boards/{board_slug}-history-question-paper',
            '{board_slug}-geography-question-paper/b' => 'boards/{board_slug}-geography-question-paper',
            '{board_slug}-political-science-question-paper/b' => 'boards/{board_slug}-political-science-question-paper',
            '{board_slug}-psychology-question-paper/b' => 'boards/{board_slug}-psychology-question-paper',
            '{board_slug}-sanskrit-question-paper/b' => 'boards/{board_slug}-sanskrit-question-paper',
            '{board_slug}-sociology-question-paper/b' => 'boards/{board_slug}-sociology-question-paper',
            '{board_slug}-social-science-question-paper/b' => 'boards/{board_slug}-social-science-question-paper',
            '{board_slug}-mathematics-syllabus/b' => 'boards/{board_slug}-mathematics-syllabus',
            '{board_slug}-physics-syllabus/b' => 'boards/{board_slug}-physics-syllabus',
            '{board_slug}-chemistry-syllabus/b' => 'boards/{board_slug}-chemistry-syllabus',
            '{board_slug}-biology-syllabus/b' => 'boards/{board_slug}-biology-syllabus',
            '{board_slug}-computer-science-syllabus/b' => 'boards/{board_slug}-computer-science-syllabus',
            '{board_slug}-economics-syllabus/b' => 'boards/{board_slug}-economics-syllabus',
            '{board_slug}-english-syllabus/b' => 'boards/{board_slug}-english-syllabus',
            '{board_slug}-business-studies-syllabus/b' => 'boards/{board_slug}-business-studies-syllabus',
            '{board_slug}-accountancy-syllabus/b' => 'boards/{board_slug}-accountancy-syllabus',
            '{board_slug}-hindi-syllabus/b' => 'boards/{board_slug}-hindi-syllabus',
            '{board_slug}-home-science-syllabus/b' => 'boards/{board_slug}-home-science-syllabus',
            '{board_slug}-physical-education-syllabus/b' => 'boards/{board_slug}-physical-education-syllabus',
            '{board_slug}-history-syllabus/b' => 'boards/{board_slug}-history-syllabus',
            '{board_slug}-geography-syllabus/b' => 'boards/{board_slug}-geography-syllabus',
            '{board_slug}-political-science-syllabus/b' => 'boards/{board_slug}-political-science-syllabus',
            '{board_slug}-psychology-syllabus/b' => 'boards/{board_slug}-psychology-syllabus',
            '{board_slug}-sanskrit-syllabus/b' => 'boards/{board_slug}-sanskrit-syllabus',
            '{board_slug}-sociology-syllabus/b' => 'boards/{board_slug}-sociology-syllabus',
            '{board_slug}-social-science-syllabus/b' => 'boards/{board_slug}-social-science-syllabus',
            '{board_slug}-mathematics-answer-key/b' => 'boards/{board_slug}-mathematics-answer-key',
            '{board_slug}-physics-answer-key/b' => 'boards/{board_slug}-physics-answer-key',
            '{board_slug}-chemistry-answer-key/b' => 'boards/{board_slug}-chemistry-answer-key',
            '{board_slug}-biology-answer-key/b' => 'boards/{board_slug}-biology-answer-key',
            '{board_slug}-computer-science-answer-key/b' => 'boards/{board_slug}-computer-science-answer-key',
            '{board_slug}-economics-answer-key/b' => 'boards/{board_slug}-economics-answer-key',
            '{board_slug}-english-answer-key/b' => 'boards/{board_slug}-english-answer-key',
            '{board_slug}-business-studies-answer-key/b' => 'boards/{board_slug}-business-studies-answer-key',
            '{board_slug}-accountancy-answer-key/b' => 'boards/{board_slug}-accountancy-answer-key',
            '{board_slug}-hindi-answer-key/b' => 'boards/{board_slug}-hindi-answer-key',
            '{board_slug}-home-science-answer-key/b' => 'boards/{board_slug}-home-science-answer-key',
            '{board_slug}-physical-education-answer-key/b' => 'boards/{board_slug}-physical-education-answer-key',
            '{board_slug}-history-answer-key/b' => 'boards/{board_slug}-history-answer-key',
            '{board_slug}-geography-answer-key/b' => 'boards/{board_slug}-geography-answer-key',
            '{board_slug}-political-science-answer-key/b' => 'boards/{board_slug}-political-science-answer-key',
            '{board_slug}-psychology-answer-key/b' => 'boards/{board_slug}-psychology-answer-key',
            '{board_slug}-sanskrit-answer-key/b' => 'boards/{board_slug}-sanskrit-answer-key',
            '{board_slug}-sociology-answer-key/b' => 'boards/{board_slug}-sociology-answer-key',
            '{board_slug}-social-science-answer-key/b' => 'boards/{board_slug}-social-science-answer-key'
        ];

        $newArry = [];
        foreach ($urls as $key => $value) {
            // Replace the string in each value
            $newArry[str_replace('{board_slug}', $boardSlug, $key)] = str_replace('{board_slug}', $boardSlug, $value);
        }
        foreach (array_keys($newArry) as $url) {
            if ((strpos($content, $url) == true)) {
                $data[$url] = $newArry[$url];
            }
        }

        return strtr($content, $data);
    }


    public function actionUpdateBoardSlug()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'updated_overview_slug.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $board = Board::find()->where(['slug' => $fileop[0]])->one();
            if (empty($board)) {
                return [];
            }

            $board->slug = $fileop[1];
            $board->old_slug = $fileop[0];

            if ($board->save()) {
                echo "{$board->slug}\t {$board->old_slug}\n";
            } else {
                print_r($board->getErrors());
            }
        }
    }

    public function actionUpdateBoardContentSlug()
    {
        $arr = [
            'time-table' => 'date-sheet',
            'hall-ticket' => 'admit-card',
            'routine' => 'date-sheet',
            'reference-books' => 'books'
        ];

        $data = ['time-table', 'hall-ticket', 'routine', 'reference-books'];

        $boards = BoardContent::find()->select(['page_slug', 'id', 'old_page_slug', 'parent_id'])->where(['in', 'page_slug', $data]);
        foreach ($boards->batch() as $board) {
            foreach ($board as $b) {
                if (!empty($b->parent_id)) {
                    continue;
                }
                $board = BoardContent::find()->select(['page_slug', 'id', 'old_page_slug'])->where(['page_slug' => $b->page_slug])->one();
                if (empty($board)) {
                    continue;
                }

                $board->page_slug = $arr[$b->page_slug];
                $board->old_page_slug = $b->page_slug;

                if ($board->save()) {
                    echo "{$board->page_slug}\t {$board->old_page_slug}\n";
                } else {
                    print_r($board->getErrors());
                }
            }
        }
    }

    public function actionUpdateTrendingCourse()
    {
        // $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'trendingCourse.csv';
        // $entityType = 'course';

        // $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'TrendingExam.csv';
        // $entityType = 'exam';

        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'TrendingBoard.csv';
        $entityType = 'board';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $trending = TrendingPage::find()
                ->where(['entity' => $entityType])
                ->andWhere(['entity_id' => $fileop[1], 'display_name' => $fileop[3]])
                ->one();

            if (!$trending) {
                $trending = new TrendingPage();
            }

            $trending->entity = $entityType;
            $trending->entity_id = $fileop[1];
            $trending->url = $fileop[2];
            $trending->display_name = $fileop[3];

            if ($trending->save()) {
                echo "{$trending->display_name} \n";
            } else {
                print_r($trending->getErrors());
            }
        }
    }

    public function actionUpdateExamPosition()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam_positions.csv';
        $i = 0;

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = Exam::find()
                ->where(['slug' => $fileop[2], 'parent_id' => null])
                ->one();

            if ($model) {
                $model->search_volume = $fileop[4];
                if ($model->save()) {
                    echo "{$model->display_name} \t {$model->search_volume} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    public function actionUpdateCollegeCity()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'collegeCityUpdate.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $college = College::find()->select(['id', 'slug'])->where(['slug' => $fileop[1]])->one();

            if (empty($college)) {
                continue;
            }

            $college->city_id = 906;

            if ($college->save()) {
                echo "{$college->slug} \n";
            } else {
                print_r($college->getErrors());
            }
        }
    }


    public function actionCollegeProgramFeesUpdates()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'collegeprogram_fees_uplaod.csv';
        $i = 0;

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $query = new Query();
            $model = $query->select(['cpf.id', 'cp.id as cProgramId', 'cpf.status'])
                ->from('college_program_fees cpf')
                ->innerJoin('college_program cp', 'cp.id = cpf.college_program_id')
                ->where(['cp.college_id' => $fileop[0]])
                ->andWhere(['college_program_id' => $fileop[2]])
                ->andWhere(['cp.status' => 1])
                ->andWhere(['type' => 'tuition_fees'])
                ->all();

            if (count($model) == (int)$fileop[9]) {
                $i = 1;
                foreach ($model as $value) {
                    if ($i <= (int)$fileop[9]) {
                        $feesModel = CollegeProgramFees::find()->where(['id' => $value['id'], 'type' => 'tuition_fees'])->one();
                        if (empty($feesModel)) {
                            continue;
                        }

                        $feesModel->fees = (int)($fileop[6] / $fileop[9]);

                        $feesModel->duration_type = 0;
                        $feesModel->type = 'tuition_fees';
                        $feesModel->duration = $i;
                        $feesModel->status = 1;
                        // dd($feesModel);
                        if ($feesModel->save()) {
                            echo "{$feesModel->id} \t {$feesModel->fees} \n";
                        }
                    }
                    $i++;
                }
            }
            self::collegeSave($fileop[0], $fileop[2]);
        }
    }

    public static function collegeSave($collegeId, $collegeProgramId)
    {
        if (!empty($collegeProgramId)) {
            $collegeProgModel = CollegeProgram::find()->where(['college_id' => $collegeId, 'id' => $collegeProgramId])->one();
            if (!empty($collegeProgModel)) {
                if ($collegeProgModel->save()) {
                    echo $collegeProgModel->course->slug . " \n";
                    $collegeModel = College::find()->where(['id' => $collegeId])->one();
                    if (!empty($collegeModel) && $collegeModel->save()) {
                        echo "{$collegeModel->id} \n";
                        // dd($collegeModel->slug);
                    }
                }
            }
        }
    }

    public function actionLiveApplicationFormDeactivate()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'laf-deactive.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $liveApplication = LiveApplication::find()
                ->where(['college_id' => (int) $fileop[2]])
                ->andWhere(['status' => LiveApplication::STATUS_ACTIVE])
                ->one();

            if ($liveApplication == null) {
                continue;
            }

            $liveApplication->status = LiveApplication::STATUS_INACTIVE;

            if ($liveApplication->save()) {
                echo "College ID {$liveApplication->college_id} status updated to INACTIVE\n";
            } else {
                echo "Failed to update College ID {$liveApplication->college_id}. Errors: \n";
                print_r($liveApplication->getErrors());
            }
        }
    }

    public function actionPopularArticleUpdate()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'articleSlug.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        $j = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $j++;
            $modelArticle = Article::find()->where(['slug' => $fileop[0]])->one();
            if (!empty($modelArticle['id'])) {
                $modelArticle->is_popular = Article::POPULAR_YES;
                if ($modelArticle->save()) {
                    echo "{$modelArticle->id}\t{$j}\n";
                } else {
                    print_r($modelArticle->getErrors());
                }
            }
        }
    }

    public function actionImportBucketwiseCta()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'bucket_cta_correction_new.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $model = LeadBucket::find()->where(['id' => $fileop[0]])->one();

            if (!empty($model->id)) {
                $checkCtaPopUp = LeadBucketCta::find()
                    ->where(['bucket_id' => $fileop[0]])
                    ->andWhere(['cta_id' => $fileop[1]])
                    ->andWhere(['cta_position' => 'auto_popup_cta'])
                    ->one();

                if ($checkCtaPopUp) {
                    echo "{$checkCtaPopUp->cta_text} /t auto pop up position exsits \n";
                    continue;
                }

                $modelCta = new LeadBucketCta();
                $ctaData = LeadBucketCtaDetail::find()->where(['id' => $fileop[1]])->one();
                $bucket_slug = $this->createSlug($model->bucket);
                if (!empty($ctaData)) {
                    $modelCta->bucket_id = $model->id;
                    $modelCta->cta_id = $ctaData->id;
                    $modelCta->cta_text = $ctaData->cta_text;
                    $modelCta->cta_title = $ctaData->cta_title;
                    $modelCta->cta_description = '';
                    $modelCta->lead_form_title = $ctaData->lead_form_title;
                    $modelCta->lead_form_description = $ctaData->lead_form_description;
                    $modelCta->cta_position = 'auto_popup_cta';
                }
                $cta_slug = 'news_article_{slug}_' . $bucket_slug . '_' . 'auto_popup_cta';
                $modelCta->cta_slug = $cta_slug;
                $modelCta->platform = 0;
                $modelCta->web = $cta_slug . '_web';
                $modelCta->wap = $cta_slug . '_wap';
                $modelCta->cta_new_name = '';
                $modelCta->page_event = '';
                $modelCta->status = LeadBucketCta::STATUS_ACTIVE;
                $modelCta->save();
                if ($modelCta->save()) {
                    echo "{$modelCta->id} \t {$modelCta->bucket_id} \n";
                } else {
                    print_r($modelCta->getErrors());
                }
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionImportApplyNowCollegeCta()
    {
        //Colleges Courses Fees
        //Colleges Info
        $model = LeadBucket::find()->where(['bucket' => 'Colleges Info'])->one();

        if (!empty($model->id)) {
            $modelCta = new LeadBucketCta();
            $modelCta->bucket_id = $model->id;
            $modelCta->cta_id = null;
            $modelCta->cta_text = 'Apply Now {list}';
            $modelCta->cta_title = 'Apply Now';
            $modelCta->cta_description = '';
            $modelCta->lead_form_title = 'Register now to Apply';
            $modelCta->lead_form_description = '{name}, {city}';
            $modelCta->cta_position = 'card_top_cta';
            // $cta_slug = 'colleges_courses-fees_{slug}_card_top_cta';
            $cta_slug = 'colleges_info_{slug}_course_card_top_cta';
            $modelCta->cta_slug = $cta_slug;
            $modelCta->platform = 0;
            $modelCta->web = $cta_slug . '_web';
            $modelCta->wap = $cta_slug . '_wap';
            $modelCta->cta_new_name = '';
            $modelCta->page_event = '';
            $modelCta->status = LeadBucketCta::STATUS_ACTIVE;
            $modelCta->save();
            if ($modelCta->save()) {
                echo "{$modelCta->id} \t {$modelCta->bucket_id} \n";
            } else {
                print_r($modelCta->getErrors());
            }
        } else {
            print_r($model->getErrors());
        }
    }

    public function actionUpdateDegreeTableDisplayName()
    {

        $degreeClassification = [
            'Bachelors' => 'UG',
            'Masters' => 'PG',
            'Doctorate' => 'Doctorate',
            'Diploma' => 'Diploma',
            'Postgraduate Diploma' => 'PG',
            'Certificate' => 'Certificate',
            'Postgraduate Certificate' => 'PG',
            '10th' => 'UG',
            '11th' => 'UG',
            '12th' => 'UG',
        ];

        $degrees = Degree::find()->all();

        foreach ($degrees as $degree) {
            $degree->display_name = isset($degreeClassification[$degree->name]) ?  $degreeClassification[$degree->name] : '';
            $degree->save();
        }
    }

    public function actionCollegeDeactive()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_name.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $liveApplication = College::find()
                ->where(['slug' => $fileop[1]])
                ->andWhere(['status' => LiveApplication::STATUS_ACTIVE])
                ->one();

            if (empty($liveApplication)) {
                continue;
            }

            $liveApplication->status = LiveApplication::STATUS_INACTIVE;

            if ($liveApplication->save()) {
                echo "College ID {$liveApplication->slug} status updated to INACTIVE\n";
            } else {
                echo "Failed to update College ID {$liveApplication->college_id}. Errors: \n";
                print_r($liveApplication->getErrors());
            }
        }
    }

    public function actionImportBoardsH1Title()
    {
        echo 'Import Started.' . PHP_EOL;

        $pagesToUpdate = ['syllabus', 'answer-key'];
        $boards = Board::find()->indexBy('id')->all();

        $row = 0;
        $updatedRecords = [];
        $failedRecords = [];

        foreach ($pagesToUpdate as $pageSlug) {
            $parentIds = BoardContent::find()
                ->select(['id', 'board_id'])
                ->where(['page_slug' => $pageSlug])
                ->indexBy('board_id')
                ->asArray()
                ->all();
            foreach ($boards as $board) {
                if (!isset($parentIds[$board->id])) {
                    echo "Skipping: No subpage found for Board ID {$board->id} \n";
                    continue;
                }

                $parentId = $parentIds[$board->id]['id'];

                $models = BoardContent::find()
                    ->where(['board_id' => $board->id, 'parent_id' => $parentId])
                    ->all();

                foreach ($models as $model) {
                    $row++;

                    try {
                        $dynamicSeo = ContentHelper::getBoardSeoInfo($board->display_name, $board->state_id, $model->page_slug, $pageSlug);

                        if (empty($dynamicSeo)) {
                            echo "Skipping: Excluded Board found {$board->id} \n";

                            // Store excluded board details
                            $excludedBoards[] = [
                                'Board ID' => $board->id,
                                'Board Name' => $board->display_name,
                                'State ID' => $board->state_id
                            ];

                            continue;
                        }
                        $h1 = $title = $dynamicSeo[$board->level] ?? '';

                        if (empty($h1)) {
                            echo "Skipping: H!, Title not found {$board->id} \n";
                            continue;
                        }

                        $model->h1 = $h1;
                        $model->meta_title = $title;

                        if ($model->save()) {
                            $updatedRecords[] = [
                                'Page Slug' => $model->page_slug,
                                'Board Name' => $board->display_name,
                                'Updated Fields' => 'h1, title',
                                'New H1' => $h1,
                                'Subpage' => $pageSlug,
                                'level' => $board->level
                            ];
                        } else {
                            $failedRecords[] = [
                                'Page Slug' => $model->page_slug,
                                'Board Name' => $board->display_name,
                                'Errors' => json_encode($model->getErrors())
                            ];
                        }
                    } catch (\Exception $e) {
                        echo "Error on row $row: " . $e->getMessage() . PHP_EOL;
                    }
                }
            }
        }

        // Summary Report
        echo "\n==== Import Summary ====\n";
        $summaryData = [
            [
                'Total Records' => $row,
                'Updated Records' => count($updatedRecords),
                'Failed Records' => count($failedRecords)
            ]
        ];
        echo ConsoleHelper::table($summaryData);

        if (!empty($updatedRecords)) {
            echo "\n==== Updated Records ====\n";
            echo ConsoleHelper::table($updatedRecords);
        }

        if (!empty($failedRecords)) {
            echo "\n==== Failed Records ====\n";
            echo ConsoleHelper::table($failedRecords);
        }


        if (!empty($excludedBoards)) {
            echo "\n==== Excluded Boards ====\n";
            echo ConsoleHelper::table($excludedBoards);
        }

        echo 'All done!' . PHP_EOL;
    }

    public function actionImportAllBoardsH1Title()
    {
        echo 'Import Started.' . PHP_EOL;

        $row = 0;
        $updatedRecords = [];
        $failedRecords = [];

        $boards = BoardContent::find()
            ->where(['NOT', ['parent_id' => null]])
            ->andWhere(['lang_code' => 1])
            ->all();
        foreach ($boards as $board) {
            try {
                $type = $board->parent_id == '' ? '' :  $board->parent->page_slug;
                $dynamicSeo = ContentHelper::getBoardSeoInfo($board->board->display_name, $board->board->state_id, $board->page_slug, $type);

                if (empty($dynamicSeo)) {
                    echo "Skipping: Excluded Board found {$board->board->display_name} \t {$board->board->state_id} \n";

                    // Store excluded board details
                    $excludedBoards[] = [
                        'Board ID' => $board->id,
                        'Board Name' => $board->board->display_name,
                        'State ID' => $board->board->state_id,
                        'Page Slug' => $board->page_slug
                    ];

                    continue;
                }
                $row++;

                $h1 = $title = $dynamicSeo[$board->board->level] ?? '';

                if (empty($h1)) {
                    echo "Skipping: H!, Title not found {$board->id} \n";
                    continue;
                }

                $board->h1 = $h1;
                $board->meta_title = $title;

                if ($board->save()) {
                    $updatedRecords[] = [
                        'Page Slug' => $board->page_slug,
                        'Board Name' => $board->board->display_name,
                        'Updated Fields' => 'h1, title',
                        'New H1' => $h1,
                        'type' => $type
                    ];
                } else {
                    $failedRecords[] = [
                        'Page Slug' => $board->page_slug,
                        'Board Name' => $board->board->display_name,
                        'Errors' => json_encode($board->getErrors())
                    ];
                }
            } catch (\Exception $e) {
                echo "Error on row $row: " . $e->getMessage() . PHP_EOL;
            }
        }
        // }

        // Summary Report
        echo "\n==== Import Summary ====\n";
        $summaryData = [
            [
                'Total Records' => $row,
                'Updated Records' => count($updatedRecords),
                'Failed Records' => count($failedRecords)
            ]
        ];
        echo ConsoleHelper::table($summaryData);

        if (!empty($updatedRecords)) {
            echo "\n==== Updated Records ====\n";
            echo ConsoleHelper::table($updatedRecords);
        }

        if (!empty($failedRecords)) {
            echo "\n==== Failed Records ====\n";
            echo ConsoleHelper::table($failedRecords);
        }


        if (!empty($excludedBoards)) {
            echo "\n==== Excluded Boards ====\n";
            echo ConsoleHelper::table($excludedBoards);
        }

        echo 'All done!' . PHP_EOL;
    }

    public function actionImportAllBoardsParentH1Title()
    {
        echo 'Import Started.' . PHP_EOL;

        $row = 0;
        $updatedRecords = [];
        $failedRecords = [];

        $boards = BoardContent::find()
            ->where(['parent_id' => null])
            ->andWhere(['lang_code' => 1])
            ->all();
        foreach ($boards as $board) {
            if (empty($board) || empty($board->board)) {
                echo "Skipping NO Board: \n";
                continue;
            }

            $board_name = $board->board->display_name ?? $board->board->name;
            try {
                $type = $board->parent_id == '' ? '' :  $board->parent->page_slug;
                $dynamicSeo = ContentHelper::getBoardSeoInfo($board_name, $board->board->state_id, $board->page_slug, $type);

                if (empty($dynamicSeo)) {
                    echo "Skipping: Excluded Board found {$board_name} \t {$board->board->state_id} \n";

                    // Store excluded board details
                    $excludedBoards[] = [
                        'Board ID' => $board->id,
                        'Board Name' => $board_name,
                        'State ID' => $board->board->state_id,
                        'Page Slug' => $board->page_slug
                    ];

                    continue;
                }
                $row++;

                $h1 = $title = $dynamicSeo[$board->board->level] ?? '';

                if (empty($h1)) {
                    echo "Skipping: H!, Title not found {$board->id} \t {$board->board->level} \n";
                    continue;
                }

                $board->h1 = $h1;
                $board->meta_title = $title;

                if ($board->save()) {
                    $updatedRecords[] = [
                        'Page Slug' => $board->page_slug,
                        'Board Name' => $board_name,
                        'Updated Fields' => 'h1, title',
                        'New H1' => $h1,
                        'type' => $type
                    ];
                } else {
                    $failedRecords[] = [
                        'Page Slug' => $board->page_slug,
                        'Board Name' => $board_name,
                        'Errors' => json_encode($board->getErrors())
                    ];
                }
            } catch (\Exception $e) {
                echo "Error on row $row: " . $e->getMessage() . PHP_EOL;
                $failedRecords[] = [
                    'Page Slug' => $board->page_slug,
                    'Board Name' => $board_name,
                    'Errors' => json_encode($board->getErrors())
                ];
            }
        }
        // }

        // Summary Report
        echo "\n==== Import Summary ====\n";
        $summaryData = [
            [
                'Total Records' => $row,
                'Updated Records' => count($updatedRecords),
                'Failed Records' => count($failedRecords)
            ]
        ];
        echo ConsoleHelper::table($summaryData);

        if (!empty($updatedRecords)) {
            echo "\n==== Updated Records ====\n";
            echo ConsoleHelper::table($updatedRecords);
        }

        if (!empty($failedRecords)) {
            echo "\n==== Failed Records ====\n";
            echo ConsoleHelper::table($failedRecords);
        }


        if (!empty($excludedBoards)) {
            echo "\n==== Excluded Boards ====\n";
            echo ConsoleHelper::table($excludedBoards);
        }

        echo 'All done!' . PHP_EOL;
    }

    public function actionCollegeDocReader()
    {
        $csvFilePath = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'docReading.csv';
        $i = 0;

        if (!file_exists($csvFilePath)) {
            return 'CSV file not found!';
        }

        if (($handle = fopen($csvFilePath, 'r')) !== false) {
            while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                if ($i == 0 || empty($data[0])) { // Skip header row and empty rows
                    $i++;
                    continue;
                }

                $college = College::find()
                    ->where(['id' => $data[0], 'status' => College::STATUS_ACTIVE])
                    ->one();

                if (!empty($college) && !empty($data[4])) { // Ensure the column has a value
                    $collegeContents = CollegeContent::find()
                        ->where(['entity_id' => $data[0]])
                        ->andWhere(['sub_page' => 'info'])
                        ->one();

                    $contents = $this->fetchGoogleDocContent($data[4]);

                    if (strpos($contents, 'Error') === 0) {
                        Yii::error("Failed to fetch content for URL: {$data[4]}. Error: $contents");
                        continue;
                    }

                    // Remove unwanted metadata and fix HTML
                    $cleanContent = $this->cleanGoogleDocsHTML($contents);
                    $collegeContents->h1 = $data[5];
                    $collegeContents->meta_title = $data[6];
                    $collegeContents->author_id = $data[7];
                    $collegeContents->content = $cleanContent;
                    if (!($collegeContents->save())) {
                        print_r($collegeContents->getErrors());
                    }
                }

                $i++; // Increment counter
            }
            fclose($handle);
        }
    }

    public function fetchGoogleDocContent($url)
    {
        try {
            $exportUrl = $this->getGoogleDocExportUrl($url);

            $client = new \GuzzleHttp\Client([
                'headers' => [
                    'User-Agent' => 'Mozilla/5.0', // Mimic a browser request
                ]
            ]);

            $response = $client->get($exportUrl, ['http_errors' => false]);

            if ($response->getStatusCode() !== 200) {
                return 'Error fetching content: HTTP ' . $response->getStatusCode();
            }

            $htmlContent = (string) $response->getBody();

            if (empty(trim($htmlContent))) {
                return 'Error: Empty response from Google Docs';
            }

            // Clean up unnecessary <p> and <span> tags
            return $this->cleanGoogleDocsHTML($htmlContent);
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    public function getGoogleDocExportUrl($url)
    {
        if (preg_match('/\/document\/d\/([a-zA-Z0-9_-]+)/', $url, $matches)) {
            return "https://docs.google.com/document/d/{$matches[1]}/export?format=html";
        }
        return $url; // Return original URL if no match
    }

    public function cleanGoogleDocsHTML($html)
    {
        $html = preg_replace('/<meta[^>]+>/i', '', $html); // Remove <meta> tags
        $html = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $html); // Remove <script> tags
        $html = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $html); // Remove <style> tags

        return $this->removeExtraPTags($html);
    }

    public function removeExtraPTags($html)
    {
        $dom = new DOMDocument();
        libxml_use_internal_errors(true); // Suppress warnings due to malformed HTML

        if (!$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'))) {
            return 'Error: Unable to parse HTML.';
        }

        libxml_clear_errors();

        $xpath = new DOMXPath($dom);

        // Remove <p class="c0">
        foreach ($xpath->query('//p[contains(@class, "c0") or contains(@class, "c1")]') as $p) {
            while ($p->hasChildNodes()) {
                $p->parentNode->insertBefore($p->firstChild, $p);
            }
            $p->parentNode->removeChild($p);
        }

        // Remove <span class="c1">
        foreach ($xpath->query('//span[contains(@class, "c0") or contains(@class, "c1")]') as $span) {
            while ($span->hasChildNodes()) {
                $span->parentNode->insertBefore($span->firstChild, $span);
            }
            $span->parentNode->removeChild($span);
        }

        return $dom->saveHTML();
    }

    public function actionNewCtaPostAction()
    {
        $this->actionImportBucketwiseCta(); //news article auto pop up
        $this->actionAddNewCtaText();
        $this->actionArticleNewsCta();
        $this->actionAlternateCtaText();
        $this->actionCtaTextThankYou();
        $this->actionUpdateBoardNewCtaText();
        $this->actionInsertBoardAutoPop();
        $this->actionUpdateAltenateCtaIdArticleNew();
        $this->actionUpdateExamNewCtaText();
        $this->actionInsertExamAutoPop();
    }

    public function actionAlternateCtaText()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'alternate_cta_text.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        $j = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = AlternateCtaText::find()->where(['cta_text' => $fileop[0]])->one();

            if (!$model) {
                $model = new AlternateCtaText();
            }

            $model->cta_text = $fileop[0];
            $model->status = AlternateCtaText::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->cta_text} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionAddNewCtaText()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'new_cta_text.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        $j = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = LeadBucketCtaDetail::find()->where(['cta_text' => $fileop[0]])->one();

            if (!$model) {
                $model = new LeadBucketCtaDetail();
            } else {
                echo "{$model->cta_text} \t exsits \n";
            }

            $model->scenario = LeadBucketCtaDetail::SCENARIO_IMPORTER;
            $model->entity = LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE;
            $model->cta_text = $fileop[0];
            $model->lead_form_title = $fileop[1];
            $model->status = LeadBucket::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->cta_text} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateNewCtaTextBucketWise()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'bucketwisw_new_cta_update.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        $j = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }
            $cta_id = LeadBucketCtaDetail::find()->where(['cta_text' => $fileop[3]])->one();
            $model = LeadBucketCta::find()
                ->where(['bucket_id' => $fileop[1]])
                ->andWhere(['cta_position' => $fileop[2]])
                ->one();

            if (!$model || !$cta_id) {
                echo "model does not exsits \n";
            }

            $model->bucket_id = $fileop[1];
            $model->cta_id = $cta_id->id;
            $model->cta_title = $cta_id->cta_text;
            $model->lead_form_title = $cta_id->lead_form_title;
            $model->lead_form_description = $cta_id->lead_form_description;
            $model->cta_text = $cta_id->cta_text;

            if ($model->save()) {
                echo "{$model->cta_text} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateCtaText()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cta_text_test.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        $j = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $model = LeadBucketCtaDetail::find()
                ->where(['id' => $fileop[0]])
                ->one();

            if (!$model) {
                $model = new LeadBucketCtaDetail();
            }

            $model->id = $fileop[0];
            $model->entity = $fileop[1];
            $model->cta_text = $fileop[2];
            $model->cta_title = $fileop[3];
            $model->cta_description = $fileop[4];
            $model->lead_form_title = $fileop[5];
            $model->lead_form_description = $fileop[6];
            $model->status = $fileop[7];
            $model->created_at = $fileop[8];
            $model->updated_at = $fileop[9];

            if ($model->save()) {
                echo "{$model->cta_text} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionArticleNewsCta()
    {
        echo 'Import Started.' . PHP_EOL;

        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'article_news_cta_update.csv';

        if (!file_exists($file) || ($handle = fopen($file, 'r')) === false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $row = 0;
        $insertedRecords = [];
        $failedRecords = [];
        $notUpdatedRecords = [];

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            $row++;

            if ($row == 1 || empty($fileop[0])) { // Skip header and empty rows
                continue;
            }

            try {
                $model = LeadBucketCta::find()->where(['id' => $fileop[0]])->one();

                if (!$model) {
                    echo "CTA Model Not Found for ID: {$fileop[0]} \n";
                    $failedRecords[] = ['id' => $fileop[0], 'error' => 'Model not found'];
                    continue;
                }

                $model->cta_id = $fileop[3];
                $model->cta_text = $fileop[10];

                if ($model->save()) {
                    $insertedRecords[] = [
                        'id' => $model->id,
                        'old_cta_text' => $model->cta_text,
                        'new_cta_text' => $fileop[10],
                        'new_cta_id' => $fileop[3],
                        'old_cta_id' => $model->cta_id
                    ];
                } else {
                    $failedRecords[] = [
                        'id' => $model->id,
                        'errors' => json_encode($model->errors)
                    ];
                }

                echo "Processing Row $row from CSV.\r";
            } catch (\Exception $e) {
                echo "Error on row $row: " . $e->getMessage() . PHP_EOL;
            }
        }


        self::importSummaryReport($row, $failedRecords, $insertedRecords, $notUpdatedRecords);
        fclose($handle);
    }

    public function actionUpdateBoardNewCtaText()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'board_cta_text.csv';

        $row = 0;
        $failedRecords = [];
        $updatedRecords = [];

        if (!file_exists($file)) {
            echo "CSV File doesn't exist!" . PHP_EOL;
            echo 'Path: ' . $file . PHP_EOL;
            exit;
        }

        echo 'Import Started.' . PHP_EOL;

        if (($handle = fopen($file, 'r')) !== false) {
            while (($data = fgetcsv($handle, 2000, ',')) !== false) {
                $row++;

                if ($row == 1 || empty($data[0])) {
                    continue;
                }

                try {
                    $model = LeadBucketCta::findOne($data[0]);

                    if (!$model) {
                        echo "Skipping ID {$data[0]} - Model not found\n";
                        continue;
                    }

                    // Track changes
                    $updatedFields = [];

                    // Define fields to update
                    $fieldsToUpdate = [
                        'bucket_id' => $data[1],
                        'alternate_cta_text_id' => $data[2],
                        'cta_new_name' => $data[3],
                        'lead_form_title' => $data[4],
                        'lead_form_description' => $data[5],
                        'cta_text' => $data[6] ?? $model->cta_text,
                        'cta_position' => $data[7],
                        'cta_slug' => $data[8],
                        'web' => $data[9],
                        'wap' => $data[10],
                    ];

                    foreach ($fieldsToUpdate as $field => $newValue) {
                        // dd($model, $model->$field, $newValue);
                        // if ($model->$field != $newValue) {
                        $updatedFields[$field] = [
                            'old' => $model->$field,
                            'new' => $newValue
                        ];
                        // }
                        $model->$field = $newValue;
                    }

                    if (!$model->save()) {
                        $failedRecords[] = [
                            'id' => $model->id,
                            'errors' => json_encode($model->errors)
                        ];
                    } elseif (!empty($updatedFields)) {
                        $updatedRecords[] = [
                            'id' => $model->id,
                            'updated_columns' => json_encode($updatedFields)
                        ];
                    }

                    echo "Processing Row $row from CSV.\r";
                } catch (\Exception $e) {
                    echo "Error on row $row: " . $e->getMessage() . PHP_EOL;
                }
            }

            self::importSummaryReport($row, $failedRecords, $updatedRecords);

            fclose($handle);
        }
    }


    public function actionInsertBoardAutoPop()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'board_auto_pop_up.csv';

        $row = 0;
        $failedRecords = [];
        $updatedRecords = [];

        if (!file_exists($file)) {
            echo "CSV File doesn't exist!" . PHP_EOL;
            echo 'Path: ' . $file . PHP_EOL;
            exit;
        }

        echo 'Import Started.' . PHP_EOL;

        if (($handle = fopen($file, 'r')) !== false) {
            while (($data = fgetcsv($handle, 2000, ',')) !== false) {
                $row++;

                if ($row == 1 || empty($data[1])) { // Skip header and empty rows
                    continue;
                }

                try {
                    $model = LeadBucketCta::find()
                        ->where(['bucket_id' => $data[1]])
                        ->andWhere(['cta_position' => $data[6]])
                        ->one();

                    if (!$model) {
                        $model = new LeadBucketCta();
                    }

                    $model->bucket_id = $data[1];
                    $model->cta_new_name = $data[2];
                    $model->lead_form_title = $data[3];
                    $model->lead_form_description = $data[4];
                    $model->cta_text = $data[5];
                    $model->cta_position = $data[6];
                    $model->cta_slug = $data[7];
                    $model->web = $data[8];
                    $model->wap = $data[9];
                    $model->status = LeadBucket::STATUS_ACTIVE;

                    if ($model->save()) {
                        $insertedRecords[] = [
                            'id' => $model->id,
                            'cta_text' => $model->cta_text,
                            'cta_position' => $model->cta_position,
                            'cta_slug' => $model->cta_slug
                        ];
                    } else {
                        $failedRecords[] = [
                            'row' => $row,
                            'errors' => json_encode($model->errors)
                        ];
                    }

                    echo "Processing Row $row from CSV.\r";
                } catch (\Exception $e) {
                    echo "Error on row $row: " . $e->getMessage() . PHP_EOL;
                }
            }

            self::importSummaryReport($row, $failedRecords, $insertedRecords);
            fclose($handle);
        }
    }

    public function importSummaryReport($row, $failedRecords, $records, $notUpdatedRecords = [])
    {
        // Summary Report
        echo "\n==== Import Summary ====\n";
        $summaryData = [
            [
                'Total Records' => $row,
                'Updated/Inserted Records' => count($records), // Only count truly updated ones
                'Failed Records' => count($failedRecords)
            ]
        ];
        echo ConsoleHelper::table($summaryData);

        if (!empty($records)) {
            echo "\n==== Inserted Records ====\n";
            echo ConsoleHelper::table($records);
        }

        if (!empty($failedRecords)) {
            echo "\n==== Failed Records ====\n";
            echo ConsoleHelper::table($failedRecords);
        }

        if (!empty($notUpdatedRecords)) {
            echo "\n==== Not Updated Records ====\n";
            echo "\n==== Not Updated Records ====\n";
            $notUpdatedTable = [];

            foreach ($notUpdatedRecords as $id) {
                $notUpdatedTable[] = ['id' => $id];
            }

            echo ConsoleHelper::table($notUpdatedTable);
        }

        echo 'All done!' . PHP_EOL;
    }

    public function actionCtaTextThankYou()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'board_thank_you.csv';
        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }
        $i = 0;
        $j = 0;
        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $model = CtaThankYou::find()
                ->where(['cta_text' => $fileop[1]])
                ->andWhere(['entity_type' => $fileop[0]])
                ->one();

            if (!$model) {
                $model = new CtaThankYou();
            }

            $model->entity_type = (int) $fileop[0];
            $model->cta_text = $fileop[1];
            $model->thank_you_text = $fileop[2];
            $model->status = CtaThankYou::STATUS_ACTIVE;

            if ($model->save()) {
                echo "{$model->entity_type} \t {$model->cta_text} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function actionUpdateAltenateCtaIdArticleNew()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'alteranate_text_article_new.csv';
        if (!file_exists($file)) {
            echo "CSV File doesn't exist!" . PHP_EOL;
            exit;
        }

        echo 'Import Started.' . PHP_EOL;

        $row = 0;
        $updatedRecords = [];
        $failedRecords = [];

        if (($handle = fopen($file, 'r')) !== false) {
            while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
                $row++;
                if ($row == 1 || empty($fileop[1])) { // Skip header and empty rows
                    continue;
                }

                try {
                    $model = LeadBucketCta::find()
                        ->where(['bucket_id' => $fileop[6]])
                        ->andWhere(['cta_id' => $fileop[4]])
                        ->andWhere(['cta_position' => $fileop[3]])
                        ->one();

                    if (!$model) {
                        echo "Skipping record: Model does not exist {$fileop[6]} \t {$fileop[3]} \t {$fileop[4]}\n";
                        continue;
                    }

                    $model->alternate_cta_text_id = $fileop[5];

                    if ($model->save()) {
                        $updatedRecords[] = [
                            'bucket_id' => $model->bucket_id,
                            'cta_id' => $model->cta_id,
                            'updated_field' => 'alternate_cta_text_id',
                            'new_value' => $fileop[5]
                        ];
                    } else {
                        $failedRecords[] = [
                            'bucket_id' => $model->bucket_id,
                            'cta_id' => $model->cta_id,
                            'errors' => json_encode($model->getErrors())
                        ];
                    }
                } catch (\Exception $e) {
                    echo "Error on row $row: " . $e->getMessage() . PHP_EOL;
                }
            }
            fclose($handle);
        }

        self::importSummaryReport($row, $failedRecords, $updatedRecords);
    }

    public function actionUpdateExamNewCtaText()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam_new_cta.csv';

        $row = 0;
        $failedRecords = [];
        $updatedRecords = [];

        if (!file_exists($file)) {
            echo "CSV File doesn't exist!" . PHP_EOL;
            echo 'Path: ' . $file . PHP_EOL;
            exit;
        }

        echo 'Import Started.' . PHP_EOL;

        if (($handle = fopen($file, 'r')) !== false) {
            while (($data = fgetcsv($handle, 2000, ',')) !== false) {
                $row++;

                if ($row == 1 || empty($data[0])) {
                    continue;
                }

                try {
                    $model = LeadBucketCta::findOne($data[0]);

                    if (!$model) {
                        echo "Skipping ID {$data[0]} - Model not found\n";
                        continue;
                    }

                    // Track changes
                    $allProcessedIds[] = $data[0];
                    $updatedFields = [];
                    $notUpdatedRecords = [];

                    // Define fields to update
                    $fieldsToUpdate = [
                        'bucket_id' => $data[1],
                        'alternate_cta_text_id' => $data[2],
                        'cta_new_name' => $data[14],
                        'lead_form_title' => $data[6],
                        'lead_form_description' => $data[7],
                        'cta_text' => $data[8] ?? $model->cta_text,
                        'cta_position' => $data[9],
                        'cta_slug' => $data[10],
                        'web' => $data[12],
                        'wap' => $data[13],
                    ];

                    foreach ($fieldsToUpdate as $field => $newValue) {
                        // dd($model, $model->$field, $newValue);
                        // if ($model->$field != $newValue) {
                        $updatedFields[$field] = [
                            'old' => $model->$field,
                            'new' => $newValue
                        ];
                        // }
                        $model->$field = $newValue;
                    }

                    if (!$model->save()) {
                        $failedRecords[] = [
                            'id' => $model->id,
                            'errors' => json_encode($model->errors)
                        ];
                    } elseif (!empty($updatedFields)) {
                        $updatedRecords[] = [
                            'id' => $model->id,
                            'updated_columns' => json_encode($updatedFields)
                        ];
                    } else {
                        $notUpdatedRecords[] = $model->id; // If no fields changed, add to not updated list
                    }

                    echo "Processing Row $row from CSV.\r";
                } catch (\Exception $e) {
                    $notUpdatedRecords[] = $model->id;
                    echo "Error on row $row: " . $e->getMessage() . PHP_EOL;
                }
            }

            self::importSummaryReport($row, $failedRecords, $updatedRecords, $notUpdatedRecords);

            fclose($handle);
        }
    }

    public function actionInsertExamAutoPop()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exams_auto_popup_cta.csv';

        $row = 0;
        $failedRecords = [];
        $updatedRecords = [];

        if (!file_exists($file)) {
            echo "CSV File doesn't exist!" . PHP_EOL;
            echo 'Path: ' . $file . PHP_EOL;
            exit;
        }

        echo 'Import Started.' . PHP_EOL;

        if (($handle = fopen($file, 'r')) !== false) {
            while (($data = fgetcsv($handle, 2000, ',')) !== false) {
                $row++;

                if ($row == 1 || empty($data[1])) { // Skip header and empty rows
                    continue;
                }

                try {
                    $model = LeadBucketCta::find()
                        ->where(['bucket_id' => $data[1]])
                        ->andWhere(['cta_position' => $data[6]])
                        ->one();

                    if (!$model) {
                        $model = new LeadBucketCta();
                    }

                    $model->bucket_id = $data[1];
                    $model->cta_new_name = $data[2];
                    $model->lead_form_title = $data[3];
                    $model->lead_form_description = $data[4];
                    $model->cta_text = $data[5];
                    $model->cta_position = $data[6];
                    $model->cta_slug = $data[7];
                    $model->web = $data[8];
                    $model->wap = $data[9];
                    $model->status = LeadBucket::STATUS_ACTIVE;

                    if ($model->save()) {
                        $insertedRecords[] = [
                            'id' => $model->id,
                            'cta_text' => $model->cta_text,
                            'cta_position' => $model->cta_position,
                            'cta_slug' => $model->cta_slug
                        ];
                    } else {
                        $failedRecords[] = [
                            'row' => $row,
                            'errors' => json_encode($model->errors)
                        ];
                    }

                    echo "Processing Row $row from CSV.\r";
                } catch (\Exception $e) {
                    echo "Error on row $row: " . $e->getMessage() . PHP_EOL;
                }
            }

            self::importSummaryReport($row, $failedRecords, $insertedRecords);
            fclose($handle);
        }
    }

    public function actionRunMediaFileExams()
    {
        $this->actionImportExamsAnswerKeys();
        $this->actionImportExamsPyqp();
        $this->actionImportExamsSamplePapers();
    }

    public function actionImportExamsAnswerKeys()
    {
        $csvFilePath = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'media_drive_exam_upload.csv';

        if (!file_exists($csvFilePath)) {
            echo "CSV file not found!\n";
            return ExitCode::DATAERR;
        }

        $localSavePath = '/var/www/html/gmu-yii-backend/frontend/web/yas/pdf/answer_key_pdfs/';
        if (!is_dir($localSavePath)) {
            mkdir($localSavePath, 0777, true);
        }

        if (($handle = fopen($csvFilePath, 'r')) !== false) {
            $i = 0;

            while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                if ($i == 0 || empty($data[5])) {
                    $i++;
                    continue;
                }

                $fileBaseUrl = $data[9]; // 'answer_key_file'
                $filePath = $data[10]; // 'actual file path'

                if (empty($filePath) || $data[6] == 'null' || $data[6] == null || empty($fileBaseUrl)) {
                    echo "Skipping row: Missing file path.\n";
                    continue;
                }

                $fileUrl = rtrim($fileBaseUrl, '/') . '/exams/' . ltrim($filePath, '/');
                $fileContent = file_get_contents($fileUrl);

                if (!$fileContent) {
                    echo "Failed to download: $fileUrl\n";
                    continue;
                }

                $newFileName = uniqid('', true) . '.pdf';
                $localFilePath = $localSavePath . $newFileName;

                file_put_contents($localFilePath, $fileContent);
                echo "File saved locally as: $localFilePath\n";

                if (filesize($localFilePath) === 0) {
                    echo "File is empty after saving! Skipping upload.\n";
                    continue;
                }

                $s3Path = DataHelper::s3Path($newFileName, 'downloadables');
                $binaryContent = file_get_contents($localFilePath);
                $uploaded = (new S3Service())->uploadBinaryFile($s3Path, $binaryContent, 'application/pdf');

                if ($uploaded) {
                    $model = new MediaDrive();
                    $model->entity = 'exam';
                    $model->entity_id = $data[5];
                    $model->page = $data[6];
                    $model->sub_page = '13';
                    $model->file_name = $newFileName;
                    $model->description = $data[11];
                    $model->year = $data[8];
                    $model->status = 1;

                    if ($model->validate() && $model->save()) {
                        echo "Successfully uploaded: $newFileName \t $model->sub_page \t $model->year \n";
                    } else {
                        echo "Failed to save metadata for: $newFileName\n";
                        print_r($model->getErrors());
                    }
                } else {
                    echo "Failed to upload to S3: $newFileName\n";
                }
                $i++;
            }
            fclose($handle);
        }

        return ExitCode::OK;
    }

    public function actionImportExamsPyqp()
    {
        $csvFilePath = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'media_drive_exam_pyqp_upload.csv';

        if (!file_exists($csvFilePath)) {
            echo "CSV file not found!\n";
            return ExitCode::DATAERR;
        }

        $localSavePath = '/var/www/html/gmu-yii-backend/frontend/web/yas/pdf/pyqp_pdfs/';
        if (!is_dir($localSavePath)) {
            mkdir($localSavePath, 0777, true);
        }

        if (($handle = fopen($csvFilePath, 'r')) !== false) {
            $i = 0;

            while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                if ($i == 0 || empty($data[5])) {
                    $i++;
                    continue;
                }

                $fileBaseUrl = $data[9]; // 'answer_key_file'
                $filePath = $data[10]; // 'actual file path'

                if (empty($filePath) || $data[6] == 'null' || $data[6] == null || empty($fileBaseUrl)) {
                    echo "Skipping row: Missing file path.\n";
                    continue;
                }

                $fileUrl = rtrim($fileBaseUrl, '/') . '/exams/' . ltrim($filePath, '/');
                $fileContent = file_get_contents($fileUrl);

                if (!$fileContent) {
                    echo "Failed to download: $fileUrl\n";
                    continue;
                }

                $newFileName = uniqid('', true) . '.pdf';
                $localFilePath = $localSavePath . $newFileName;

                file_put_contents($localFilePath, $fileContent);
                echo "File saved locally as: $localFilePath\n";

                if (filesize($localFilePath) === 0) {
                    echo "File is empty after saving! Skipping upload.\n";
                    continue;
                }

                $s3Path = DataHelper::s3Path($newFileName, 'downloadables');
                $binaryContent = file_get_contents($localFilePath);
                $uploaded = (new S3Service())->uploadBinaryFile($s3Path, $binaryContent, 'application/pdf');

                if ($uploaded) {
                    $model = new MediaDrive();
                    $model->entity = 'exam';
                    $model->entity_id = $data[5];
                    $model->page = $data[6];
                    $model->sub_page = '1';
                    $model->file_name = $newFileName;
                    $model->description = $data[11];
                    $model->year = $data[8];
                    $model->status = 1;

                    if ($model->validate() && $model->save()) {
                        echo "Successfully uploaded: $newFileName \t $model->sub_page \t $model->year \n";
                    } else {
                        echo "Failed to save metadata for: $newFileName\n";
                        print_r($model->getErrors());
                    }
                } else {
                    echo "Failed to upload to S3: $newFileName\n";
                }
                $i++;
            }
            fclose($handle);
        }

        return ExitCode::OK;
    }

    public function actionImportExamsSamplePapers()
    {
        $csvFilePath = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'media_drive_exam_sample_upload.csv';

        if (!file_exists($csvFilePath)) {
            echo "CSV file not found!\n";
            return ExitCode::DATAERR;
        }

        $localSavePath = '/var/www/html/gmu-yii-backend/frontend/web/yas/pdf/sample/';
        if (!is_dir($localSavePath)) {
            mkdir($localSavePath, 0777, true);
        }

        if (($handle = fopen($csvFilePath, 'r')) !== false) {
            $i = 0;

            while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                if ($i == 0 || empty($data[5])) {
                    $i++;
                    continue;
                }
                // dd($data);
                $fileBaseUrl = $data[9]; // 'answer_key_file'
                $filePath = $data[10]; // 'actual file path'

                if (empty($filePath) || $data[6] == 'null' || $data[6] == null || empty($fileBaseUrl)) {
                    echo "Skipping row: Missing file path.\n";
                    continue;
                }

                $fileUrl = rtrim($fileBaseUrl, '/') . '/exams/' . ltrim($filePath, '/');
                $fileContent = file_get_contents($fileUrl);

                if (!$fileContent) {
                    echo "Failed to download: $fileUrl\n";
                    continue;
                }

                $newFileName = uniqid('', true) . '.pdf';
                $localFilePath = $localSavePath . $newFileName;

                file_put_contents($localFilePath, $fileContent);
                echo "File saved locally as: $localFilePath\n";

                if (filesize($localFilePath) === 0) {
                    echo "File is empty after saving! Skipping upload.\n";
                    continue;
                }

                $s3Path = DataHelper::s3Path($newFileName, 'downloadables');
                $binaryContent = file_get_contents($localFilePath);
                $uploaded = (new S3Service())->uploadBinaryFile($s3Path, $binaryContent, 'application/pdf');

                if ($uploaded) {
                    $model = new MediaDrive();
                    $model->entity = 'exam';
                    $model->entity_id = $data[5];
                    $model->page = $data[6];
                    $model->sub_page = '8';
                    $model->file_name = $newFileName;
                    $model->description = $data[11];
                    $model->year = $data[8];
                    $model->status = 1;

                    if ($model->validate() && $model->save()) {
                        echo "Successfully uploaded: $newFileName \t $model->sub_page \t $model->year \n";
                    } else {
                        echo "Failed to save metadata for: $newFileName\n";
                        print_r($model->getErrors());
                    }
                } else {
                    echo "Failed to upload to S3: $newFileName\n";
                }
                $i++;
            }
            fclose($handle);
        }

        return ExitCode::OK;
    }

    public function actionExamWidgetDatesUpdation()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'exam_widget_date_updation.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        $updated = 0;
        $created = 0;
        $failed = 0;

        $summary = [
            'updated' => [],
            'created' => [],
            'created_count' => [],  // key: exam_id|name => count
            'failed' => []
        ];

        echo 'Import Started.....';

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[1])) {
                $i++;
                continue;
            }

            $examId = $fileop[1];
            $name = $fileop[2];

            $model = ExamDate::find()->where(['exam_id' => $examId])
                ->andWhere(['name' => $name])
                ->one();

            $isNew = false;
            if (!$model) {
                $model = new ExamDate();
                $model->exam_id = $examId;
                $model->name = $name;
                $model->type = $fileop[5];
                $model->status = ExamDate::STATUS_ACTIVE;
                $isNew = true;
            }

            $model->start = $fileop[3];
            $model->end = $fileop[4];

            if ($model->save()) {
                if ($isNew) {
                    $created++;
                    $summary['created'][] = [
                        'exam_id' => $model->exam_id,
                        'name' => $model->name,
                        'start' => $model->start,
                        'end' => $model->end,
                    ];
                    $key = $examId . ' | ' . $name;
                    if (!isset($summary['created_count'][$key])) {
                        $summary['created_count'][$key] = 0;
                    }
                    $summary['created_count'][$key]++;
                } else {
                    $updated++;
                    $summary['updated'][] = [
                        'id' => $model->id,
                        'exam_id' => $model->exam_id,
                        'name' => $model->name,
                        'start' => $model->start,
                        'end' => $model->end,
                    ];
                }
            } else {
                $failed++;
                $summary['failed'][] = [
                    'exam_id' => $examId,
                    'name' => $name,
                    'start' => $fileop[3],
                    'end' => $fileop[4],
                    'errors' => $model->getErrors(),
                ];
            }
        }

        echo "\nSummary Report:\n";
        echo "----------------------------\n";
        echo "Total Updated: $updated\n";
        echo "Total Created: $created\n";

        if (!empty($summary['created_count'])) {
            echo "\nCreated Entries (Grouped by exam_id and name):\n";
            foreach ($summary['created_count'] as $key => $count) {
                list($examId, $name) = explode('|', $key);
                echo "Exam ID: $examId, Name: $name, Count: $count\n";
            }
        }

        echo "Total Failed: $failed\n";

        if (!empty($summary['failed'])) {
            echo "\nFailed Records Details:\n";
            foreach ($summary['failed'] as $fail) {
                echo "Exam ID: {$fail['exam_id']}, Name: {$fail['name']}, Errors: " . json_encode($fail['errors']) . "\n";
            }
        }
    }

    public function actionImportCollegeProgramMap()
    {
        $this->actionAddMissingCutOffCategory();
        $this->actionCollegeProgramRankMap();
    }

    public function actionAddMissingCutOffCategory()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'institute_cut-off_category.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        $updated = 0;
        $created = 0;
        $failed = 0;

        $summary = [
            'created' => [],
            'failed' => [],
        ];

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $name = trim($fileop[0]);

            if (empty($name)) {
                continue;
            }

            $model = CutoffCategory::find()->where(['name' => $name])->one();

            if (!$model) {
                $model = new CutoffCategory();
                $model->name = $name;
                if ($model->save()) {
                    $created++;
                    $summary['created'][] = ['id' => $model->id, 'name' => $model->name];
                } else {
                    $failed++;
                    $summary['failed'][] = ['name' => $name, 'errors' => $model->getErrors()];
                }
            } else {
                $updated++;
            }
        }

        // 🧾 Summary Report
        echo "\nSummary Report:\n";
        echo "----------------------------\n";
        echo "Total Updated: $updated\n";
        echo "Total Created: $created\n";
        echo "Total Failed: $failed\n";

        if (!empty($summary['created'])) {
            echo "\nNewly Created Categories:\n";
            echo str_pad('ID', 10) . str_pad('Name', 30) . "\n";
            echo str_repeat('-', 40) . "\n";
            foreach ($summary['created'] as $item) {
                echo str_pad($item['id'], 10) . str_pad($item['name'], 30) . "\n";
            }
        }

        if (!empty($summary['failed'])) {
            echo "\nFailed Records Details:\n";
            foreach ($summary['failed'] as $fail) {
                echo "Name: {$fail['name']}, Errors: " . json_encode($fail['errors']) . "\n";
            }
        }
    }

    public function actionCollegeProgramRankMap()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'college_program_rank_map.csv';

        if (($handle = fopen($file, 'r')) == false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        // Preload data for faster lookup
        $categoryMap = ArrayHelper::map(CutoffCategory::find()->select(['id', 'name'])->asArray()->all(), 'name', 'id');

        $i = 0;
        $created = $updated = $failed = 0;
        $summary = [
            'created' => [],
            'failed' => []
        ];

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i++ == 0 || empty($fileop[0]) || empty($fileop[1]) || empty($fileop[5])) {
                continue;
            }

            $categoryName = trim($fileop[2]);

            $collegeId = $fileop[0] ?? null;
            $programId = CollegeProgram::find()->select('program_id')->where(['id' => $fileop[1]])->one();
            $categoryId = $categoryMap[$categoryName] ?? null;

            $missing = [];

            if (!$collegeId) {
                $missing[] = "College - {$collegeId}";
            }
            if (!$programId) {
                $missing[] = "Program - {$programId}";
            }
            if (!$categoryId) {
                $missing[] = "Category - {$categoryName}";
            }

            if (!empty($missing)) {
                $failed++;
                $summary['failed'][] = [
                    'missing_info' => implode(', ', $missing) ?? ''
                ];
                continue;
            }

            $college = College::findOne($collegeId);
            $feature = (new CollegeService)->getFeatures($college);
            $collegeTypeValue = !empty($feature['Institution Type']) ? ucfirst($feature['Institution Type']['value']) : '';
            $program = Program::findOne($programId['program_id']);
            $courseId = $program->programCourseMapping->course_id;
            $stateId = $college->city->state_id;

            $model = InstituteProgramRankMapping::find()
                ->where([
                    'college_id' => $collegeId,
                    'program_id' => $programId,
                    'cutoff_category_id' => $categoryId,
                    'round' => $fileop[4]
                ])
                ->one();

            $isNew = false;

            if (!$model) {
                $model = new InstituteProgramRankMapping();
                $isNew = true;
            }

            $examId = Exam::find()->where(['slug' => 'jee-main'])->one();

            $model->exam_id = $examId->id;
            $model->college_id = (int) $collegeId ?? '';
            $model->program_id = $programId['program_id'] ?? '';
            $model->course_id = $courseId ?? '';
            $model->highest_rank = $fileop[3];
            $model->lowest_rank = $fileop[4];
            $model->round = $fileop[6];
            $model->state_id = $stateId ?? '';
            $model->year = 2024;
            $model->cutoff_category_id = $categoryId;
            $model->college_type = ucfirst($collegeTypeValue) ?? '';
            $model->status = InstituteProgramRankMapping::STATUS_ACTIVE;

            if ($model->save()) {
                echo "Saved ID: {$model->id}\n";
                if ($isNew) {
                    $created++;
                    $summary['created'][] = [
                        'id' => $model->id,
                        'college' => $college->name,
                        'program' => $program->name,
                        'category' => $categoryName,
                        'round' => $fileop[4]
                    ];
                } else {
                    $updated++;
                }
            } else {
                $failed++;
                $summary['failed'][] = [
                    'college' => $college->name,
                    'program' => $program->name,
                    'category' => $categoryName,
                    'errors' => $model->getErrors()
                ];
            }
        }

        // Summary report
        echo "\nSummary Report:\n";
        echo "----------------------------\n";
        echo "Total Updated: $updated\n";
        echo "Total Created: $created\n";
        echo "Total Failed: $failed\n";

        if (!empty($summary['created'])) {
            echo "\nCreated Records:\n";
            echo "ID\tCollege\tProgram\tCategory\tRound\n";
            foreach ($summary['created'] as $row) {
                echo "{$row['id']}\t{$row['college']}\t{$row['program']}\t{$row['category']}\t{$row['round']}\n";
            }
        }

        if (!empty($summary['failed'])) {
            echo "\nFailed Records:\n";
            foreach ($summary['failed'] as $fail) {
                if (isset($fail['missing_info'])) {
                    echo "Missing: {$fail['missing_info']}\n";
                } else {
                    echo "No failed Records\n";
                }
            }
        }
    }

    public function actionUpdateExamParentIdSeoTable()
    {
        $success = [];
        $failed = [];

        $examContent = ExamContent::find()
            ->select(['id', 'exam_id', 'slug', 'parent_id'])
            ->where(['NOT', ['parent_id' => null]])
            ->all();

        foreach ($examContent as $content) {
            $seoModel = SeoInfo::find()
                ->where(['entity' => SeoInfo::ENTITY_EXAM, 'entity_id' => $content->exam_id, 'page' => $content->slug])
                ->andWhere(['parent_id' => null])
                ->one();

            if (!$seoModel) {
                continue;
            }

            $seoModel->parent_id = $content->parent_id;

            if ($seoModel->save()) {
                $success[] = [
                    'SeoInfo ID' => $seoModel->id,
                    'Exam ID' => $content->exam_id,
                    'Slug' => $content->slug,
                    'Parent ID Set' => $content->parent_id,
                ];
            } else {
                $failed[] = [
                    'Exam ID' => $content->exam_id,
                    'Slug' => $content->slug,
                    'Errors' => $seoModel->getErrors(),
                ];
            }
        }
        echo "\n=== Successful Updates ===\n";
        foreach ($success as $row) {
            echo "SeoInfo ID: {$row['SeoInfo ID']}, Exam ID: {$row['Exam ID']}, Slug: {$row['Slug']}, Parent ID: {$row['Parent ID Set']}\n";
        }

        echo "\n=== Failed Updates ===\n";
        foreach ($failed as $row) {
            echo "Exam ID: {$row['Exam ID']}, Slug: {$row['Slug']}, Errors: " . json_encode($row['Errors']) . "\n";
        }

        echo "\nSummary: \n";
        echo 'Total Records Processed: ' . count($examContent) . "\n";
        echo 'Successful Updates: ' . count($success) . "\n";
        echo 'Failed Updates: ' . count($failed) . "\n";
    }
}
