<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%seo_info}}`.
 */
class m250602_122515_create_new_index_seo_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createIndex(
            '{{%idx-unique-seo_info-entity-entity_id-page-parent_id}}',
            '{{%seo_info}}',
            ['entity', 'entity_id', 'page', 'parent_id'],
            true
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('{{%idx-unique-seo_info-entity-entity_id-page-parent_id}}', '{{%seo_info}}');
    }
}
