<?php

use yii\db\Migration;

/**
 * Class m250522_201031_alter_institute_program_rank_mapping_table
 */
class m250522_201031_alter_institute_program_rank_mapping_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%institute_program_rank_mapping}}', 'exam_id', $this->integer()->defaultValue(null) . ' AFTER id');

        $this->createIndex(
            '{{%idx-institute_program_rank_mapping-exam_id}}',
            '{{%institute_program_rank_mapping}}',
            'exam_id'
        );

        $this->addForeignKey(
            '{{%fk-institute_program_rank_mapping-exam_id}}',
            '{{%institute_program_rank_mapping}}',
            'exam_id',
            '{{%exam}}',
            'id',
            'CASCADE'
        );
    }

     /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey(
            '{{%fk-institute_program_rank_mapping-exam_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        $this->dropIndex(
            '{{%idx-institute_program_rank_mapping-exam_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        $this->dropColumn('{{%institute_program_rank_mapping}}', 'exam_id');
    }
}
