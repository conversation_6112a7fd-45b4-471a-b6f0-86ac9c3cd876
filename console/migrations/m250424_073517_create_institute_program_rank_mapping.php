<?php

use yii\db\Migration;

/**
 * Class m250424_073517_create_institute_program_rank_mapping
 */
class m250424_073517_create_institute_program_rank_mapping extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%institute_program_rank_mapping}}', [
            'id' => $this->primaryKey(),
            'college_id' => $this->integer()->notNull(),
            'program_id' => $this->integer()->notNull(),
            'course_id' => $this->integer(),
            'highest_rank' => $this->integer(),
            'lowest_rank' => $this->integer(),
            'round' => $this->integer(),
            'state_id' => $this->integer(),
            'year' => $this->integer(),
            'cutoff_category_id' => $this->integer(),
            'college_type' => $this->string(),
            'status' => $this->smallInteger()->notNull()->defaultValue(0),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ], $options);

        // creates index for column `college_id`
        $this->createIndex(
            '{{%idx-institute_program_rank_mapping-college_id}}',
            '{{%institute_program_rank_mapping}}',
            'college_id'
        );

        // add foreign key for table `{{%college}}`
        $this->addForeignKey(
            '{{%fk-institute_program_rank_mapping-college_id}}',
            '{{%institute_program_rank_mapping}}',
            'college_id',
            '{{%college}}',
            'id',
            'CASCADE'
        );

        // creates index for column `program_id`
        $this->createIndex(
            '{{%idx-institute_program_rank_mapping-program_id}}',
            '{{%institute_program_rank_mapping}}',
            'program_id'
        );

        // add foreign key for table `{{%program}}`
        $this->addForeignKey(
            '{{%fk-institute_program_rank_mapping-program_id}}',
            '{{%institute_program_rank_mapping}}',
            'program_id',
            '{{%program}}',
            'id',
            'CASCADE'
        );

        // creates index for column `cutoff_category_id`
        $this->createIndex(
            '{{%idx-institute_program_rank_mapping-cutoff_category_id}}',
            '{{%institute_program_rank_mapping}}',
            'cutoff_category_id'
        );

        // add foreign key for table `{{%cutoff_category}}`
        $this->addForeignKey(
            '{{%fk-institute_program_rank_mapping-cutoff_category_id}}',
            '{{%institute_program_rank_mapping}}',
            'cutoff_category_id',
            '{{%cutoff_category}}',
            'id',
            'CASCADE'
        );

        // creates index for column `state_id`
        $this->createIndex(
            '{{%idx-institute_program_rank_mapping-state_id}}',
            '{{%institute_program_rank_mapping}}',
            'state_id'
        );

        // add foreign key for table `{{%state}}`
        $this->addForeignKey(
            '{{%fk-institute_program_rank_mapping-state_id}}',
            '{{%institute_program_rank_mapping}}',
            'state_id',
            '{{%state}}',
            'id',
            'CASCADE'
        );

        // creates index for column `course_id`
        $this->createIndex(
            '{{%idx-institute_program_rank_mapping-course_id}}',
            '{{%institute_program_rank_mapping}}',
            'course_id'
        );

        // add foreign key for table `{{%course}}`
        $this->addForeignKey(
            '{{%fk-institute_program_rank_mapping-course_id}}',
            '{{%institute_program_rank_mapping}}',
            'course_id',
            '{{%course}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // drops foreign key for table `{{%college}}`
        $this->dropForeignKey(
            '{{%fk-institute_program_rank_mapping-college_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        // drops index for column `college_id`
        $this->dropIndex(
            '{{%idx-institute_program_rank_mapping-college_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        // drops foreign key for table `{{%program}}`
        $this->dropForeignKey(
            '{{%fk-institute_program_rank_mapping-program_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        // drops index for column `program_id`
        $this->dropIndex(
            '{{%idx-institute_program_rank_mapping-program_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        // drops foreign key for table `{{%cutoff_category}}`
        $this->dropForeignKey(
            '{{%fk-institute_program_rank_mapping-cutoff_category_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        // drops index for column `cutoff_category_id`
        $this->dropIndex(
            '{{%idx-institute_program_rank_mapping-cutoff_category_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        // drops foreign key for table `{{%state}}`
        $this->dropForeignKey(
            '{{%fk-institute_program_rank_mapping-state_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        // drops index for column `state_id`
        $this->dropIndex(
            '{{%idx-institute_program_rank_mapping-state_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        // drops foreign key for table `{{%course}}`
        $this->dropForeignKey(
            '{{%fk-institute_program_rank_mapping-course_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        // drops index for column `course_id`
        $this->dropIndex(
            '{{%idx-institute_program_rank_mapping-course_id}}',
            '{{%institute_program_rank_mapping}}'
        );

        $this->dropTable('{{%institute_program_rank_mapping}}');
    }
}
