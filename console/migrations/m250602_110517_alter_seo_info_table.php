<?php

use yii\db\Migration;

/**
 * Class m250602_110517_alter_seo_info_table
 */
class m250602_110517_alter_seo_info_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%seo_info}}', 'parent_id', $this->integer()->defaultValue(null) . ' AFTER entity');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%seo_info}}', 'parent_id');
    }
}
