
<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%new_index_for_exam_content}}`.
 */
class m250603_101557_create_new_index_for_exam_content_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createIndex(
            '{{%idx-unique-exam_content-exam_id-slug-parent_id}}',
            '{{%exam_content}}',
            ['exam_id', 'slug', 'parent_id'],
            true
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex('{{%idx-unique-exam_content-exam_id-slug-parent_id}}', '{{%exam_content}}');
    }
}
