<?php

namespace common\event;

use common\helpers\DataHelper;
use common\models\CollegeContent;
use common\models\documents\Sitemap;
use common\helpers\CollegeHelper;
use common\models\Article;
use common\models\BoardContent;
use common\models\Category;
use common\models\College;
use common\models\CollegeCourseContent;
use common\models\CollegeProgram;
use common\models\Course;
use common\models\CourseContent;
use common\models\ExamContent;
use common\models\Program;
use common\services\SitemapService;
use frontend\services\ExamService;
use common\models\SitemapUpdate;
use common\models\Career;
use common\models\Board;
use common\models\NewsSubdomain;

class SitemapEvent
{
    const DOMAIN = 'https://www.getmyuni.com';
    const CHANGE_FREQ_DAILY = 'daily';
    const CHANGE_FREQ_MONTHLY = 'monthly';

    /** Update the sitemap collection
     * @param $collegeId | College ID
     * @param $slug | college page slug
     */
    public function updateCollegeSitemap($collegeId, $slug = null)
    {

        $query = CollegeContent::find()->with('college')->where(['entity_id' => $collegeId]);
        if (!empty($slug)) {
            $collegePages = $query->andWhere(['sub_page' => $slug]);
        }
        $collegePages = $query->all();

        if (!empty($collegePages)) {
            foreach ($collegePages as $page) {
                $url = CollegeHelper::collegeUrlFormate($page->college->slug, $page->sub_page);
                $model = Sitemap::find()
                    ->where(['slug' => $url])
                    ->andWhere(['subPage' => $page->sub_page])
                    ->one();

                if (!$model) {
                    $model = new Sitemap();
                }

                $model->entity = Sitemap::ENTITY_COLLEGE;
                $model->domain = self::DOMAIN;
                $model->slug = $url;
                $model->subPage = $page->sub_page;
                $model->priority = '1.0';
                $model->changeFreq = self::CHANGE_FREQ_DAILY;
                $model->lastModified = date(DATE_ATOM, strtotime($page->updated_at));
                $model->status = $page->college->status == Sitemap::STATUS_INACTIVE ? $page->college->status : $page->status;

                if ($model->save()) {
                    // echo "{$model->slug} \t {$model->entity} \n";
                } else {
                    print_r($model->getErrors());
                }
            }
        }
    }

    /** Update the sitemap pi collection
     * @param $collegeId | College ID
     *
     */
    public function updateCollegeProgramSitemap($collegeProgram, $collegeSlug, $collegeStatus)
    {
        if ($collegeProgram->updated_at instanceof \yii\db\Expression) {
            $timeStamp = date('Y-m-d H:i:s');
            $date = date(DATE_ATOM, strtotime($timeStamp));
        } else {
            $date = date(DATE_ATOM, strtotime($collegeProgram->updated_at));
        }

        $program = Program::find()->where(['id' => $collegeProgram->program_id])->one();

        if (!empty($program) && !empty($collegeProgram->course) && $collegeProgram->course->status == Course::STATUS_ACTIVE) {
            $url = CollegeHelper::collegeUrlFormate($collegeSlug, 'pi', $program->slug);
            
            $model = SitemapUpdate::find()
            ->where(['slug' => $url])
            ->andWhere(['sub_page' => 'pi'])
            ->one();
            // dd($model);
            $this->saveSitemapUpdate($model, Sitemap::ENTITY_COLLEGE, $url, 'pi', $collegeProgram->status, $collegeProgram->updated_at);

            // $model = Sitemap::find()
            //     ->where(['slug' => $url])
            //     ->one();

            // if (!$model) {
            //     $model = new Sitemap();
            // }

            // $model->entity = Sitemap::ENTITY_COLLEGE;
            // $model->domain = self::DOMAIN;
            // $model->slug = $url;
            // $model->subPage = 'pi';
            // $model->priority = '0.5';
            // $model->changeFreq = self::CHANGE_FREQ_MONTHLY;
            // $model->lastModified = $date;
            // $model->status = $collegeProgram->page_index == 0 || $collegeStatus == College::STATUS_INACTIVE || $collegeStatus == College::STATUS_IS_DELETED ? CollegeProgram::STATUS_INACTIVE : (int) $collegeProgram->status;

            // if ($model->save()) {
            //     echo "{$collegeProgram->id} \t {$model->slug} \t {$model->entity} \n";
            // } else {
            //     print_r($model->getErrors());
            // }
        }
    }

    /** Update the sitemap ci collection
     * @param $collegeId | College ID
     *
     */
    public function updateCollegeCourseSitemap($collegeCourse, $collegeSlug, $collegeStatus)
    {
        if ($collegeCourse->updated_at instanceof \yii\db\Expression) {
            $timeStamp = date('Y-m-d H:i:s');
            $date = date(DATE_ATOM, strtotime($timeStamp));
        } else {
            $date = date(DATE_ATOM, strtotime($collegeCourse->updated_at));
        }

        $course = Course::find()->where(['id' => $collegeCourse->course_id])->andWhere(['status' => Course::STATUS_ACTIVE])->one();

        if (!empty($course)) {
            $url = CollegeHelper::collegeUrlFormate($collegeSlug, 'ci', $course->slug);
            $model = Sitemap::find()
                ->where(['slug' => $url])
                ->one();

            if (!$model) {
                $model = new Sitemap();
            }

            $model->entity = Sitemap::ENTITY_COLLEGE;
            $model->domain = self::DOMAIN;
            $model->slug = $url;
            $model->subPage = 'ci';
            $model->priority = '0.5';
            $model->changeFreq = self::CHANGE_FREQ_MONTHLY;
            $model->lastModified = $date;
            $model->status = $collegeCourse->page_index == 0 || $collegeStatus == College::STATUS_INACTIVE || $collegeStatus == College::STATUS_IS_DELETED ? CollegeCourseContent::STATUS_INACTIVE : (int) $collegeCourse->status;

            if ($model->save()) {
                echo "{$collegeCourse->id} \t {$model->slug} \t {$model->entity} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function updateCollegeCourseSitemapMysl($collegeCourse, $collegeSlug, $collegeStatus)
    {
        if ($collegeCourse->updated_at instanceof \yii\db\Expression) {
            $timeStamp = date('Y-m-d H:i:s');
            $date = date(DATE_ATOM, strtotime($timeStamp));
        } else {
            $date = date(DATE_ATOM, strtotime($collegeCourse->updated_at));
        }

        $course = Course::find()->where(['id' => $collegeCourse->course_id])->andWhere(['status' => Course::STATUS_ACTIVE])->one();

        if (!empty($course)) {
            $url = CollegeHelper::collegeUrlFormate($collegeSlug, 'ci', $course->slug);

            $model = SitemapUpdate::find()
            ->where(['slug' => $url])
            ->andWhere(['sub_page' => 'ci'])
            ->one();
            // dd($model);
            $this->saveSitemapUpdate($model, Sitemap::ENTITY_COLLEGE, $url, 'ci', $collegeCourse->status, $collegeCourse->updated_at);

            // $model = SitemapUpdate::find()
            //     ->where(['slug' => $url])
            //     ->one();

            // if (!$model) {
            //     $model = new Sitemap();
            // }

            // $model->entity = Sitemap::ENTITY_COLLEGE;
            // $model->domain = self::DOMAIN;
            // $model->slug = $url;
            // $model->subPage = 'ci';
            // $model->priority = '0.5';
            // $model->changeFreq = self::CHANGE_FREQ_MONTHLY;
            // $model->lastModified = $date;
            // $model->status = $collegeCourse->page_index == 0 || $collegeStatus == College::STATUS_INACTIVE || $collegeStatus == College::STATUS_IS_DELETED ? CollegeCourseContent::STATUS_INACTIVE : (int) $collegeCourse->status;

            // if ($model->save()) {
            //     echo "{$collegeCourse->id} \t {$model->slug} \t {$model->entity} \n";
            // } else {
            //     print_r($model->getErrors());
            // }
        }
    }


    /**
     * Update Article to sitemap collection
     * $slug Article Slug
     */
    public function updateArticleSitemap($slug)
    {
        $article = Article::find()->where(['slug' => $slug])->byExcludeCategory(Category::EXCLUDE_CATEGORY)->one();

        if (empty($article)) {
            return false;
        }

        $model = Sitemap::find()->where(['slug' => $article->slug])->one();

        if (!$model) {
            $model = new Sitemap();
        }

        $model->entity = ($article->entity == 'study-abroad') ? Sitemap::ENTITY_STUDY_ABROAD : Sitemap::ENTITY_ARTICLE;
        $model->domain = self::DOMAIN;
        $model->slug = $article->slug;
        $model->country = !empty($article->country_slug) ? $article->country_slug : '';
        $model->priority = '1.0';
        $model->lang_code = isset($article->lang_code) ? $article->lang_code : DataHelper::$languageCode['en'];
        $model->changeFreq = self::CHANGE_FREQ_DAILY;
        $model->lastModified = date(DATE_ATOM, strtotime($article->updated_at));
        $model->status = $article->status;

        if ($model->save()) {
            return true;
            echo "{$model->slug} \t {$model->entity} \n";
        } else {
            print_r($model->getErrors());
        }
    }

    /** Update the sitemap collection
     * @param $examId Exam ID
     * @param $slug Exam page slug
     */
    public function updateExamSitemap($examId, $slug = null)
    {
        $examService = new ExamService();

        $query = ExamContent::find()->with('exam')->where(['exam_id' => $examId]);
        if (!empty($slug)) {
            $examPages = $query->andWhere(['slug' => $slug]);
        }
        $examPages = $query->all();

        if (empty($examPages)) {
            return false;
        }

        foreach ($examPages as $page) {
            if ($page->parent_id != null) {
                $examContent = ExamContent::findOne($page->parent_id);
                if ($examContent->slug == 'syllabus') {
                    $url = $examService->examUrlFormate($page->exam->slug, $page->slug);
                    $url = $url . '-syllabus';
                } else {
                    $url = $examService->examUrlFormate($page->exam->slug, $page->slug);
                }
            } else {
                $url = $examService->examUrlFormate($page->exam->slug, $page->slug);
            }
            $model = Sitemap::find()->where(['slug' => $url])->one();

            if (!$model) {
                $model = new Sitemap();
            }

            $model->entity = Sitemap::ENTITY_EXAM;
            $model->domain = self::DOMAIN;
            $model->slug = $url;
            $model->priority = '1.0';
            $model->lang_code = isset($page->exam->lang_code) ? $page->exam->lang_code : DataHelper::$languageCode['en'];
            $model->changeFreq = self::CHANGE_FREQ_DAILY;
            $model->lastModified = date(DATE_ATOM, strtotime($page->updated_at));
            $model->status = $page->exam->status == Sitemap::STATUS_INACTIVE ? $page->exam->status : $page->status;

            if ($model->save()) {
                echo "{$model->slug} \t {$model->entity} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    /** Update the sitemap collection
     * @param $boardId Board ID
     * @param $slug  board page slug
     */
    public function updateBoardSitemap($boardId, $pageSlug = null)
    {
        $b = Board::findOne($boardId);
        // dd($b);
        $query = BoardContent::find()->with('board')->where(['board_id' => $boardId]);
        if (!empty($pageSlug)) {
            $boardPages = $query->andWhere(['page_slug' => $pageSlug]);
        }

        $boardPages = $query->all();

        if (empty($boardPages)) {
            return false;
        }

        foreach ($boardPages as $page) {
            $b = Board::findOne($page['board_id']);
            if ($page->page_slug == 'overview') {
                $url = $b->slug;
            } else {
                if ($page->parent_id != null) {
                    $boardContent = BoardContent::findOne($page['parent_id']);
                    if ($boardContent->page_slug == 'supplementary') {
                        $url = $page->board->slug . '-' . $boardContent->page_slug . '-' . $page->page_slug;
                    } else {
                        $url = $page->board->slug . '-' . $page->page_slug . '-' . $boardContent->page_slug;
                    }
                } else {
                    $url = $b->slug . '-' . $page->page_slug;
                }
            }
            $model = Sitemap::find()
                ->where(['slug' => $url])
                ->andWhere(['subPage' => $page->page_slug])
                ->andWhere(['lang_code' => $page->lang_code])
                ->one();

            if (!$model) {
                $model = new Sitemap();
            }

            $model->entity = Sitemap::ENTITY_BOARD;
            $model->domain = self::DOMAIN;
            $model->slug = $url;
            $model->subPage = $page->page_slug;
            $model->priority = '1.0';
            $model->lang_code = isset($b->lang_code) ? $b->lang_code : DataHelper::$languageCode['en'];
            $model->changeFreq = self::CHANGE_FREQ_DAILY;
            $model->lastModified = date(DATE_ATOM, strtotime($page->updated_at));
            $model->status = $b->status == Sitemap::STATUS_INACTIVE ? $b->status : $page->status;

            if ($model->save()) {
                echo "{$model->slug} \t {$model->entity} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    /** Update the sitemap collection
     * @param $courseID Course ID
     * @param $slug  course page slug
     */
    public function updateCourseSitemap($courseId, $pageSlug = null)
    {
        $sitemapService = new SitemapService();
        $query = CourseContent::find()->with('course')->where(['course_id' => $courseId]);
        if (!empty($pageSlug)) {
            $coursePages = $query->andWhere(['page' => $pageSlug]);
        }
        $coursePages = $query->all();

        if (empty($coursePages)) {
            return false;
        }

        foreach ($coursePages as $page) {
            if ($page->page == 'about') {
                $url = $page->course->slug . '-course';
            } else {
                $url = $page->course->slug . '-' . $page->page;
            }

            $model = $sitemapService->getData($url, Sitemap::ENTITY_COURSE, $page->page);

            if (!$model) {
                $model = new Sitemap();
            }

            $model->entity = Sitemap::ENTITY_COURSE;
            $model->domain = self::DOMAIN;
            $model->slug = $url;
            $model->subPage = $page->page;
            $model->priority = '1.0';
            $model->changeFreq = self::CHANGE_FREQ_DAILY;
            $model->lastModified = date(DATE_ATOM, strtotime($page->updated_at));
            $model->status = $page->course->status == Sitemap::STATUS_INACTIVE ? $page->course->status : $page->status;

            if ($model->save()) {
                echo "{$model->slug} \t {$model->entity} \n";
            } else {
                print_r($model->getErrors());
            }
        }
    }

    public function updateCollegeUpdateXml($collegeId, $status, $slug = null)
    {
        $query = CollegeContent::find()->with('college')->where(['entity_id' => $collegeId]);
        if (!empty($slug)) {
            $collegePages = $query->andWhere(['sub_page' => $slug]);
        }
        $collegePages = $query->all();
        if (!empty($collegePages)) {
            foreach ($collegePages as $page) {
                if ($page->parent_id != null) {
                    $collegeContent = CollegeContent::find()->where(['id' => $page->parent_id])->one();
                    $url = CollegeHelper::collegeDropDownUrlFormate($page->college->slug, $page->sub_page, $collegeContent->sub_page);
                } else {
                    $url = CollegeHelper::collegeUrlFormate($page->college->slug, $page->sub_page);
                }

                $url = 'college/' . $url;

                $model = SitemapUpdate::find()
                    ->where(['slug' => $url])
                    ->andWhere(['sub_page' => $page->sub_page])
                    ->one();
                // dd($model);
                $this->saveSitemapUpdate($model, Sitemap::ENTITY_COLLEGE, $url, $page->sub_page, $status);
            }
        }
    }

    public function updateCourseUpdateXml($courseId, $status, $pageSlug = null)
    {
        $query = CourseContent::find()->with('course')->where(['course_id' => $courseId]);
        if (!empty($pageSlug)) {
            $coursePages = $query->andWhere(['page' => $pageSlug]);
        }
        $coursePages = $query->all();

        if (empty($coursePages)) {
            return false;
        }
        foreach ($coursePages as $page) {
            if ($page->parent_id != null) {
                $final = preg_replace('#[ -]+#', '-', $page->page);
                $courseContent = CourseContent::find()->where(['id' => $page->parent_id])->one();
                $url = $page->course->slug . '-' . $courseContent->page . '/' . strtolower($final);
            } else {
                if ($page->page == 'about') {
                    $url = $page->course->slug . '-course';
                } else {
                    $url = $page->course->slug . '-' . $page->page;
                }
            }

            $model = SitemapUpdate::find()
                ->where(['slug' => $url])
                ->andWhere(['sub_page' => $pageSlug])
                ->one();

            $this->saveSitemapUpdate($model, Sitemap::ENTITY_COURSE, $url, $page->page, $status);
        }
    }

    public function updateBoardUpdateXml($boardId, $status, $pageSlug = null)
    {
        $query = BoardContent::find()->with('board')->where(['board_id' => $boardId]);
        if (!empty($pageSlug)) {
            $boardPages = $query->andWhere(['page_slug' => $pageSlug]);
        }
        $boardPages = $query->one();

        if (empty($boardPages)) {
            return false;
        }

        if ($boardPages->page_slug == 'overview') {
            if (empty($page->board->slug)) {
                return false;
            }

            $url = $boardPages->board->slug;
        } else {
            if ($boardPages->parent_id != null) {
                $boardContent = BoardContent::findOne($boardPages->parent_id);
                if ($boardContent->page_slug == 'supplementary') {
                    $url = $boardPages->board->slug . '-' . $boardContent->page_slug . '-' . $boardPages->page_slug;
                } else {
                    $url = $boardPages->board->slug . '-' . $boardPages->page_slug . '-' . $boardContent->page_slug;
                }
            } else {
                $url = $boardPages->board->slug . '-' . $boardPages->page_slug;
            }
        }
        $url .= '/b';
        $model = SitemapUpdate::find()
            ->where(['slug' => $url])
            ->andWhere(['sub_page' => $boardPages->page_slug])
            ->andWhere(['lang_code' => $boardPages->lang_code])
            ->one();

        $this->saveSitemapUpdate($model, Sitemap::ENTITY_BOARD, $url, $boardPages->page_slug, $status);
    }

    public function updateExamUpdateXml($examId, $status, $slug = null)
    {
        $examService = new ExamService();

        $query = ExamContent::find()->with('exam')->where(['exam_id' => $examId]);
        if (!empty($slug)) {
            $examPages = $query->andWhere(['slug' => $slug]);
        }
        $examPages = $query->all();

        if (empty($examPages)) {
            return false;
        }

        foreach ($examPages as $page) {
            if ($page->parent_id != null) {
                $examContent = ExamContent::findOne($page->parent_id);
                if ($examContent->slug == 'syllabus') {
                    $url = $examService->examUrlFormate($page->exam->slug, $page->slug);
                    $url = $url . '-syllabus';
                } else {
                    $url = $examService->examUrlFormate($page->exam->slug, $page->slug);
                }
            } else {
                $url = $examService->examUrlFormate($page->exam->slug, $page->slug);
            }
            $url = 'exams/' . $url;
            $model = SitemapUpdate::find()->where(['slug' => $url])->one();

            $this->saveSitemapUpdate($model, Sitemap::ENTITY_EXAM, $url, $page->slug, $status);
        }
    }

    public function updateCareerUpdateXml($carrerId, $status, $page = null)
    {
        $carrer = Career::findOne($carrerId);

        if ($page == 'salary') {
            $url = 'careers/' . $carrer->slug . '-salary';
        } else {
            $url = 'careers/' . $carrer->slug;
        }

        $model = SitemapUpdate::find()->where(['slug' => $url])->one();

        $this->saveSitemapUpdate($model, Sitemap::ENTITY_CAREER, $url, $page, $status);
    }

    public function updateNewsUpdateXml($newsId, $status, $updatedAt = null, $publishedAt = null)
    {
        $news = NewsSubdomain::findOne($newsId);
        
        $statusVale = $status;
        
        if ($news->status == 1) {
            $statusVale = $status;
        }

        $model = SitemapUpdate::find()->where(['slug' => $news->slug])->one();
        // dd($publishedAt);
        $this->saveSitemapUpdate($model, Sitemap::ENTITY_NEWS, $news->slug, 'news', $statusVale, $updatedAt, $publishedAt);
    }

    public function updateArticleUpdateXml($slug, $status)
    {
        $url = 'articles/' . $slug;
        $model = SitemapUpdate::find()->where(['slug' => $url])->one();

        $this->saveSitemapUpdate($model, Sitemap::ENTITY_ARTICLE, $url, 'article', $status);
    }

    public function updateNcertUpdateXml($slug, $status)
    {
        $url = 'ncert/' . $slug;
        $model = SitemapUpdate::find()->where(['slug' => $url])->one();

        $this->saveSitemapUpdate($model, Sitemap::ENTITY_NCERT, $url, 'ncert', $status);
    }

    private function saveSitemapUpdate($model, $entity, $url, $subPage, $status, $updatedAt = null, $publishedAt = null)
    {
        if (!$model) {
            $model = new SitemapUpdate();
        } else {
            $model->touch('updated_at');
        }

        if ($entity == 'news') {
            $domain = 'https://news.getmyuni.com';
            $model->lang_code = 1;
        } else {
            $domain = self::DOMAIN;
        }

        // if (!empty($updatedAt) && strtotime($model->sitemap_update) <= strtotime($updatedAt)) {
        //     $updatedAt = $model->sitemap_update;
        // } else {
        //     $updatedAt = $updatedAt;
        // }

        // if (!empty($publishedAt) && strtotime($model->published_at) <= strtotime($publishedAt)) {
        //     $publishedAt = $model->published_at;
        // } else {
        //     $publishedAt = $publishedAt;
        // }

        $model->entity = $entity;
        $model->domain = $domain;
        $model->slug = $url;
        $model->sub_page = $subPage;
        $model->priority = '1.0';
        $model->change_freq = self::CHANGE_FREQ_DAILY;
        $model->sitemap_update = $updatedAt ?? null;
        if (!empty($publishedAt)) {
            $model->published_at = $publishedAt;
        }
        // } else {
        //     $model->published_at = $model->published_at;
        // }
        $model->status = $status;
        // dd($model);
        if ($model->save()) {
            return true;
            echo "{$model->slug} \t {$model->entity} \n";
        } else {
            print_r($model->getErrors());
        }
    }
}
