<?php

namespace common\models;

use common\event\SitemapEvent;
use common\helpers\DataHelper;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use common\models\query\ExamContentQuery;
use yii\behaviors\SluggableBehavior;
use common\models\User;
use common\services\CacheClearService;
use common\services\UserService;

/**
 * This is the model class for table "exam_content".
 *
 * @property int $id
 * @property int $exam_id
 * @property string $name
 * @property string $slug
 * @property string $content
 * @property int $type
 * @property int $status
 * @property string $created_at
 * @property string $updated_at
 *
 * @property Exam $exam
 */
class ExamContent extends \yii\db\ActiveRecord
{

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;
    const STATUS_DRAFT = 2;
    const SCENARIO_IMPORTER = 'importer';

    const IS_FREELANCER_NO = 0;
    const IS_FREELANCER_YES = 1;

    public $preview;
    public $skipAfterSave = false;

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'updatedAtAttribute' => false,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // [
            //     'class' => SluggableBehavior::class,
            //     'attribute' => 'name',
            // ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'exam_content';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['exam_id', 'name', 'slug', 'localize_year', 'author_id'], 'required', 'except' => self::SCENARIO_IMPORTER],
            [['content'], 'required', 'when' => function ($model) {
                return $model->is_freelancer == ExamContent::IS_FREELANCER_NO;
            }, 'whenClient' => "function (attribute, value) {
                if($('#update-validation').val()==1){
                    return true;
                }
                return $('#examcontent-is_freelancer').val() == '0';
            }", 'except' => self::SCENARIO_IMPORTER],
            [['exam_id', 'status', 'author_id', 'localize_year', 'parent_id', 'lang_code', 'is_freelancer'], 'integer'],
            [['content', 'editor_remark'], 'string'],
            [['name', 'slug'], 'string', 'max' => 255],
            [['slug', 'exam_id', 'parent_id'], 'unique', 'targetAttribute' => ['slug', 'exam_id', 'parent_id']],
            [['exam_id'], 'exist', 'skipOnError' => true, 'targetClass' => Exam::className(), 'targetAttribute' => ['exam_id' => 'id']],
            [['published_at', 'created_at', 'updated_at', 'created_by', 'updated_by'], 'safe']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'author_id' => 'Author ID',
            'parent_id' => 'Sub Page',
            'exam_id' => 'Exam ID',
            'name' => 'Name',
            'slug' => 'Slug',
            'content' => 'Content',
            'type' => 'Type',
            'localize_year' => 'Localize Year',
            'status' => 'Status',
            'lang_code' => 'language code',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'published_at' => 'Published At',
        ];
    }

    /**
     * Gets query for [[Exam]].
     *
     * @return \yii\db\ActiveQuery|ExamQuery
     */
    public function getExam()
    {
        return $this->hasOne(Exam::className(), ['id' => 'exam_id']);
    }

    public function examname()
    {
        return $this->exam->name;
    }

    /**
     * {@inheritdoc}
     * @return ExamContentQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new ExamContentQuery(get_called_class());
    }

    public function getUser()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id']);
        // ->where(['not', ['user.status' => User::STATUS_DELETED]]);
    }

    public function getDefaultuser()
    {
        return UserService::getDefaultUserData();
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getBackendauthor()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id']);
    }

    // /**
    //  * Undocumented function
    //  *
    //  * @return void
    //  */
    public function beforeValidate()
    {
        $oldName = $this->name ?? null;
        $contentList = DataHelper::examContentList();
        $this->name = $contentList[$this->slug] ?? null;
        if ($this->name == null && $oldName != null && $this->parent_id != null) {
            $examContentModel = ExamContent::findOne($this->parent_id);
            $dropDownArr = DataHelper::getExamDropDownSubPages();
            if (in_array($oldName, $dropDownArr[$examContentModel->slug])) {
                $this->name = $oldName;
            }
        }

        return parent::beforeValidate();
    }

    public function beforeSave($insert)
    {
        // if ($insert) {
        // $this->author_id = isset(Yii::$app->user) ? Yii::$app->user->id : null;
        // }
        return parent::beforeSave($insert);
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($this->skipAfterSave) {
            return;
        }

        (new SitemapEvent())->updateExamSitemap($this->exam_id, $this->slug);

        (new SitemapEvent())->updateExamUpdateXml($this->exam_id, $this->status, $this->slug);


        // CacheClearService::entityContent(Exam::ENTITY_EXAM, $this->parent_id, $this->slug, $this->exam->slug, $this->exam_id, '', $this->lang_code);

        return parent::afterSave($insert, $changedAttributes);
    }


    public function getParent()
    {
        return $this->hasOne(ExamContent::class, ['id' => 'parent_id'])->alias('parent');
    }
}
