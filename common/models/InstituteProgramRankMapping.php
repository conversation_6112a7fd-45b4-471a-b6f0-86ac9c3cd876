<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "institute_program_rank_mapping".
 *
 * @property int $id
 * @property int $college_id
 * @property int $program_id
 * @property int $exam_id
 * @property int $course_id
 * @property int $highest_rank
 * @property int $lowest_rank
 * @property int $round
 * @property int $state_id
 * @property int $year
 * @property int $cutoff_category_id
 * @property int $status
 * @property string $created_at
 * @property string $updated_at
 *
 * @property College $college
 * @property CutoffCategory $cutoffCategory
 * @property Program $program
 */
class InstituteProgramRankMapping extends ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'institute_program_rank_mapping';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['college_id', 'exam_id', 'program_id', 'cutoff_category_id', 'year', 'highest_rank', 'lowest_rank', 'round'], 'required'],
            [['college_id', 'program_id', 'highest_rank', 'lowest_rank', 'round', 'cutoff_category_id', 'status', 'state_id', 'year', 'course_id', 'exam_id'], 'integer'],
            [['created_at', 'updated_at', 'college_type'], 'safe'],
            [['college_id'], 'exist', 'skipOnError' => true, 'targetClass' => College::className(), 'targetAttribute' => ['college_id' => 'id']],
            [['cutoff_category_id'], 'exist', 'skipOnError' => true, 'targetClass' => CutoffCategory::className(), 'targetAttribute' => ['cutoff_category_id' => 'id']],
            [['program_id'], 'exist', 'skipOnError' => true, 'targetClass' => Program::className(), 'targetAttribute' => ['program_id' => 'id']],
            [
                ['college_id', 'program_id', 'exam_id', 'cutoff_category_id', 'year', 'round'],
                'unique',
                'targetAttribute' => ['college_id', 'program_id', 'cutoff_category_id', 'year', 'round'],
                'message' => 'The combination of college, program, category, year, and round must be unique.',
                'skipOnError' => true
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'college_id' => 'College ID',
            'program_id' => 'Program ID',
            'course_id' => 'Course ID',
            'highest_rank' => 'Highest Rank',
            'lowest_rank' => 'Lowest Rank',
            'round' => 'Round',
            'exam_id' => 'Exam ID',
            'state_id' => 'State ID',
            'year' => 'Year',
            'cutoff_category_id' => 'Cutoff Category ID',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'college_id']);
    }

    public function getExam()
    {
        return $this->hasOne(Exam::className(), ['id' => 'exam_id']);
    }

    /**
     * Gets query for [[CutoffCategory]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CutoffCategoryQuery
     */
    public function getCutoffCategory()
    {
        return $this->hasOne(CutoffCategory::className(), ['id' => 'cutoff_category_id']);
    }

    /**
     * Gets query for [[Program]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ProgramQuery
     */
    public function getProgram()
    {
        return $this->hasOne(Program::className(), ['id' => 'program_id']);
    }

    /**
     * Gets query for [[State]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\StateQuery
     */
    public function getState()
    {
        return $this->hasOne(State::className(), ['id' => 'state_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\InstituteProgramRankMappingQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\InstituteProgramRankMappingQuery(get_called_class());
    }
}
