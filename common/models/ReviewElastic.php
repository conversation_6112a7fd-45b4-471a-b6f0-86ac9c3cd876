<?php

namespace common\models;

use yii\elasticsearch\ActiveRecord;

/**
 * ReviewElastic represents the elasticsearch index for reviews
 */
class ReviewElastic extends ActiveRecord
{
    /**
     * @return array the list of attributes for this record
     */
    public function attributes()
    {
        return [
            'review_id',
            'student_id',
            'college_id',
            'course_id',
            'admission_year',
            'batch',
            'status',
            'review_created_at',
            'review_updated_at',
            'review_overall_rating',
            'student_name',
            'student_profile_pic',
            'college_name',
            'college_slug',
            'college_display_name',
            'college_city_id',
            'college_city_name',
            'college_state_id',
            'college_state_name',
            'course_name',
            'course_slug',
            'stream_id',
            'stream_name',
            'stream_slug',
            'city_slug',
            'state_slug',
            'review_slug',
            'review_title',
            'review_content',
            'category_ratings',
        ];
    }

    /**
     * @return string the name of the index associated with this ActiveRecord class.
     */
    public static function index()
    {
        return 'reviews';
    }

    /**
     * @return string the name of the type of this ActiveRecord class.
     */
    public static function type()
    {
        return 'review';
    }

    /**
     * Define the mapping for this elasticsearch type
     */
    public static function mapping()
    {
        return [
            'properties' => [
                'review_id' => ['type' => 'integer'],
                'student_id' => ['type' => 'integer'],
                'college_id' => ['type' => 'integer'],
                'course_id' => ['type' => 'integer'],
                'admission_year' => ['type' => 'integer'],
                'batch' => ['type' => 'integer'],
                'status' => ['type' => 'integer'],
                'review_created_at' => ['type' => 'date', 'format' => 'yyyy-MM-dd HH:mm:ss'],
                'review_updated_at' => ['type' => 'date', 'format' => 'yyyy-MM-dd HH:mm:ss'],
                'review_overall_rating' => ['type' => 'float'],
                'student_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'student_profile_pic' => ['type' => 'keyword'],
                'college_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'college_slug' => ['type' => 'keyword'],
                'college_display_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'college_city_id' => ['type' => 'integer'],
                'college_city_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'college_state_id' => ['type' => 'integer'],
                'college_state_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'course_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'course_slug' => ['type' => 'keyword'],
                'stream_id' => ['type' => 'integer'],
                'stream_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'stream_slug' => ['type' => 'keyword'],
                'city_slug' => ['type' => 'keyword'],
                'state_slug' => ['type' => 'keyword'],
                'review_slug' => ['type' => 'keyword'],
                'review_title' => [
                    'type' => 'text',
                    'analyzer' => 'standard'
                ],
                'review_content' => [
                    'type' => 'text',
                    'analyzer' => 'standard'
                ],
                'category_ratings' => [
                    'type' => 'nested',
                    'properties' => [
                        'category_id' => ['type' => 'integer'],
                        'category_name' => ['type' => 'keyword'],
                        'rating' => ['type' => 'float']
                    ]
                ]
            ]
        ];
    }

    /**
     * Create the index with mapping
     */
    public static function createIndex()
    {
        $db = static::getDb();
        $command = $db->createCommand();
        
        // Delete index if exists
        try {
            $command->deleteIndex(static::index());
        } catch (\Exception $e) {
            // Index doesn't exist, continue
            unset($e); // Suppress unused variable warning
        }
        
        // Create index with mapping
        $command->createIndex(static::index(), [
            'mappings' => [
                static::type() => static::mapping()
            ]
        ]);
    }

    /**
     * Update mapping for existing index
     */
    public static function updateMapping()
    {
        $db = static::getDb();
        $command = $db->createCommand();
        
        try {
            $command->setMapping(static::index(), static::type(), static::mapping());
        } catch (\Exception $e) {
            // If mapping update fails, recreate index
            unset($e); // Suppress unused variable warning
            static::createIndex();
        }
    }
}
