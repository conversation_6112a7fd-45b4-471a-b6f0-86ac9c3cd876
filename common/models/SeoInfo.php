<?php

namespace common\models;

use common\models\query\SeoInfoQuery;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "seo_info".
 *
 * @property string $entity
 * @property int $entity_id
 * @property string|null $page
 * @property string|null $h1
 * @property string|null $title
 * @property string|null $description
 * @property string $created_at
 * @property string $updated_at
 */
class SeoInfo extends \yii\db\ActiveRecord
{
    const ENTITY_EXAM = 'exam';

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                // 'updatedAtAttribute' => false,
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'seo_info';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            // [['entity', 'entity_id'], 'required'],
            [['entity_id', 'parent_id'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['description'], 'string'],
            [['entity', 'h1', 'title'], 'string', 'max' => 255],
            [['page'], 'string', 'max' => 64],
            [['entity', 'entity_id', 'page', 'parent_id'], 'unique', 'targetAttribute' => ['entity', 'entity_id', 'page', 'parent_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'entity' => 'Entity',
            'entity_id' => 'Entity ID',
            'page' => 'Page',
            'h1' => 'H1',
            'title' => 'Title',
            'description' => 'Description',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * {@inheritdoc}
     * @return \common\models\SeoInfoQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new SeoInfoQuery(get_called_class());
    }

    /**
     * Save the model only if either of them (h1, title or description) is not empty
     *
     * @param SeoInfo $model
     * @return void
     */
    public function checkEmptyAndSave(SeoInfo $model)
    {
        if (empty($this->h1) && empty($this->title) && empty($this->description)) {
            return $model;
        } else {
            return $model->save();
        }
    }

    public function getExam()
    {
        return $this->hasOne(Exam::className(), ['id' => 'entity_id']);
    }
}
