<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use common\services\CacheClearService;
use common\event\SitemapEvent;

/**
 * This is the model class for table "news_content".
 *
 * @property int $id
 * @property int|null $news_id
 * @property int|null $author_id
 * @property string|null $h1
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property string|null $content
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property User $author
 * @property NewsSubdomain $news
 */
class NewsContentSubdomain extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;
    const STATUS_DRAFT = 2;
    const SCENARIO_IMPORTER = 'importer';

    public $preview;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        //  uncomment after running the importer
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    // ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                    // We'll handle updated_at in beforeSave method to implement conditional updates
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'news_content_subdomain';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['h1', 'author_id', 'news_id', 'meta_title', 'meta_keywords', 'meta_description', 'content'], 'required', 'except' => self::SCENARIO_IMPORTER],
            [['news_id', 'author_id', 'status'], 'integer'],
            [['content', 'editor_remark'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['meta_description'], 'string', 'max' => 500],
            [['meta_keywords'], 'string', 'max' => 400],
            [['h1', 'meta_title'], 'string', 'max' => 100],
            [['news_id'], 'unique', 'targetAttribute' => ['news_id'], 'message' => 'The Combination is alreday been taken'],
            [['author_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['author_id' => 'id']],
            [['news_id'], 'exist', 'skipOnError' => true, 'targetClass' => NewsSubdomain::className(), 'targetAttribute' => ['news_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'news_id' => 'News ID',
            'author_id' => 'Author ID',
            'h1' => 'H1',
            'meta_title' => 'Meta Title',
            'meta_keywords' => 'Meta Keywords',
            'meta_description' => 'Meta Description',
            'content' => 'Content',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getAuthor()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id'])->select(['id', 'name', 'slug']);
    }
    public function getTransAuthor()
    {
        return $this->hasOne(UserTranslation::className(), ['tag_user_id' => 'author_id']);
    }

    /**
     * Gets query for [[News]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\NewsSubdomainQuery
     */
    public function getNews()
    {
        return $this->hasOne(NewsSubdomain::className(), ['id' => 'news_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\NewsContentSubdomainQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\NewsContentSubdomainQuery(get_called_class());
    }

    /**
     * {@inheritdoc}
     */
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        // Condition 2: In news content, only update timestamp if content changes
        if (!$insert) {
            if ($this->isAttributeChanged('h1', false) ||
                $this->isAttributeChanged('meta_title', false) ||
                $this->isAttributeChanged('meta_keywords', false) ||
                $this->isAttributeChanged('content', false) ||
                $this->isAttributeChanged('editor_remark', false) ||
                $this->isAttributeChanged('meta_description', false)
            ) {
                // Update the timestamp when content changes
                $this->updated_at = new \yii\db\Expression('NOW()');
            } else {
                // Keep the original updated_at value for other field changes
                $this->updated_at = $this->getOldAttribute('updated_at');
            }
        }

        return true;
    }

    public function afterSave($insert, $changedAttributes)
    {
        if (!$insert) {
            $contentChange = array_key_exists('content', $changedAttributes);

            if ($contentChange) {
                // Update `updated_at` to current time if name or slug changed
                $this->updated_at = new \yii\db\Expression('NOW()');
            } else {
                // Revert to old value if no name/slug change
                $this->updated_at = $this->getOldAttribute('updated_at');
            }
        }

        if ((int)$this->status == self::STATUS_ACTIVE) {
            if (array_key_exists('content', $changedAttributes)) {
                (new SitemapEvent())->updateNewsUpdateXml($this->news_id, $this->status, $this->updated_at);
            } else if (array_key_exists('meta_title', $changedAttributes)) {
                (new SitemapEvent())->updateNewsUpdateXml($this->news_id, $this->status, $this->updated_at);
            } else if (array_key_exists('meta_keywords', $changedAttributes)) {
                (new SitemapEvent())->updateNewsUpdateXml($this->news_id, $this->status, $this->updated_at);
            } else if (array_key_exists('meta_description', $changedAttributes)) {
                (new SitemapEvent())->updateNewsUpdateXml($this->news_id, $this->status, $this->updated_at);
            } else if (array_key_exists('h1', $changedAttributes)) {
                (new SitemapEvent())->updateNewsUpdateXml($this->news_id, $this->status, $this->updated_at);
            }
        }

        // CacheClearService::entityContent(NewsSubdomain::ENTITY_NEWS, '', '', $this->news->slug, $this->news->id, '', $this->news->lang_code);

        return parent::afterSave($insert, $changedAttributes);
    }
}
