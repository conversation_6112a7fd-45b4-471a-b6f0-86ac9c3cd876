<?php

namespace common\models;

use common\event\SitemapEvent;
use common\services\CollegeService;
use Yii;
use yii\base\DynamicModel;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use common\services\CacheClearService;

/**
 * This is the model class for table "college_course_content".
 *
 * @property int $id
 * @property int|null $college_id
 * @property int|null $course_id
 * @property string|null $qualification
 * @property string|null $content
 * @property string|null $eligiblity
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property string|null $h1
 * @property int|null $status
 * @property int|null $page_index
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class CollegeCourseContent extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'college_course_content';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['college_id', 'course_id', 'status', 'page_index'], 'integer'],
            [['content', 'eligibility','editor_remark'], 'string'],
            [['course_id'], 'required'],
            [['created_at', 'updated_at', 'qualification'], 'safe'],
            [['meta_title', 'meta_description', 'h1'], 'string', 'max' => 255],
            [['college_id', 'course_id'], 'unique', 'targetAttribute' => ['college_id', 'course_id'], 'message' => 'Combination of this course and college has already been taken'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'college_id' => 'College ID',
            'course_id' => 'Course ID',
            'qualification' => 'Qualification',
            'content' => 'Content',
            'eligibility' => 'Eligibility',
            'meta_title' => 'Meta Title',
            'meta_description' => 'Meta Description',
            'h1' => 'H1',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */

    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'college_id']);
    }

    /**
     * Gets query for [[Courses]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CourseQuery
     */

    public function getCourse()
    {
        return $this->hasOne(Course::className(), ['id' => 'course_id']);
    }

    public function beforeSave($insert)
    {
        $this->qualification = json_encode($this->qualification);
        return parent::beforeSave($insert);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\CollegeCourseContentQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\CollegeCourseContentQuery(get_called_class());
    }

    public function afterSave($insert, $changedAttributes)
    {

        CollegeService::updateCoursePageIndexDocument($this);

        (new SitemapEvent())->updateCollegeCourseSitemapMysl($this, $this->college->slug, $this->college->status);

        // CacheClearService::entityContent(College::ENTITY_COLLEGE, 'courses-fees', $this->course->slug, $this->college->slug, $this->id, 'ci');

        return parent::afterSave($insert, $changedAttributes);
    }
}
