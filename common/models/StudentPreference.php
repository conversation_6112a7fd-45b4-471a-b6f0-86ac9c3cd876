<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use common\models\Course;
use common\models\Student;
use common\services\LeadService;
use common\services\WebEngage;

/**
 * This is the model class for table "student_preference".
 *
 * @property int $id
 * @property int $student_id
 * @property string|null $exam
 * @property string|null $stream
 * @property string|null $course
 * @property string|null $specialization
 * @property string|null $degree
 * @property int $created_at
 * @property int $updated_at
 *
 * @property Student $student
 */
class StudentPreference extends \yii\db\ActiveRecord
{
    const SCENARIO_IMPORTER = 'importer';
    const IS_REMOVED_COURSE_YES = 1;
    const IS_REMOVED_COURSE_NO = 0;
    const IS_REMOVED_CITY_NO = 0;
    const IS_REMOVED_CITY_YES = 1;

    const IS_STUDY_ABROAD_YES = 1;
    const IS_STUDY_ABROAD_NO = 0;

    const IS_DISTANCE_YES = 1;
    const IS_DISTANCE_NO = 0;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'student_preference';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['activity_id'], 'required', 'except' => self::SCENARIO_IMPORTER],
            [['activity_id', 'student_id', 'distance_education', 'is_study_abroad', 'exam', 'stream', 'course', 'child_course_id', 'program_id', 'specialization',  'interested_city', 'interested_state', 'degree', 'highest_qualification', 'city_is_removed', 'course_is_removed', 'level', 'admission'], 'integer'],
            [['created_at', 'updated_at', 'user_highest_qualification'], 'safe'],
            [['activity_id'], 'exist', 'skipOnError' => true, 'targetClass' => StudentActivity::className(), 'targetAttribute' => ['activity_id' => 'id'], 'except' => self::SCENARIO_IMPORTER],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'activity_id' => 'Activity ID',
            'exam' => 'Exam',
            'stream' => 'Stream',
            'course' => 'Parent Course',
            'child_course_id' => 'Child Course',
            'program_id' => 'Parent Id',
            'specialization' => 'Specialization',
            'degree' => 'Degree',
            'highest_qualification' => 'Qualification',
            'interested_city' => 'Interested City',
            'interested_state' => 'Interested State',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Student]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getStudent()
    {
        return $this->hasOne(Student::className(), ['id' => 'student_id']);
    }

    /**
     * Gets query for [[StudentActivity]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getStudentActivity()
    {
        return $this->hasOne(StudentActivity::className(), ['id' => 'activity_id']);
    }

    /**
     * Gets query for [[StudentPreferenceSpecialization]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\StudentPreferenceSpecializationNewQuery
     */
    public function getSpecialization()
    {
        return $this->hasMany(StudentPreferenceSpecialization::className(), ['student_preference_id' => 'id']);
    }

    /**
     * Gets query for [[PreferredLocation]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\StudentPreferencePreferredLocation
     */
    public function getPreferredLocation()
    {
        return $this->hasMany(StudentPreferencePreferredLocation::className(), ['student_preference_id' => 'id']);
    }

    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);
        if ($insert) {
            $studentDetail =  Student::find()->where(['id' => $this->student_id])->one();
            $insertedId = $this->getPrimaryKey();
            $studentPreference = StudentPreference::find()->where(['id' => $insertedId])->one();
            $studentActivity = StudentActivity::find()->where(['id' => $studentPreference->activity_id])->one();

            //below function will be called only at the time of registration.
            // if ($studentActivity->category_sub_page == Student::STUDENT_CATEGORY) {
            $created_at = date('Y-m-d H:i:s');
            $eventName = 'lead_creation';
            $webEngagePayLoad = [
                'phone' =>  $studentDetail->phone,
                'email' =>  $studentDetail->email,
                'name' => $studentDetail->name,
                'current_city' =>  empty($studentDetail->current_city) ? '' : (int)$studentDetail->current_city,
                'current_state' =>  empty($studentDetail->current_state) ?: (int)$studentDetail->current_state,
                'source' => $studentDetail->source,
                'page_url' =>  $studentDetail->source_url,
                'highest_qualification' => empty($studentPreference->highest_qualification) ? '' : $studentPreference->highest_qualification,
                'specialization' => empty($studentPreference->specialization) ? '' : $studentPreference->specialization,
                'entity' => 'site',
                'entity_name' => Student::STUDENT_CATEGORY,
                'cta_location' => Student::STUDENT_CATEGORY,
                'interested_course' => empty($studentPreference->course) ? '' : $studentPreference->course,
                'is_opt_verified' => 'Yes',
                'source_url' =>  $studentDetail->source_url,
                'utm_source' => $studentDetail->utm_source ?? '',
                'utm_campaign' => $studentDetail->utm_campaign ?? '',
                'utm_medium' => $studentDetail->utm_medium ?? '',
                'previous_url' => Yii::$app->request->referrer ?? ''

            ];

            WebEngage::registerUser($studentDetail->phone, $studentDetail->email, $studentDetail->name, $studentDetail->source_url);
            WebEngage::pushEvent($studentDetail->phone, $eventName, $created_at, $webEngagePayLoad);
            // }
        }
    }
}
