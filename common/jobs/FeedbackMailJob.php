<?php

namespace common\jobs;

use Yii;

/**
 * Class FeedbackMailJob.
 */
class FeedbackMailJob extends \yii\base\BaseObject implements \yii\queue\RetryableJobInterface
{
    public $email;

    public $lead;

    /**
     * @inheritdoc
     */
    public function execute($queue)
    {
        $subject = sprintf('%s Thank you for your query at GetMyUni - Admissions Helpline', $this->lead['full_name']);

        Yii::$app->mailer->compose('feedback/feedback-html', [
            'lead' => $this->lead
        ])
        ->setFrom('<EMAIL>')
        // ->setTo($this->email)
        ->setTo('<EMAIL>')
        ->setSubject($subject)
        ->send();
    }

    /**
     * @inheritdoc
     */
    public function getTtr()
    {
        return 60;
    }

    /**
     * @inheritdoc
     */
    public function canRetry($attempt, $error)
    {
        return $attempt < 3;
    }
}
