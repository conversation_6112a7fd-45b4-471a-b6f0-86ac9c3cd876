<?php

namespace common\services;

use common\helpers\CourseHelper;
use common\helpers\DataHelper;
use common\models\Category;
use common\models\College;
use common\models\Course;
use common\models\CourseContent;
use common\models\documents\College as DocumentsCollege;
use common\models\Exam;
use common\models\GmuMetaCategory;
use common\models\HighlightAttribute;
use common\models\HighlightAttributeValue;
use common\models\Stream;
use common\models\StreamContent;
use frontend\services\ExamService;
use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Inflector;
use common\helpers\ContentHelper;
use DOMDocument;

class CourseService
{
    protected $examService;
    const HOMEPAGE_CONTENT = 'course';

    public function __construct(
        ExamService $examService
    ) {
        $this->examService = $examService;
    }

    /**
     * Get Course Details
     *
     * @param object $course Course Object
     * @return object | null
     */
    public function getDetail($course)
    {
        $course = Course::find()->bySlug($course)->with('stream')->active()->one();

        return $course;
    }

    /**
     *  Combine course/index API data in a single $data Array
     */
    public function getAPICourseData(): array
    {
        $dataArr                    = [];
        $courseContent              = $this->getHomePageContent();
        $courseTopContent           = $courseContent['top_content'];
        $dataArr['popular_courses'] = $this->getAPIPopularCourses();
        $dataArr['top_content']     = !empty($courseTopContent) ? $courseTopContent : '';

        return $dataArr;
    }

    /**
     *  Get page Content and Meta data
     */
    public function getHomePageContent($slug = '')
    {
        return GmuMetaCategory::find()
            ->select(['h1', 'title', 'description', 'top_content'])
            ->where(['slug' =>  self::HOMEPAGE_CONTENT])
            ->andWhere(['status' => GmuMetaCategory::STATUS_ACTIVE])
            ->one();
    }

    /**
     *  Get Names of popular couses
     */
    protected function getAPIPopularCourses(): array
    {
        $popularCourseArr   = [];
        foreach ($this->getPopularCourse() as $value) {
            $popularCourseArr[] = $value->short_name;
        }
        return $popularCourseArr;
    }

    /**
     * Get the popular course
     *
     * @return array |[]
     */
    public function getPopularCourse(int $limit = 5)
    {
        $course = Course::find()
            ->select(['name', 'short_name', 'slug'])
            ->where(['is_popular' => Course::POPULAR_YES])
            ->active()
            ->limit($limit)
            ->all();

        return $course ?? [];
    }

    /**
     * Get the feature of course
     * @param int  $courseId Course Id,
     * @param string $entity  Entity as Course
     *
     * @return array | []
     */
    public function getFeature($entityId, $entity, $orderBy = null)
    {
        $query = new Query();
        $query->select(['ha.is_key_feature', 'ha.name', 'hav.value'])
            ->from('highlight_attribute_value as hav')
            ->leftJoin('highlight_attribute as ha', 'ha.id = hav.is_key_feature_id')
            ->where(['hav.entity' => $entity])
            ->andWhere(['hav.entity_id' => $entityId])
            ->andWhere(['hav.status' => HighlightAttributeValue::STATUS_ACTIVE])
            ->andWhere(['ha.status' => HighlightAttribute::STATUS_ACTIVE]);
        $features = $query->all();
        if (empty($features)) {
            return [];
        }
        $items = [];
        foreach ($features as $feature) {
            $items[$feature['is_key_feature']][0] = $feature['value'];
            $items[$feature['is_key_feature']][1] = $feature['name'];
        }
        //order by
        $data = array_merge(array_flip($orderBy), $items);
        return $data;
    }


    public static  function getDurationFeature($entityId, $entity, $orderBy = null)
    {
        $query = new Query();
        $query->select(['ha.is_key_feature', 'ha.name', 'hav.value'])
            ->from('highlight_attribute_value as hav')
            ->leftJoin('highlight_attribute as ha', 'ha.id = hav.is_key_feature_id')
            ->where(['hav.entity' => $entity])
            ->andWhere(['hav.entity_id' => $entityId])
            ->andWhere(['ha.id' =>2])
            ->andWhere(['hav.status' => HighlightAttributeValue::STATUS_ACTIVE])
            ->andWhere(['ha.status' => HighlightAttribute::STATUS_ACTIVE]);
        $features = $query->one();
        if (empty($features)) {
            return [];
        }
       
        $items = [];
        foreach ($features as $key=>$feature) {
            if($key=='value'){
                $items['value'] = $feature;
            }
           
            
        }
        //return $items;
        //order by
        return $items;
    }

    /**
     * Return pages for Course
     *
     * @param integer $courseId
     * @return array|[]
     */
    public function getMenu(int $courseId)
    {
        $pages = CourseContent::find()
            ->select(['page'])
            ->andWhere(['course_id' => $courseId])
            ->andWhere(['status' => CourseContent::STATUS_ACTIVE])
            ->andWhere(['parent_id' => null])
            ->all();

        $items = [];
        if (!empty($pages)) {
            foreach ($pages as $key => $value) {
                $items[$value->page] = CourseHelper::$subPages[$value->page];
            }
        }

        return $items ?? [];
    }

    /**
     * Get Course Page Content
     * @param integer $id Course Id
     * @param string $page Course Page name,
     *
     * @return object | null
     */
    public function getContent($id, $page)
    {
        $courseContent = CourseContent::find()
            ->where(['course_id' => $id])
            ->andWhere(['page' => $page])
            ->with('author')
            ->active()
            ->one();
        /*if (in_array($page, ContentHelper::$_coursePageContent)) {
            $htmlContent = ContentHelper::getGenerateHtml($courseContent->content, $page);
            $courseContent->content = $htmlContent;
        }*/
        return $courseContent;
    }

    public function getLiveApplication($course, $limit = 10)
    {
        $mapping = $this->getCourseMapping($course);
        if (!$mapping) {
            return [];
        }

        $query = new Query();
        $query->select([
            'gsc.college_id', 'gsccd.redirect_link', 'gsccd.fees'
        ])
            ->from('gmu_sponsor_colleges as gsc')
            ->innerJoin('gmu_sponsor_colleges_carousel_details as gsccd', 'gsccd.college_id = gsc.college_id')
            ->where(['gsc.is_deleted' => 0])
            ->andWhere(['gsccd.is_deleted' => 0])
            ->andWhere(['gsccd.course_name' => $mapping['vanityurl']])
            ->groupBy('gsc.college_id');

        if (!empty($limit)) {
            $query->limit($limit);
        }

        $query->orderBy('gsc.display_order');
        $data = $query->all(\Yii::$app->gmudb);

        $items = [];
        foreach ($data as $key => $value) {
            $college = College::find()
                ->select(['id', 'name', 'display_name', 'slug', 'logo_image'])
                ->byOldId($value['college_id'])
                ->one();

            if (!empty($college)) {
                $items[] = [
                    'collegeId' => $college->id,
                    'name' => $college->name ?? '',
                    'display_name' => $college->display_name ?? '',
                    'slug' => $college->slug ?? '',
                    'image' =>  $college->logo_image ?? '',
                    'fees' => $value['fees'],
                    'link' => $value['redirect_link']
                ];
            }
        }

        return $items ?? [];
    }

    private function getCourseMapping($course)
    {
        if (empty($course)) {
            return [];
        }

        $query = new Query();
        $query->select(['vanityurl'])
            ->from('gmu_course_type_mapping')
            ->where(['in', 'short_vanityurl', $course]);

        if (isset($query->from)) {
            return $query->one(\Yii::$app->gmudb);
        }

        return [];
    }

    /**
     * Get Colleges associted with Course
     * @param object $course Course,
     * @param integer $limit recorde limits
     *
     * @return array | []
     */
    public function getTopColleges($course, $limit = 8)
    {
        $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
        $specialization = $course->slug;
        if (!empty($course->parent_id)) {
            $currCourse = self::getParentCourse($course->parent_id) ?? $course;
            $strSlug = str_contains($course->slug, $currCourse->slug);
            if (!$strSlug) {
                $specialization = $currCourse->slug . '-' . $course->slug;
            }
        }
        $colleges = $collection->aggregate(
            [
                [
                    '$match' => [
                        '$or' => [
                            [
                                'course.course_slug' => $course->slug
                            ],
                            [
                                'course.specialization' => $specialization
                            ]
                        ]
                    ]
                ],
                [
                    '$project' => [
                        'name' => 1,
                        'display_name' => 1,
                        'slug' => 1,
                        'rank' => 1,
                        'banner_image' => 1,
                        'logo' => 1,
                        'city_name' => 1,
                        'state_name' => 1,
                        'is_sponsored' => 1
                    ]
                ],
                [
                    '$sort' => ['rank' => 1]
                ],
                [
                    '$limit' => $limit
                ]
            ]
        );

        if (count($colleges) < 2) {
            return false;
        }

        $items = [];
        foreach ($colleges as $college) {
            $items[] = [
                'name' => $college['name'],
                'display_name' => $college['display_name'],
                'slug' => $college['slug'],
                'rank' => $college['rank'],
                'cover_image' => $college['banner_image'],
                'logo_image' => $college['logo'],
                'city' => $college['city_name'] ?? '',
                'state' => $college['state_name'] ?? '',
                'is_sponsored' => $college['is_sponsored'] ?? ''
            ];
        }
        $is_sponsored = empty($items) ? '' : in_array(College::SPONSORED_NO, array_column($items, 'is_sponsored'));

        return ['items' => $items ?? [], 'is_sponsored' => empty($is_sponsored) ? College::SPONSORED_YES  : College::SPONSORED_NO];
    }

    /**
     * Get Courses by Stream
     * @param object $course Course Object
     *
     * @return array|[]
     */

    public function getStreamCourse($course, $page = null, $limit = 10)
    {
        $courses = Course::find()
            ->select(['course.name as name', 'course.short_name as short_name', 'course.slug as slug', 'course.id'])
            ->where(['parent_id' => null])
            ->with(['courseContent'])
            ->andWhere(['degree' => $course->degree])
            ->andWhere(['course.status' => Course::STATUS_ACTIVE])
            ->byExcludeId($course->id)
            ->byStream($course->stream->slug)
            ->limit($limit)
            ->all();

        foreach ($courses as $course) {
            if (empty($course->courseContent)) {
                return null;
            }
            foreach ($course->courseContent as $p) {
                if ($p->page == $page) {
                    $result[] = $course;
                }
            }
        }

        return $result ?? [];
    }

    /**
     * Get Stream
     *
     * @param string|integer $param id or slug of strema
     * @return object
     */
    public function getStream($param)
    {
        $stream = Stream::find()->bySlug($param)->one();

        if (empty($stream)) {
            $stream = Stream::find()->byId($param)->one();
        }

        return $stream;
    }

    /**
     * Get Stream content
     */
    public function getStreamContent($streamId, $pageSlug)
    {
        return StreamContent::find()
            ->andWhere(['stream_id' => $streamId])
            ->andWhere(['sub_page' => $pageSlug])
            ->with('author')
            ->active()
            ->one();
    }

    /**
     * Get Specialization of ParentCourse
     *
     * @param object $course Course parentId.
     * @param int $limit
     *
     * return array|[].
     */

    public function getParentSpecialization($course, $page = null, $limit = 10)
    {
        //hide the specialization of other courses
        if ($course->parent_id == 610) {
            return [];
        }

        $courses = Course::find()
            ->select(['name', 'short_name', 'slug'])
            ->byExcludeId($course->id)
            ->byParentId(Course::tableName(), $course->parent_id ?? $course->id)
            ->innerJoinWith('courseContent')
            ->andWhere(['course.status' => Course::STATUS_ACTIVE])
            ->limit($limit)
            ->orderBy('short_name')
            ->groupBy('slug')
            ->all();

        foreach ($courses as $course) {
            if (empty($course->courseContent)) {
                return null;
            }

            foreach ($course->courseContent as $p) {
                if ($p->page == $page) {
                    $result[] = $course;
                }
            }
        }

        return $courses ?? [];
    }

    /**
     * Get List of [Exams] for Course
     *
     * @param int |$course Course.
     * @return array |[] Exams.
     */

    public function getExamByCourse($course, $limit = 8)
    {
        $data = [];
        $subTexts = ['exam-start', 'result-date'];

        if (!$course) {
            return [];
        }

        if (!empty($course->parent_id)) {
            $course = self::getParentCourse($course->parent_id) ?? $course;
        }

        $exams = $course->getExams()
            ->nationaLevelExam()
            ->orderBy([new \yii\db\Expression('FIELD(exam_type_id, ' . ExamService::NATIONAL_COMMON_EXAM . ')DESC'), 'id' => SORT_ASC])
            ->limit($limit)
            ->active()
            ->all();

        foreach ($exams as $exam) {
            $examDates = $this->examService->getDate($exam->id);
            $item = [
                'id' => $exam->id,
                'name' => $exam->name,
                'display_name' => $exam->display_name,
                'slug' => $exam->slug,
                'image' => $exam->cover_image,
                'lang_code' => $exam->lang_code
            ];

            foreach ($examDates as $date) {
                if (in_array($date->slug, $subTexts)) {
                    $item += [
                        str_replace('-', '_', $date->slug) => $date->start
                    ];
                }
            }

            $data[] = $item;
        }
        return $data;
    }

    /**
     * Get college associated with course
     * @param string $courseSlug Course Slug
     *
     * return array |[]
     */
    public function getCourseCount($coursesSlug)
    {
        $key = md5(__CLASS__ . '\/' . __FUNCTION__ . json_encode(func_get_args()));
        $data = Yii::$app->cache->getOrSet($key, function () use ($coursesSlug) {
            $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
            $courses = $collection->aggregate([
                [
                    '$unwind' => [
                        'path' => '$course',
                        'preserveNullAndEmptyArrays' => false
                    ]
                ],
                [
                    '$match' => [
                        'is_sponsored' => College::SPONSORED_NO,
                        '$or' => [
                            [
                                'course.course_slug' => [
                                    '$in' => $coursesSlug
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    '$project' => [
                        'course.course_slug' => 1,
                        'college_id' => 1,
                    ]
                ]
            ]);

            $specializations = $collection->aggregate([
                [
                    '$unwind' => [
                        'path' => '$course',
                        'preserveNullAndEmptyArrays' => false
                    ]
                ],
                [
                    '$unwind' => [
                        'path' => '$course.specialization',
                        'preserveNullAndEmptyArrays' => false
                    ]
                ],
                [
                    '$match' => [
                        'is_sponsored' => College::SPONSORED_NO,
                        '$or' => [
                            [
                                'course.specialization' => [
                                    '$in' => $coursesSlug
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    '$project' => [
                        'course.specialization' => 1,
                        'college_id' => 1,
                    ]
                ]
            ]);

            $courseItems = [];
            foreach ($courses as $course) {
                if (isset($course['course']['course_slug'])) {
                    $courseItems[$course['course']['course_slug']][] = $course['college_id'];
                }
            }

            $specializationItems = [];
            foreach ($specializations as $specialization) {
                if (isset($specialization['course']['specialization'])) {
                    $specializationItems[$specialization['course']['specialization']][] = $specialization['college_id'];
                }
            }

            $items = array_merge($courseItems, $specializationItems);
            $slugs = [];
            foreach ($items as $itemKey => $itemValue) {
                if (!in_array($itemKey, $slugs) && count($itemValue) > 1) {
                    $slugs[] = $itemKey;
                }
            }

            return $slugs;
        }, 60 * 60 * 24 * 90);

        return $data;
    }

    /**
     * Get the Courses list  based on stream
     * @param object $stream Stream
     *
     * return array |[]
     */
    public function getCourseByStream($stream)
    {
        $key = md5(__CLASS__ . '\/' . __FUNCTION__ . json_encode($stream->attributes));
        $data = Yii::$app->cache->getOrSet($key, function () use ($stream) {
            $query = new Query();
            $query->select('c.id, c.name, c.slug, c.short_name, c.degree, cc.page, c.stream_id')
                ->from('course as c')
                ->leftJoin('course_content as cc', 'cc.course_id = c.id')
                ->where(['c.stream_id' => $stream->id])
                ->andWhere(['cc.parent_id' => null])
                ->andWhere(['c.status' => Course::STATUS_ACTIVE])
                ->andWhere(['cc.status' => CourseContent::STATUS_ACTIVE]);
            $course = $query->all();

            $coursesSlug = ArrayHelper::getColumn($course, 'slug', false);
            $coursesSlug = array_values(array_map('unserialize', array_unique(array_map('serialize', $coursesSlug))));
            $pages = $this->getCourseCount($coursesSlug);

            $data = [];
            foreach ($course as $q) {
                if (empty($q['degree'])) {
                    continue;
                }

                $data['tab'][$q['name']]['id'] = $q['id'];
                $data['tab'][$q['name']]['name'] = $q['name'];
                $data['tab'][$q['name']]['slug'] = $q['slug'];
                $data['tab'][$q['name']]['degree'] = $q['degree'];
                $data['tab'][$q['name']]['stream'] = $q['stream_id'];
                if (!empty($q['page']) && $q['page'] != 'about') {
                    $data['tab'][$q['name']]['page'][$q['page']] = isset(CourseHelper::$cateSubPages[$q['page']]) ? CourseHelper::$cateSubPages[$q['page']] : '';
                }

                //college offer page check
                if (array_key_exists('college_count', $data['tab'][$q['name']])) {
                    continue;
                } else if (in_array($q['slug'], $pages)) {
                    $data['tab'][$q['name']]['college_count'] = $q['slug'] . '-colleges';
                }

                //get feature count
                if (array_key_exists('avg_fees', $data['tab'][$q['name']])) {
                    continue;
                } else {
                    // $features = $this->getFeature($q['id'], 'course');
                    // if (!empty($features)) {
                    //     $data['tab'][$q['name']]['avg_fees'] = is_string($features['avg_fees']) ? $features['avg_fees'] : '';
                    //     $data['tab'][$q['name']]['duration'] = is_string($features['duration']) ? $features['duration'] : '';
                    // }
                    $features = $this->getFeature($q['id'], 'course', array_keys(CourseHelper::$featurArr));
                    if (!empty($features)) {
                        $data['tab'][$q['name']]['avg_fees'] = (!empty($features['avg_fees'][0]) && is_string($features['avg_fees'][0])) ? $features['avg_fees'][0] : '';
                        $data['tab'][$q['name']]['duration'] = (!empty($features['duration'][0]) && is_string($features['duration'][0])) ? $features['duration'][0] : '';
                    }
                }
            }

            $menus = array_unique(array_column($data['tab'], 'degree'));
            $degrees = [];
            foreach ($menus as $k => $menu) {
                $degrees[$menu] = CourseHelper::$degree[$menu];
            }

            //tab order
            $orderArray = ['diploma', 'bachelors', 'masters', 'bachelors+masters', 'masters+doctorate', 'doctorate', 'certificate', 'fellowship-programme', 'postgraduate-diploma'];
            $finalyArr = array_merge(array_flip($orderArray), $degrees);
            foreach ($finalyArr as $k => $arr) {
                if (!is_int($arr)) {
                    $degrees['section'][$k] = $arr;
                }
            }
            $data['section'] = $degrees['section'];

            return $data ?? [];
        }, 60 * 60 * 24 * 90);

        return $data;
    }

    /**
     * Get college list based on stream
     * @param object $stream Stream Object, $limit nmber of recorde
     *
     * @return array|[]
     */
    public function getCollegeByStream($stream, $limit = 5)
    {
        $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
        $colleges = $collection->aggregate(
            [
                [
                    '$match' => [
                        'course.stream_slug' => $stream->slug
                    ]
                ],
                [
                    '$project' => [
                        'name' => 1,
                        'display_name' => 1,
                        'slug' => 1,
                        'logo' => 1
                    ]
                ],
                [
                    '$sort' => ['rank' => 1]
                ],
                [
                    '$limit' => $limit
                ]
            ]
        );
        $items = [];
        foreach ($colleges as $college) {
            $items[] = [
                'name' => $college['name'],
                'display_name' => $college['display_name'],
                'slug' => $college['slug'],
                'logo_image' => $college['logo'],
            ];
        }

        return $items ?? [];
    }

    /**
     * Get Exam list based on stream
     * @param object $stream Stream object
     *
     * @return array|[]
     */
    public function getExamByStream($stream, $limit = 5)
    {
        $query = new Query();
        $query->select(['e.name', 'e.display_name', 'e.slug', 'e.cover_image'])
            ->from('exam_stream as es')
            ->innerJoin('exam as e', 'e.id = es.exam_id')
            ->innerJoin('stream as s', 's.id = es.stream_id')
            ->where(['es.stream_id' => $stream->id])
            ->andWhere(['e.status' => Exam::STATUS_ACTIVE])
            ->andWhere(['e.type' => Exam::TYPE_NATIONAL])
            ->limit($limit);

        return $query->all() ?? [];
    }

    /**
     * Get College count based on fees
     * @param object $course
     *
     * @return array | []
     */
    public function getChartData($course)
    {
        if (!$course) {
            return [];
        }

        $hash = md5(base64_encode(__CLASS__ . '-' . __FUNCTION__ . json_encode($course->attributes)));
        $data = Yii::$app->cache->getOrSet($hash, function () use ($course) {

            $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
            $specialization = $course->slug;
            if (!empty($course->parent_id)) {
                $currCourse = self::getParentCourse($course->parent_id) ?? $course;
                $strSlug = str_contains($course->slug, $currCourse->slug);
                if (!$strSlug) {
                    $specialization = $currCourse->slug . '-' . $course->slug;
                }
            }
            $data = $collection->aggregate(
                [
                    [
                        '$match' => [
                            '$or' => [
                                [
                                    'course.course_slug' => $course->slug
                                ],
                                [
                                    'course.specialization' => $specialization
                                ]
                            ]

                        ]
                    ],
                    [
                        '$project' => [
                            'course.fees_range' => 1,
                            'course.avg_fees' => 1,
                            'course.course_slug' => 1
                        ]
                    ]
                ]
            );

            $items = [];
            foreach ($data as $d) {
                foreach ($d['course'] as $c) {
                    if (in_array($c['fees_range'], array_values(CourseHelper::$feesRange))) {
                        $items[$c['fees_range']][] = $c['avg_fees'];
                    }
                }
            }

            return !empty($items) ?
                [
                    [
                        'fees' => '0 - 1 Lakh',
                        'collegeCount' => isset($items['0-100000']) ? count($items['0-100000']) : 0
                    ],
                    [
                        'fees' => '1 - 2 Lakh',
                        'collegeCount' => isset($items['100001-200000']) ? count($items['100001-200000']) : 0
                    ],
                    [
                        'fees' => '2 - 3 Lakh',
                        'collegeCount' => isset($items['200001-300000']) ? count($items['200001-300000']) : 0
                    ],
                    [
                        'fees' => '3 - 5 Lakh',
                        'collegeCount' => isset($items['300001-500000']) ? count($items['300001-500000']) : 0
                    ],
                    [
                        'fees' => ' 5 Lakh',
                        'collegeCount' => isset($items['500001']) ? count($items['500001']) : 0
                    ]
                ]
                : [];
        }, 60 * 60 * 24 * 90);

        return  $data ?? [];
    }

    /**
     * Get Article Category associate with stream
     * @param object $stream
     *
     * @return object
     */
    public function getArticleCategory($stream)
    {
        $arr = ['computer' => 'computer-applications'];
        $query = Category::find()->select(['id', 'name', 'slug', 'display_name']);

        if (array_key_exists($stream->slug, $arr)) {
            return $query->bySlug($arr[$stream->slug])->one();
        } else {
            return $query->bySlug($stream->slug)->one();
        }
    }
    /** Get Course litst based on state */
    public function getCourseByState($course)
    {
        $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
        $specialization = $course->slug;
        if (!empty($course->parent_id)) {
            $currCourse = self::getParentCourse($course->parent_id) ?? $course;
            $strSlug = str_contains($course->slug, $currCourse->slug);
            if (!$strSlug) {
                $specialization = $currCourse->slug . '-' . $course->slug;
            }
        }
        $data = $collection->aggregate([
            [
                '$match' => [
                    '$or' => [
                        ['course.course_slug' => $course->slug],
                        ['course.specialization' => $specialization]
                    ]
                ]
            ],
            [
                '$group' => [
                    '_id' => '$state_slug',
                    'count' => ['$sum' => 1],
                    'name' => ['$first' => '$state_name']
                ]
            ],
            [
                '$project' => [
                    'slug' => '$_id',
                    '_id' => 0,
                    'name' => 1,
                    'count' => 1
                ]
            ]
        ]);

        $items = [];
        if (empty($data)) {
            return [];
        }

        foreach ($data as $d) {
            if ($d['count'] > 1) {
                $items['data'][$d['slug']] = $d;
            }
        }
        $items['courseSlug'] = $course->slug;
        $items['specializationSlug'] = $specialization;
        $items['courseName'] = $course->short_name;

        return $items;
    }

    public static function getCourseFeature($id)
    {
        $data = self::getFeature($id, 'course', array_keys(CourseHelper::$featurArr));
        return $data;
    }

    public function getSubPageDropdown($course, $page)
    {
        $dropDownArr = [];
        $courseContentPage = CourseContent::find()->select(['id', 'page'])
            ->with('author')
            ->andWhere(['course_id' => $course->id, 'parent_id' => null, 'status' => 1])->all();
        foreach ($courseContentPage as $key => $value) {
            $courseContentPage = CourseContent::find()->select(['id', 'page'])
                ->andWhere(['parent_id' => $value->id, 'status' => 1])->orderBy(['page' => SORT_ASC])->asArray()->all();
            if (!empty($courseContentPage)) {
                foreach ($courseContentPage as $dropDownContent) {
                    $dropDownArr[CourseHelper::$subPages[$value->page]][] = $dropDownContent;
                }
            }
        }
        return $dropDownArr;
    }

    /*public function getGenerateHtml($content, $pageName)
    {
        $_tableOfContents = ['table-of-content', 'table-of-contents', 'table-of-contents:'];
        $DOM = new DOMDocument();
        @$DOM->loadHTML($content);
        foreach ($DOM->getElementsByTagName('*') as $i => $element) {
            $textContent = '';
            if ($element->tagName == 'p') {
                $textContent = htmlentities($element->textContent, null, 'utf-8');
                $textContent = str_replace('&nbsp;', '', $textContent);
                $textContent_slug_title = explode(' ', $textContent);
                $textContent_slug_title = Inflector::slug(implode('-', $textContent_slug_title));
            }

            if ($element->tagName == 'p' && in_array($textContent_slug_title, $_tableOfContents)) {
                $ulElement2 = $DOM->getElementsByTagName('*')->item($i + 2);
                $ulElement3 = $DOM->getElementsByTagName('*')->item($i + 3);
                $ulElement2->parentNode->removeChild($ulElement2);
                $ulElement3->parentNode->removeChild($ulElement3);
                $element->parentNode->removeChild($element);
            }
        }
        return $DOM->saveHTML();
    }*/

    public function gePopularDegreeCourse($course, int $limit = 5)
    {
        $popularCourse = Course::find()
            ->select(['name', 'short_name', 'slug'])
            ->where(['is_popular' => Course::POPULAR_YES])
            ->andWhere(['degree' => $course->degree])
            ->andWhere(['course.status' => Course::STATUS_ACTIVE])
            ->byExcludeId($course->id)
            ->active()
            ->limit($limit)
            ->all();

        return $popularCourse ?? [];
    }

    public static function getParentCourse($courseId)
    {
        $courses = Course::find()
            ->where(['id' => $courseId])
            ->andWhere(['parent_id' => null])
            ->andWhere(['status' => Course::STATUS_ACTIVE])
            ->one();

        if (empty($courses)) {
            return [];
        }

        return $courses;
    }
}
