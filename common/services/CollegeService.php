<?php

namespace common\services;

use Carbon\Carbon;
use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\Brochure;
use common\models\College;
use common\models\CollegeContent;
use common\models\CollegeCourse;
use common\models\CollegeCourseContent;
use common\models\FeatureValue;
use common\models\Gallery;
use common\models\documents\CollegeCourse as CollectionCollegeCourse;
use common\models\Course;
use common\models\CollegeFees;
use common\models\CollegeCourseExam;
use common\models\CollegeProgramContent;
use common\models\documents\College as DocumentsCollege;
use common\models\CollegeRankings;
use common\models\LiveApplication;
use common\models\LiveApplicationCombination;
use common\models\Redirection;
use common\models\Stream;
use common\models\Qna;
use backend\models\RecentActivity;
use common\models\CollegeProgram;
use common\models\CollegeProgramExam;
use common\models\CollegeProgramFees;
use common\models\City;
use common\models\CourseEligibility;
use common\models\CourseExamCollege;
use common\models\CutOff;
use common\models\CutoffCategory;
use common\models\documents\CollegeProgram as ProgramDocuments;
use common\models\ExamDate;
use common\models\Exam;
use common\models\Program;
use common\models\ProgramCourseMapping;
use backend\models\RecentActivityTracker;
use common\helpers\CollegeRanksDataHelper;
use common\helpers\CourseHelper;
use common\models\Article;
use common\models\CollegeCourseMapping;
use common\models\CollegeHostel;
use common\models\CollegeProgramStudentEnrollment;
use common\models\CollegeRankingPublisher;
use common\models\CollegeStudent;
use common\models\CollegeStudentOverallEnrollment;
use common\models\CollegeStudentScholarship;
use common\models\ContentTemplate;
use common\models\Specialization;
use common\services\ExamService as ServicesExamService;
use common\models\Filter;
use common\models\News;
use common\models\NewsSubdomain;
use common\models\SponsorCollege;
use common\models\CollegeNotificationUpdate;
use stdClass;
use yii\db\Query;
use yii\helpers\Json;
use yii\helpers\Inflector;
use frontend\helpers\Url;
use Yii;
use yii\caching\TagDependency;
use yii\helpers\ArrayHelper;
use Twig\Environment;
use Twig\Loader\ArrayLoader;
use yii\data\ActiveDataProvider;
use yii\db\ActiveQuery;
use yii\data\Pagination;
use common\helpers\ContentHelper;
use common\models\CollegeCiSubpageContent;
use common\models\CollegeListingMapping;
use common\models\CollegePiSubpageContent;
use common\models\CourseProgramDates;
use frontend\services\FilterService;
use InvalidArgumentException;

class CollegeService
{
    public $currentPage = 1;

    public function getById(int $id)
    {
        return College::findOne($id);
    }

    /**
     * Get college by slug
     * @return object
     */
    public static function getBySlug($slug)
    {
        return College::find()
            ->where(['slug' => $slug, 'status' => College::STATUS_ACTIVE])
            ->with('city')
            ->one();
    }

    /**
     * Get college by slug
     * @return object
     */
    public static function getCollegeSlugsForSitemap($data): array
    {
        $collegeSlug = [];
        $finalCollegeSlug = [];
        foreach ($data as $val) {
            $getCollegslug = $val->sub_page == 'info' ? $val->slug : substr(str_replace($val->subPage, '', $val->slug), 0, -1);
            $collegeSlug[] = $getCollegslug;
        }
        $colleges = College::find()->select(['slug'])
            ->where(['status' => College::STATUS_ACTIVE])
            ->andWhere(['in', 'slug', $collegeSlug])->asArray()
            ->all();

        foreach ($colleges as $value) {
            $finalCollegeSlug[] = $value['slug'];
        }
        return $finalCollegeSlug;
    }


    /**
     * Get college content
     *
     * @param $id College ID
     * @param $page Current sub-page
     * @return object
     */
    public function getContent($id, $page)
    {
        return CollegeContent::find()
            ->where(['entity' => College::ENTITY_COLLEGE])
            ->andWhere(['entity_id' => $id])
            ->andWhere(['sub_page' => $page])
            ->andWhere(['status' => CollegeContent::STATUS_ACTIVE])
            ->one();
    }

    /**
     * Get college active menu
     *
     * @param \common\models\College $college
     * @return object
     */
    public function getMenu(College $college, $currentPage = 'info', $qna = '')
    {
        $collegeContents = CollegeContent::find()
            ->select(['sub_page', 'status'])
            ->where(['entity' => College::ENTITY_COLLEGE])
            ->andWhere(['entity_id' => $college->id])
            ->andWhere(['parent_id' => null])
            ->andWhere(['status' => CollegeContent::STATUS_ACTIVE])
            ->indexBy('sub_page')
            ->all();
        $menus = [];
        foreach ($collegeContents as $collegeContent) {
            if ($collegeContent->sub_page == 'qna' && empty($qna)) {
                continue;
            }
            if ($collegeContent->status == CollegeContent::STATUS_ACTIVE) {
                $menus[$collegeContent->sub_page] = CollegeHelper::$subPages[$collegeContent->sub_page];
            }
        }
        // if ($college->parent_id == null) {
        //     $menus['college-under-' . $college->slug] = 'Affiliated Colleges';
        // }
        // $menus['compare-college'] = 'Compare College';

        $orderByArray = [
            'info',
            'courses-fees',
            'admission',
            'cut-off',
            'reviews',
            'placements',
            'result',
            'facilities',
            'images-videos',
            'scholarships',
            'qna',
            'compare',
        ];

        $finalMenu = array_merge(array_flip($orderByArray), $menus);
        return $finalMenu;
    }

    /**
     * Get college features
     *
     * @param College $college College model object
     * @return array|[]
     */
    public function getFeatures(College $college)
    {
        $query = new Query();
        $query->select(['c.name as collegeName', 'fg.name as featureGroupName', 'f.name as featureName', 'fv.value', 'f.slug as featureSlug']) //@to be added 'f.slug as featureSlug'
            ->from('college_feature_value as cfv')
            ->innerJoin('college as c', 'c.id = cfv.college_id')
            ->innerJoin('feature_value as fv', 'fv.id = cfv.feature_value_id')
            ->innerJoin('feature as f', 'fv.feature_id = f.id')
            ->innerJoin('feature_group as fg', 'f.feature_group_id = fg.id')
            ->where(['cfv.college_id' => $college->id])
            ->andWhere(['fv.status' => FeatureValue::STATUS_ACTIVE]);

        $data = [];
        foreach ($query->all() as $item) {
            $data[$item['featureName']] = $item;
        }

        return $data;
    }

    public function getCollegePressRanking($collegeId)
    {
        $rankings = CollegeRankings::find()->where(['college_id' => $collegeId])
            ->andWhere(['status' => CollegeRankings::STATUS_ACTIVE])
            ->orderBy(['year' => SORT_DESC, 'rank' => SORT_ASC])
            ->all();

        return empty($rankings) ? [] : $rankings;
    }

    /**
     * Get Affiliated Colleges
     *
     * @param int $id College id
     * @param int $limit Limit the result
     *
     * @return common\models\College
     */
    public function getAffiliatedColleges($collegeId, $limit = 4, $parentId = null)
    {
        return College::find()->select(['id', 'parent_id', 'name', 'display_name', 'slug', 'logo_image', 'cover_image', 'city_id'])
            ->where(['parent_id' => $parentId ?? $collegeId])
            ->andWhere(['status' => College::STATUS_ACTIVE])
            ->excludeByCollegeId($collegeId)
            ->limit($limit)
            ->all();
    }

    /**
     * Get Affiliated Colleges for college Compare
     *
     * @param int $id College id
     * @param int $limit Limit the result
     *
     * @return common\models\College
     */
    public function getAffiliatedCollegeCompareColleges($collegeId, $limit = 4)
    {
        return College::find()->select(['id', 'parent_id', 'name', 'display_name', 'slug', 'logo_image', 'cover_image', 'city_id'])
            ->where(['parent_id' => $collegeId])
            ->andWhere(['status' => College::STATUS_ACTIVE])
            ->excludeByCollegeId($collegeId)
            ->limit($limit)
            ->all();
    }

    /**
     * Get colleges by city ID
     *
     * @param int $cityId City id
     * @param int $limit Limit the result
     * @param array $excludeId Exclude ids
     * @param string $orderBy Order by the result
     *
     * @return common\models\College
     */
    public function getCollegesByCity($cityId, $limit = 4, $excludeId = [], $orderBy = SORT_DESC)
    {
        $colleges = College::find()->select(['id', 'parent_id', 'name', 'display_name', 'slug', 'logo_image', 'cover_image', 'city_id'])
            ->active()
            ->andWhere(['city_id' => $cityId])
            ->orderBy(['updated_at' => $orderBy])
            ->limit($limit);

        if (!empty($excludeId)) {
            $colleges->andWhere(['not', ['id' => $excludeId]]);
        }

        return $colleges->all();
    }

    /**
     * Get Popular Colleges
     *
     * @param int $limit Limit the result
     * @param array $excludeId Exclude ids
     *
     * @return common\models\College
     */
    public function getPopularColleges($limit = 4, $excludeId = null, $position = null)
    {
        return [];
        $popularColleges = College::find()->active()
            ->andWhere(['is_popular' => College::POPULAR_YES])
            ->orderBy(['position' => SORT_ASC])
            ->with('city')
            ->limit($limit);

        if (!empty($excludeId)) {
            $popularColleges->andWhere(['not', ['id' => $excludeId]])
                ->andWhere(['>', 'position', $position ?? 0]);
        }

        $colleges = $popularColleges->all();

        if (!empty($colleges) && count($colleges) >= $limit) {
            return $colleges;
        } else {
            $moreColleges = $this->getPopularColleges($limit - count($colleges), $excludeId);
            return array_merge($colleges, $moreColleges);
        }
    }

    /**
     * Get College Images
     * @param object | college object
     * @return array | []
     */
    public function getGallery(College $college, $limit = null)
    {
        $images = Gallery::find()->select(['id', 'file', 'type'])
            ->where(['entity' => College::ENTITY_COLLEGE])
            ->andWhere(['entity_id' => $college->id])
            ->active();

        if ($limit != null) {
            $images->andWhere(['not', ['type' => 'videos']])->limit($limit);

            return $images->all();
        }

        $images = $images->all();
        if (!$images) {
            return [];
        }

        $items = [];
        foreach ($images as $image) {
            if ($image->type == 'videos') {
                $items[$image->type][] = str_replace('https://www.youtube.com/v/', '', $image->file);
            } else {
                $items['images'][$image->type][] = $image->file;
            }
        }
        return $items;
    }

    /**
     * Get College all Feature values
     *
     * @param array $features
     * @return string|null
     */
    public function getFeatureStringValues($features, $featureGroupName)
    {
        $items = [];
        foreach ($features as $feature) {
            if ($feature['featureGroupName'] == $featureGroupName) {
                $items[] = $feature['featureName'];
            }
        }

        return $items ? implode(', ', $items) : null;
    }

    /**
     * Get List of Exams under College
     *
     * @param int $collegeId College ID
     * @return array|[]
     */
    public function getExamList($collegeId)
    {
        $subTexts = ['exam-start', 'result-date'];
        $college = College::find()
            ->where(['id' => $collegeId])
            ->with(['exams'])
            ->one();

        if (!$college) {
            return [];
        }

        $data = [];
        foreach ($college->exams as $exam) {
            if ($exam->status == Exam::STATUS_ACTIVE) {
                $examDates = (new ServicesExamService())->getDate($exam->id);
                $item = [
                    'id' => $exam->id,
                    'name' => $exam->name,
                    'display_name' => $exam->display_name,
                    'slug' => $exam->slug,
                    'image' => $exam->cover_image,
                ];
                foreach ($examDates as $date) {
                    if (in_array($date->slug, $subTexts)) {
                        $item += [
                            str_replace('-', '_', $date->slug) => $date->start
                        ];
                    }
                }
                $data[] = $item;
            }
        }

        return json::decode(json::encode($data), false);
    }

    //get the relared articles for colleges based on the exams tagged
    public function getRelatedArticles($collegeId)
    {
        $query = new Query();
        $query->select(['ae.article_id'])
            ->from(['college_exam ce'])
            ->leftJoin('article_exam ae', 'ae.exam_id = ce.exam_id')
            ->leftJoin('article a', 'a.id = ae.article_id')
            ->where(['ce.college_id' => $collegeId])
            ->andWhere(['a.is_popular' => Article::POPULAR_YES])
            ->andWhere(['a.status' => Article::STATUS_ACTIVE])
            ->orderBy(['a.updated_at' => SORT_DESC]);

        $articleIds = $query->all();

        if (empty($articleIds)) {
            return [];
        }

        foreach ($articleIds as $id) {
            $article[] = Article::find()->select(['id', 'title', 'cover_image', 'slug', 'h1', 'author_id'])->where(['id' => $id])->one();
        }

        return $article ?? [];
    }

    //get the relared news for colleges based on the exams tagged
    public function getRelatedNews($collegeId)
    {
        $query = new Query();
        $query->select(['en.news_id'])
            ->from(['college_exam ce'])
            ->leftJoin('exam_news_subdomain en', 'en.exam_id = ce.exam_id')
            ->leftJoin('news_subdomain ns', 'ns.id = en.news_id')
            ->where(['ce.college_id' => $collegeId])
            ->andWhere(['ns.is_popular' => News::POPULAR_YES])
            ->andWhere(['ns.status' => News::STATUS_ACTIVE])
            ->orderBy(['ns.updated_at' => SORT_DESC]);

        $newsIds = $query->all();

        if (empty($newsIds)) {
            return [];
        }

        foreach ($newsIds as $id) {
            $news[] = NewsSubdomain::find()->select(['id', 'display_name', 'name', 'banner_image', 'slug'])->where(['id' => $id])->one();
        }

        return $news ?? [];
    }

    public function getAllContents($collegeId)
    {
        return CollegeContent::find()->select(['template_id', 'content', 'sub_page', 'author_id', 'updated_at', 'h1', 'meta_title', 'meta_description'])
            ->where(['entity' => College::ENTITY_COLLEGE])
            ->andWhere(['entity_id' => $collegeId])
            ->andWhere(['status' => CollegeContent::STATUS_ACTIVE])
            ->indexBy('sub_page')
            ->all();
    }


    /**
     * Get Reviews of College
     *
     * @param int | $collegeId College ID
     * @param array | $category Review Category
     * @return array|[]
     */

    public function getReviewByCategory($slug, $category, $limit = 5)
    {
        $query = new Query();
        $query->select(["*, group_concat( concat(`reviewed_category`,'@***@',`ra3`) SEPARATOR '#~*') reviewRating, group_concat( concat(`reviewed_category`,'@***@',`re3`) SEPARATOR '#~*') review"])
            ->from('gmu_college_reviewer_summary')
            ->where(['clgUrl' => $slug])
            ->andWhere(['>', 'LENGTH(re3)', 20])
            ->groupBy('reviewed_by')
            ->orderBy(['admin_updated_on' => SORT_DESC, 'LENGTH(`re3`)' => SORT_DESC]);

        if (!empty($category)) {
            $query->andWhere(['IN', 'reviewed_category', $category]);
        }

        if (!empty($limit)) {
            $query->limit($limit);
        }

        $reviews = $query->all(\Yii::$app->gmudb);

        return $reviews ?? [];
    }

    /**
     * Getting Total connection count , review count
     * @param int | $collegeId College ID
     * @return array |[]
     */
    public function getReviewRating($collegeId)
    {

        $query = new Query();
        $query->select([
            'gmu_college_rev_count as reviews_count',
            'gmu_college_ra1sum as ra1sum',
            'gmu_college_ra1count as ra1count',
            'gmu_college_conn_count as connectionCount',
            'gmu_college_tag as clgTag'
        ])
            ->from('gmu_college_review_connection_summary')
            ->where(['gmu_college_id' => $collegeId])
            ->groupBy('gmu_college_id');

        $rating = $query->all(\Yii::$app->gmudb);

        return $rating ?? [];
    }

    /**
     * Getting Total Category base rating
     * @param int | $collegeId College ID
     * @return array |[]
     */
    public function getRevCategoryRating($collegeId)
    {
        $query = new Query();
        $query->select(['SUM(ra3) as ra3', 'count(ra3) as raCont', 'reviewed_category'])
            ->from('gmu_college_reviewer_summary')
            ->where(['flag' => 0])
            ->andWhere(['gmu_clg_id' => $collegeId])
            ->groupBy('reviewed_category');

        $categoryRating = $query->all(\Yii::$app->gmudb);

        $data = [];
        foreach ($categoryRating as $rating) {
            if ($rating['ra3'] > 0) {
                if (array_key_exists($rating['reviewed_category'], CollegeHelper::$reviewCategoryRatingValue)) {
                    $data[CollegeHelper::$reviewCategory[$rating['reviewed_category']]] = round(($rating['ra3'] * CollegeHelper::$reviewCategoryRatingValue[$rating['reviewed_category']]) /
                        ($rating['raCont'] * CollegeHelper::$reviewCategoryRatingValue[$rating['reviewed_category']]) * 0.5, 1);
                }
            }
        }

        return $data ?? [];
    }

    /**
     * Getting Total Rating grouping by rating Ex : 1 ,2 , etc.
     * @param int | $collegeId College ID
     * @return array |[]
     */
    public function getRevDistributionRating($collegeId)
    {
        $query = new Query();
        $query->distinct();
        $query->select([
            'SUM(if(rating IN (1,2) ,user_count,0)) as rating1',
            'SUM(if(rating IN (3,4) ,user_count,0)) as rating2',
            'SUM(if(rating IN (5,6,7) ,user_count,0)) as rating3',
            'SUM(if(rating IN (8,9,10) ,user_count,0)) as rating4'
        ])
            ->from('gmu_ssr')
            ->where(['college_id' => $collegeId]);

        $distributionRating = $query->all(\Yii::$app->gmudb);

        $data = [];
        //to do
        foreach ($distributionRating as $rating) {
            $totalRating = $rating['rating1'] + $rating['rating2'] + $rating['rating3'] + $rating['rating4'];
            if ($totalRating != 0) {
                $data['progress1'] = round(($rating['rating1'] / $totalRating) * 100);
                $data['progress2'] = round(($rating['rating2'] / $totalRating) * 100);
                $data['progress3'] = round(($rating['rating3'] / $totalRating) * 100);
                $data['progress4'] = round(($rating['rating4'] / $totalRating) * 100);
            }
            $data['rating1'] = $rating['rating1'];
            $data['rating2'] = $rating['rating2'];
            $data['rating3'] = $rating['rating3'];
            $data['rating4'] = $rating['rating4'];
        }

        return $data ?? [];
    }

    /**
     * Get Courses available in College.
     *
     * @param int $collegeId College ID
     *
     * @return array|[] Detail List of Courses available in College.
     */
    public function getCollegeCourses($collegeId)
    {
        return CollegeProgram::find()
            ->select(['id', 'college_id', 'program_id', 'course_id'])
            ->where(['college_id' => $collegeId])
            ->active()
            // ->orderBy('position')
            ->count();
    }

    public function getCourseHighlights(College $college, $page = null, $exclude = [], $entity = '', $degree = '')
    {
        $query = ProgramDocuments::find()
            ->where(['college_id' => $college->id])
            ->andWhere(['status' => CollegeCourse::STATUS_ACTIVE])
            ->orderBy(['course_position' => SORT_ASC]);

        if (!empty($entity) && $entity == 'ci' && !empty($degree)) {
            $query->andWhere(['degree' => $degree]);
        }

        $data = $query->all();

        $items = [];
        foreach ($data as $d) {
            //exclude the Course contain + symbol from page
            if (!empty($page) && strpos($d['course'], '+') !== false) {
                continue;
            }

            if (!empty($exclude)) {
                if (in_array($d['course_slug'], $exclude)) {
                    continue;
                }

                // if (in_array($d['course_slug'], ['pgdm', 'diploma', 'certificate', 'fellowship-programme']) && !in_array($page, ['courses-fees', 'info'])) {
                //     continue;
                // }
            }
            $collegeCourseEligibility = CollegeCourseContent::find()->select(['eligibility'])->where(['course_id' => $d['course_id']])->andWhere(['college_id' => $college->id])->one();
            $collegeProgramExam = CollegeProgramExam::find()->select(['*'])->where(['college_program_id' => $d['college_program_id']])->count();
            $items[$d['course']] = [
                'id' => $d['college_program_id'],
                'program_id' => $d['program_id'],
                'course_id' => $d['course_id'] ?? '',
                'name' => $d['course'],
                'short_name' => $d['course_short_name'] ?? '',
                'slug' => $d['course_slug'],
                'duration' => $d['duration'],
                'course_duration' => $d['course_duration'] ?? '',
                'course_eligibility' => $d['course_eligibility'] ?? '',
                'course_content_eligibility' => $collegeCourseEligibility->eligibility ?? '',
                'course_position' => $d['course_position'] ?? '',
                'avgFees' => round($d['courseAvgFees'], 0),
                'mode' => $d['mode'] ?? '',
                'stream_name' => $d['stream_name'] ?? '',
                'course_page_index' => $d['coursePageIndex'] ?? '',

            ];

            if (!empty($d['specialization_id'])) {
                $items[$d['course']] += ['specializationId' => $d['specialization_id']];
            }

            $exams = [];
            if (!empty($d['exams'])) {
                $exams[$d['exams']] = self::getExamSlug($d['exams']);
                if ($collegeProgramExam > 0) {
                    $items[$d['course']] += ['exams' => $exams];
                }
            } else {
                $items[$d['course']] += ['exams' => ''];
            }

            if (!empty($d['course_brochure'])) {
                $items[$d['course']] += ['course_brochure' => Url::toCollegeBroucher($d['course_brochure'])];
            } else {
                $items[$d['course']] += ['course_brochure' => ''];
            }
            if (isset($d['isCiPage']) && $d['isCiPage'] != false) {
                $items[$d['course']] += ['coursePage' => Url::toCollegeCourse($d['course_slug'], $college->slug, $d['coursePageIndex'])];
            } else {
                $items[$d['course']] += ['coursePage' => ''];
            }
        }

        return $items;
    }

    public function getExamSlug($name)
    {
        if (empty($name)) {
            return [];
        }
        $exams = explode(',', $name);

        foreach ($exams as $key => $value) {
            $slug[$value] = Exam::find()->select(['slug'])->where(['display_name' => $value])->one()->slug;
        }

        return $slug ?? [];
    }

    public function getFeaturesByGroup($features)
    {
        $data = [];
        foreach ($features as $feature) {
            $data[$feature['featureGroupName']][] = $feature;
        }

        return $data;
    }

    /***
     * Getting Live application of sponser college
     *
     * @param Object |$college college instance
     * @return array |[]
     */

    public function getLiveApplicationFormData($collegeId, $limit = 20)
    {
        if (empty($collegeId)) {
            return '';
        }

        $college = College::find()->where(['id' => $collegeId])->one();

        if (empty($college) || empty($college->city_id)) {
            return [];
        }

        $courses = CollegeProgram::find()
            ->select(['course_id'])
            ->where(['college_id' => $college->id])
            ->groupBy('course_id')
            ->active()
            ->all();

        $data = LiveApplicationCombination::find()
            ->select([
                'live_application.college_id AS college_id',
                'college.logo_image AS logo_image',
                'college.name AS college_name',
                'college.slug AS college_slug',
                'live_application.course_name AS course_name',
                'live_application.total_fees AS total_fees',
                'live_application.redirect_url AS redirect_url'
            ])
            ->innerJoinWith('liveApplication.college')
            ->where(['in', 'live_application_combination.course_id', array_column($courses, 'course_id')])
            ->andWhere(['live_application_combination.state_id' => $college->city->state->id])
            ->andWhere(['!=', 'live_application.college_id', $college->id])
            ->andWhere(['live_application_combination.status' => LiveApplicationCombination::STATUS_ACTIVE])
            ->andWhere(['live_application.status' => LiveApplication::STATUS_ACTIVE])
            ->distinct(true)
            ->asArray()
            ->all();

        if (!empty($data)) {
            shuffle($data);
        }

        $data = array_slice($data, 0, 14);

        return $data ?? [];
    }

    /**
     * Getting Forum question & answer Count
     *
     * @param string |$collegeSlug College Slug
     * @return array |[]
     */
    public function getQnaCount($CollegeSlug)
    {
        $key = 'qna-' . $CollegeSlug;
        $data = Yii::$app->cache->getOrSet($key, function () use ($CollegeSlug) {
            $query = new Query;
            $query->select(['count(fq.id) as count'])
                ->from('forum_question_category  as fqc')
                ->innerJoin('forum_category as fc', 'fc.id = fqc.category_id')
                ->innerJoin('forum_question as fq', 'fq.id = fqc.question_id')
                ->where(['fc.slug' => $CollegeSlug])
                ->andWhere(['fq.status' => 1])
                ->andWhere(['>', 'fq.total_answer', 0]);

            return $query->all(\Yii::$app->gmudb);
        }, 60 * 60 * 7, new TagDependency(['tags' => 'qna-' . $CollegeSlug]));

        return $data ?? [];
    }

    /**
     * Get Courses available in College.
     *
     * @param string |$slug College Slug
     * @param int |$collegeId College ID
     * @return object | Detail List of Courses available in College.
     */
    public function getProgramBySlug($slug, $collegeId)
    {
        $data = ProgramDocuments::find()
            ->where(['program_slug' => $slug])
            ->andWhere(['college_id' => $collegeId])
            ->andWhere(['status' => CollegeCourse::STATUS_ACTIVE])
            ->one();

        return $data;
    }

    /**
     * Get the Program Fees Detials
     *
     * @param object |$course Course Details Object
     * @return array |[]
     */

    /*public function getProgramFeesDetails($id)
    {
        $feesData = CollegeProgramFees::find()
            ->where(['college_program_id' => $id])
            ->active()
            ->all();

        $data = [];

        if (!empty($feesData)) {
            foreach ($feesData as $fee) {
                $data['feesData'][$fee->type][] = [
                    'price' => $fee->fees,
                    'year' => ($fee->duration_type == CollegeProgramFees::FEE_ONE_TIME) ? null : $fee->duration
                ];
            }
            $data['totalFees'] = array_sum(array_column($feesData, 'fees'));
        }

        return $data ?? [];
    }*/

    public function getProgramFeeDetails($id)
    {
        $feesData = CollegeProgramFees::find()
            ->where(['college_program_id' => $id])
            ->active()
            ->all();

        $data = [];

        if (!empty($feesData)) {
            foreach ($feesData as $fee) {
                if ($fee->duration_type != CollegeFees::FEE_ONE_TIME) {
                    $data['programFees']['duration_type'][$fee->type] = $fee->type;
                    $data['programFees']['feesData'][$fee->duration][] = [
                        'price' => $fee->fees,
                        'duration_type' => $fee->type,
                        'year' => ($fee->duration_type == CollegeFees::FEE_ONE_TIME) ? null : $fee->duration
                    ];
                } else {
                    $data['programFees']['oneTimeFees'][$fee->type] = [
                        'total' => $fee->fees,
                        //'year' => ($fee->duration_type == CollegeFees::FEE_ONE_TIME) ? null : $fee->duration
                    ];
                }
            }
            $data['totalFees'] = array_sum(array_column($feesData, 'fees'));
        }
        return $data ?? [];
    }

    /** not in use
     * Get the available College Images
     *
     * @param int |$college CollegeId int
     * @return array |[]
     */
    /*public function getCollegeImage($collegeId)
    {
        $images =  Gallery::find()
            ->active()
            ->where(['entity' => College::ENTITY_COLLEGE])
            ->andWhere(['entity_id' => $collegeId])
            ->all();

        $items = [];
        foreach ($images as $image) {
            $items[$image->type][] = $image->file;
        }

        return $items ?? [];
    }*/

    /**
     * Getting College Broucher
     *
     * @param int |$collegeId, College Id,
     * @param string |$entity, College, Course
     *
     * @return  array| []
     */
    public function getCollegeBroucher($collegeId, $entity)
    {
        $brochure = Brochure::find()->select(['pdf'])
            ->where(['college_id' => $collegeId])
            ->andWhere(['entity' => $entity])
            ->one();

        return $brochure ?? null;
    }

    public function getInstitutionType($features)
    {
        return $features['Institution Type']['value'] ?? '';
    }

    /** Get Program List based on courseSlug and collegeId */
    public static function getCourseList($courseSlug, $collegeId)
    {
        $queries = ProgramDocuments::find()
            ->where(['course_slug' => $courseSlug])
            ->andWhere(['college_id' => $collegeId])
            ->andWhere(['status' => CollegeCourse::STATUS_ACTIVE])
            ->orderBy(['program_position' => SORT_ASC])
            ->all();

        $queries = array_merge(
            array_filter($queries, function ($q) {
                if ($q['program_position'] !== null) {
                    return $q;
                }
            }),
            array_filter($queries, function ($q) {
                if ($q['program_position'] === null) {
                    return $q;
                }
            })
        );

        $items = [];
        foreach ($queries as $query) {
            $item = new stdClass;
            $item->college_program_id = $query->college_program_id;
            $item->college_id = $query->college_id;
            $item->program_id = $query->program_id;
            $item->program = $query->program;
            $item->program_slug = $query->program_slug;
            $item->program_position = $query->program_position;
            $item->fees = $query->fees;
            $item->duration = $query->duration;
            $item->duration_type = $query->duration_type;
            $item->total_seat = $query->total_seat;
            $item->type = $query->type;
            $item->degree = $query->degree;
            $item->mode = $query->mode;
            $item->stream_id = $query->stream_id;
            $item->course = $query->course;
            $item->course_slug = $query->course_slug;
            $item->isCiPage = $query->isCiPage;
            $item->courseAvgFees = $query->courseAvgFees;
            $item->exams = $query->exams;
            $item->status = $query->status;
            $item->pageIndex = $query->pageIndex ?? '';
            $item->course_short_name = $query->course_short_name ?? '';
            $item->specialization_name = $query->specialization_name ?? '';

            $items[] = $item;
        }

        return $items;
    }

    /** get the program list based on collegeid and course id */
    public function getCourseSpecializationList($courseId, $collegeId)
    {

        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);
        $aggregate[] = ['$addFields' => [
            'program_position_null' => [
                '$cond' => [
                    'if' => ['$eq' => ['$program_position', null]],
                    'then' => 1,
                    'else' => 0
                ]
            ],
            'course_position_null' => [
                '$cond' => [
                    'if' => ['$eq' => ['$course_position', null]],
                    'then' => 1,
                    'else' => 0
                ]
            ]
        ]];
        $sort = [
            'program_position_null' => 1,
            'course_position_null' => 1,
            'program_position' => 1,
            'course_position' => 1
        ];

        $aggregate[] = [
            '$match' => [
                'college_id' => $collegeId,
                'course_id' => $courseId,
                'type' => 'full_time',
                'status' => 1,
                '$and' => [
                    [
                        'program' => [
                            '$not' => [
                                '$regex' => '[+{]|tie up|Hons|Integrated|Diploma|Distance|Dual Degree|Lateral|certificate|Fellowship'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $aggregate[] = [
            '$sort' => $sort
        ];

        $data = $collection->aggregate($aggregate);

        $items = [];

        foreach ($data as $d) {
            $items[] = [
                'name' => $d['program'],
                'slug' => $d['program_slug'],
                'fees' => $d['fees'],
                'duration' => $d['duration'],
                'duration_type' => !empty($d['duration_type']) ? $d['duration_type'] : 1,
                'pageIndex' => $d['pageIndex'],
                'specialization' => isset($d['specialization_name']) ? $d['specialization_name'] : '',
                'position' => $d['program_position'] ?? ''
            ];
        }

        return $items;
    }

    /**
     * Get the available exam details for course
     * @param int |Id Program Id
     * @return object
     */
    public function getProgramExam($id)
    {
        $exams = CollegeCourseExam::find()->where(['college_course_id' => $id])->all();
        if (!$exams) {
            return [];
        }

        $item = [];
        foreach ($exams as $exam) {
            $data = (new ServicesExamService())->getDetail($exam['exam_id']);
            $examDates = $this->getDate($exam['exam_id']);
            if (empty($data)) {
                continue;
            }

            $item[$data->slug] = [
                'name' => $data->name,
                'display_name' => $data->display_name,
                'slug' => $data->slug,
                'image' => $data->cover_image,
                'exam_type' => $data->exam_type_id,
            ];
            if (!empty($examDates)) {
                $dateArr = [];
                foreach ($examDates as $date) {
                    $dateArr += [
                        str_replace('-', '_', $date->slug) => $date->start . '|' . $date->end
                    ];
                }
                $item[$data->slug]['date'] = $dateArr;
                unset($dateArr);
            }
        }
        //sort based on exam_type
        $keys = array_column($item, 'exam_type');
        if (!empty($keys)) {
            array_multisort($keys, SORT_ASC, $item);
        }

        return $item;
    }

    /** get the exam Date */
    public function getDate(int $id)
    {
        return ExamDate::find()->select(['name', 'slug', 'exam_id', 'start', 'end'])
            ->andWhere(['exam_id' => $id])
            ->andWhere(['status' => ExamDate::STATUS_ACTIVE])
            ->orderBy(['start' => SORT_ASC])
            ->all();
    }

    /**
     * Get the available exam details for course
     * @param int |$courseId Course Id, $collegeId CollegeId
     * @return object
     */
    public function getCourseExam($courseId, $collegeId)
    {
        $query = CollegeCourse::find()
            ->with(['exams'])
            ->where(['course_id' => $courseId])
            ->andWhere(['college_id' => $collegeId])
            ->andWhere(['status' => CollegeCourse::STATUS_ACTIVE])
            ->all();

        if (!$query) {
            return [];
        }

        $exams = [];
        $items = [];
        // dd($query);
        foreach ($query as $ckey => $cValue) {
            if (!empty($cValue->exams)) {
                // $exams[$cValue->exams[0]->slug] = $cValue->exams;
                $items[$cValue->exams[0]->display_name] = $cValue->exams[0]->display_name ?? $cValue->exams[0]->name;
            }
        }

        // foreach ($exams as $ekey => $exam) {
        //     $items[] =  $exam[0]->display_name ?? $exam[0]->name;
        // }

        return !empty($items) ? implode(', ', $items) : [];
    }


    public static function getCourseContent($courseId, $collegeId)
    {
        $collegeCourseContent = CollegeCourseContent::find()
            ->where(['college_id' => $collegeId])
            ->andWhere(['course_id' => $courseId])
            ->andWhere(['status' => CollegeCourseContent::STATUS_ACTIVE])
            ->one();

        return $collegeCourseContent ?? [];
    }

    public static function getSubpageContent($modelClass, $id, $subpageSlug, $year)
    {
        if (!in_array($modelClass, [CollegeCiSubpageContent::class, CollegePiSubpageContent::class])) {
            throw new InvalidArgumentException('Invalid model class provided.');
        }

        // Dynamically determine the primary key and status constant
        $idField = ($modelClass === CollegeCiSubpageContent::class) ? 'college_course_content_id' : 'college_program_id';
        $statusActive = $modelClass::STATUS_ACTIVE; // Fetch status constant from the model

        return $modelClass::find()
            ->where([
                $idField => $id,
                'subpage' => $subpageSlug,
                'status' => $statusActive, // Use the model-specific status
            ])
            ->andFilterWhere(['year' => $year])
            ->one() ?? [];
    }

    public static function checkSubpageContent($modelClass, $id)
    {

        $idField = ($modelClass === CollegeCiSubpageContent::class) ? 'college_course_content_id' : 'college_program_id';
        $statusActive = $modelClass::STATUS_ACTIVE;

        $subpages = $modelClass::find()
            ->select(['subpage', 'year', 'position'])
            ->where([
                $idField => $id,
                'status' => $statusActive,
            ])
            ->orderBy([
                new \yii\db\Expression('CASE WHEN position IS NULL THEN 1 ELSE 0 END'), // NULLs last
                'position' => SORT_ASC,
                'year' => SORT_DESC
            ])
            ->asArray()
            ->all();

        $uniqueSubpages = [];

        foreach ($subpages as $subpageData) {
            $subpageName = $subpageData['subpage'];

            if ($subpageName === 'cut-off' || !isset($uniqueSubpages[$subpageName])) {
                $uniqueSubpages[] = $subpageData;
            }
        }

        return $uniqueSubpages;
    }

    public function getProgramContent($programId, $collegeId)
    {
        $details = [];
        $query = CollegeProgram::find()
            ->where(['id' => $programId])
            ->andWhere(['college_id' => $collegeId])
            ->andWhere(['status' => CollegeCourse::STATUS_ACTIVE])
            ->one();

        if (empty($query)) {
            return [];
        }

        $programContent = CollegeProgramContent::find()
            ->where(['college_course_id' => $query->id])
            ->andWhere(['status' => CollegeProgramContent::STATUS_ACTIVE])
            ->one();

        if (!empty($programContent->qualification)) {
            $details = json_decode($programContent->qualification);
            if (!empty($details)) {
                if (!empty($details->eligibility)) {
                    foreach ($details->eligibility as $key => $value) {
                        $detailsEligibilityArr = json_decode(json_encode($value), true);
                        // Filter the array to remove empty values
                        $filteredArray = array_filter($detailsEligibilityArr);

                        // Check if the count of the filtered array is zero
                        if (count($filteredArray) != 3) {
                            unset($details->eligibility[$key]);
                        }
                    }
                }

                if (!empty($details->exams)) {
                    foreach ($details->exams as $key => $value) {
                        $detailsExamArr = json_decode(json_encode($value), true);
                        if (!empty($detailsExamArr['marks'])) {
                            // Filter the array to remove empty values
                            $filteredArray = array_filter($detailsExamArr);

                            // Check if the count of the filtered array is zero
                            if (count($filteredArray) === 0) {
                                unset($details->exams[$key]);
                            }
                        } else {
                            unset($details->exams[$key]);
                        }
                    }
                }
            }
        }
        if (!empty($details)) {
            $programContent->qualification = json_encode($details);
        }
        return $programContent ?? [];
    }

    public function getCourse($slug)
    {
        return Course::find()->active()->bySlug($slug)->one();
    }

    //will change when all data is update
    public function formateFee(array $data)
    {
        $feesData = array_column($data, 'fees');
        $count = count($feesData);
        $dataArr = [];

        if (!empty($feesData)) {
            $dataArr['Tuition Fees'] = array_sum($feesData) / $count;
        }

        return $dataArr ?? [];
    }

    //will move
    public function getCutOffData($collegeId)
    {
        return [];
        $query = new Query();
        $query->select('*')
            ->from('gmu_college_cutoff')
            ->where(['gmu_college_id' => $collegeId])
            ->andWhere(['not', ['gmu_course_type_short' => 'NULL']])
            ->orderBy(['gmu_discipline' => SORT_ASC])
            ->orderBy(['gmu_course_type' => SORT_ASC])
            ->orderBy(['year' => SORT_DESC]);

        $cutoff = $query->all(\Yii::$app->gmudb);

        if (!empty($cutoff)) {
            $data = [];

            $allowed = ['gmu_discipline', 'gmu_course_type', 'gmu_course_type_short', 'gmu_program_name', 'CATEGORY', 'Quota_AI_CR', 'Quota_AI_OR', 'Quota_HS_CR', 'Quota_HS_OR', 'Quota_OS_CR', 'Quota_OS_OR', 'Quota_AI', 'Quota_HS', 'MARKS', 'MARKS_Female', 'MARKS_Male', 'RANK', 'RANK_Female', 'RANK_Male', 'RANK_RANGE_CR', 'RANK_RANGE_Female', 'RANK_RANGE_Male', 'RANK_RANGE_OR', 'PERCENTILE', 'quota_filter'];
            $aggregate = ['Quota_AI_CR', 'Quota_AI_OR', 'Quota_HS_CR', 'Quota_HS_OR', 'Quota_OS_CR', 'Quota_OS_OR', 'Quota_AI', 'Quota_HS', 'MARKS', 'MARKS_Female', 'MARKS_Male', 'RANK', 'RANK_Female', 'RANK_Male', 'RANK_RANGE_CR', 'RANK_RANGE_Female', 'RANK_RANGE_Male', 'RANK_RANGE_OR', 'PERCENTILE'];

            $operation_keys = [];
            $disc_level_exams = [];
            $meta_courses = [];

            foreach ($cutoff as $key => $value) {
                $operation[$value['gmu_discipline']][$value['gmu_exam_name']][$value['year']][$value['round']][$value['gmu_course_type']][$value['CATEGORY']][$key] = array_filter(array_intersect_key($value, array_flip($allowed)));
                $newArry[$value['gmu_discipline']][$value['gmu_exam_name']][$value['year']][$value['round']] = array_keys(array_filter(array_intersect_key($value, array_flip($allowed))));

                foreach (array_filter(array_intersect_key($value, array_flip($aggregate))) as $da => $de) {
                    $aggregate_operation[$value['gmu_discipline']][$value['gmu_exam_name']][$value['year']][$value['round']][$value['gmu_course_type']][$value['CATEGORY']][$da][] = $de;
                }

                $disc_level_exams[$value['gmu_discipline']]['exam_' . $value['gmu_exam_name']] = 1;
                $disc_level_years[$value['gmu_discipline']]['year_' . $value['year']] = 1;
                $disc_level_rounds[$value['gmu_discipline']]['round_' . $value['round']] = 1;
                $disc_level_courses[$value['gmu_discipline']]['course_' . Inflector::slug($value['gmu_course_type'])] = 1;

                $exam_level_years[$value['gmu_exam_name']]['year_' . $value['year']] = 1;
                $exam_level_rounds[$value['gmu_exam_name']]['round_' . $value['round']] = 1;
                $exam_level_courses[$value['gmu_exam_name']]['course_' . Inflector::slug($value['gmu_course_type'])] = 1;

                $year_level_rounds[$value['gmu_exam_name']][$value['year']]['round_' . $value['round']] = 1;
                $year_level_courses[$value['gmu_exam_name']][$value['year']]['course_' . Inflector::slug($value['gmu_course_type'])] = 1;

                $data['all_disciplines'][$value['gmu_discipline']] = 1;
                $data['all_exams'][$value['gmu_exam_name']] = 1;
                $data['all_years'][$value['year']] = 1;
                $data['all_rounds'][$value['round']] = 1;
                $data['all_courses'][Inflector::slug($value['gmu_course_type'])] = 1;
                $meta_courses[$value['gmu_course_type']] = 1;

                $data['mapping_courses'][Inflector::slug($value['gmu_course_type'])] = $value['gmu_course_type_short'];
            }

            krsort($data['all_years']);
            ksort($data['all_rounds']);
            ksort($data['all_courses']);

            foreach ($disc_level_exams as $disc => $disc_details) {
                $disc_details = array_keys($disc_details);
                $disc_level_exams_final[$disc] = implode(' ', ($disc_details));
            }
            foreach ($disc_level_years as $disc => $disc_details) {
                $disc_details = array_keys($disc_details);
                $disc_level_years_final[$disc] = implode(' ', ($disc_details));
            }
            foreach ($disc_level_rounds as $disc => $disc_details) {
                $disc_details = array_keys($disc_details);
                $disc_level_rounds_final[$disc] = implode(' ', ($disc_details));
            }
            foreach ($disc_level_courses as $disc => $disc_details) {
                $disc_details = array_keys($disc_details);
                $disc_level_courses_final[$disc] = implode(' ', ($disc_details));
            }
            foreach ($data['all_disciplines'] as $disc => $index) {
                $data['disc_level'][$disc] = $disc_level_exams_final[$disc] . ' ' . $disc_level_years_final[$disc] . ' ' . $disc_level_rounds_final[$disc] . ' ' . $disc_level_courses_final[$disc];
            }

            foreach ($exam_level_years as $exam => $exam_details) {
                $exam_details = array_keys($exam_details);
                $exam_level_years_final[$exam] = implode(' ', ($exam_details));
            }
            foreach ($exam_level_rounds as $exam => $exam_details) {
                $exam_details = array_keys($exam_details);
                $exam_level_rounds_final[$exam] = implode(' ', ($exam_details));
            }
            foreach ($exam_level_courses as $exam => $exam_details) {
                $exam_details = array_keys($exam_details);
                $exam_level_courses_final[$exam] = implode(' ', ($exam_details));
            }
            foreach ($data['all_exams'] as $exam => $index) {
                $data['exam_level'][$exam] = $exam_level_years_final[$exam] . ' ' . $exam_level_rounds_final[$exam] . ' ' . $exam_level_courses_final[$exam];
            }

            foreach ($year_level_courses as $exam => $exam_details) {
                foreach ($exam_details as $year => $year_details) {
                    $year_details = array_keys($year_details);
                    $year_level_courses_final[$exam][$year] = implode(' ', ($year_details));
                }
            }
            foreach ($year_level_rounds as $exam => $exam_details) {
                foreach ($exam_details as $year => $year_details) {
                    $year_details = array_keys($year_details);
                    $year_level_rounds_final[$exam][$year] = implode(' ', ($year_details));
                }
            }
            foreach ($newArry as $discipline => $disc_details) {
                foreach ($disc_details as $exam => $exam_details) {
                    foreach ($exam_details as $year => $year_details) {
                        $data['year_level'][$exam][$year] = $year_level_rounds_final[$exam][$year] . ' ' . $year_level_courses_final[$exam][$year];
                    }
                }
            }

            $data['cutoff'] = $operation;
            $data['cutoff_keys'] = $newArry;
            $data['cutoff_avg'] = $aggregate_operation;

            $full_forms['MARKS'][0] = $full_forms['MARKS'][1] = 'MARKS';
            $full_forms['MARKS_Male'][0] = 'MARKS';
            $full_forms['MARKS_Male'][1] = 'Male';
            $full_forms['MARKS_Female'][0] = 'MARKS';
            $full_forms['MARKS_Female'][1] = 'Female';
            $full_forms['PERCENTILE'][0] = $full_forms['PERCENTILE'][1] = 'PERCENTILE';
            $full_forms['Quota_AI'][0] = $full_forms['Quota_AI'][1] = 'ALL INDIA QUOTA';
            $full_forms['Quota_AI_OR'][0] = 'ALL INDIA QUOTA';
            $full_forms['Quota_AI_OR'][1] = 'Open Rank';
            $full_forms['Quota_AI_CR'][0] = 'ALL INDIA QUOTA';
            $full_forms['Quota_AI_CR'][1] = 'Closing Rank';
            $full_forms['Quota_HS'][0] = $full_forms['Quota_HS'][1] = 'HOME STATE QUOTA';
            $full_forms['Quota_HS_OR'][0] = 'HOME STATE QUOTA';
            $full_forms['Quota_HS_OR'][1] = 'Open Rank';
            $full_forms['Quota_HS_CR'][0] = 'HOME STATE QUOTA';
            $full_forms['Quota_HS_CR'][1] = 'Closing Rank';
            $full_forms['Quota_OS_OR'][0] = 'OTHER STATE QUOTA';
            $full_forms['Quota_OS_OR'][1] = 'Open Rank';
            $full_forms['Quota_OS_CR'][0] = 'OTHER STATE QUOTA';
            $full_forms['Quota_OS_CR'][1] = 'Closing Rank';
            $full_forms['RANK'][0] = $full_forms['RANK'][1] = 'RANK';
            $full_forms['RANK_Male'][0] = 'Gender Based Ranks';
            $full_forms['RANK_Male'][1] = 'Male';
            $full_forms['RANK_Female'][0] = 'Gender Based Ranks';
            $full_forms['RANK_Female'][1] = 'Female';
            $full_forms['RANK_RANGE_Male'][0] = 'Gender Based Rank Range';
            $full_forms['RANK_RANGE_Male'][1] = 'Male';
            $full_forms['RANK_RANGE_Female'][0] = 'Gender Based Rank Range';
            $full_forms['RANK_RANGE_Female'][1] = 'Female';
            $full_forms['RANK_RANGE_OR'][0] = 'RANK RANGE';
            $full_forms['RANK_RANGE_OR'][1] = 'Open Rank';
            $full_forms['RANK_RANGE_CR'][0] = 'RANK RANGE';
            $full_forms['RANK_RANGE_CR'][1] = 'Closing Rank';

            $data['full_forms'] = $full_forms;
        }

        return $data ?? [];
    }

    public function getCollegeByDiscipline($collegeId, $cityId, $courseId = null, $limit = 10)
    {
        $key = md5(__CLASS__ . '\/' . __FUNCTION__ . json_encode([$collegeId, $cityId, $limit]));
        $data = Yii::$app->cache->getOrSet($key, function () use ($collegeId, $cityId, $courseId, $limit) {
            if (empty($cityId)) {
                return [];
            }

            $colleges = [];

            $query = new Query();
            $query->select(['COUNT(cc.id) as count', 'course_id'])
                ->from('college_program as cc')
                ->innerJoin('course as co', 'co.id = cc.course_id')
                ->where(['cc.status' => 1])
                ->andWhere(['cc.college_id' => $collegeId])
                ->groupBy('cc.course_id')
                ->orderBy(['COUNT(cc.id)' => SORT_DESC]);

            if (!empty($courseId)) {
                $query->andWhere(['cc.course_id' => $courseId]);
            }
            $result = $query->all();

            foreach ($result as $r) {
                $query1 = new Query();
                $query1->select(['c.*', 's.name as streamName', 's.slug as streamSlug', 's.id as streamId'])
                    ->from('college as c')
                    ->innerJoin('city as ct', 'ct.id = c.city_id')
                    ->innerJoin('college_program as cc', 'cc.college_id = c.id')
                    ->innerJoin('course as co', 'co.id = cc.course_id')
                    ->innerJoin('stream as s', 's.id = co.stream_id')
                    ->where(['ct.id' => $cityId])
                    ->andWhere(['cc.course_id' => $r['course_id']])
                    ->andWhere(['not', ['c.id' => $collegeId]])
                    ->andWhere(['c.status' => College::STATUS_ACTIVE])
                    ->groupBy('c.id')
                    ->limit($limit);

                $colleges = $query1->all();
                if (!empty($colleges)) {
                    break;
                }
            }

            $sponsored = self::checkFilterPageStatus($colleges, '', $cityId, $courseId);

            return ['colleges' => $colleges, 'sponsorStatus' => $sponsored];
        }, 60 * 60 * 24 * 90);

        return $data ?? [];
    }

    /**
     * Get the CI Page list
     * @param array | $param , object | $college College
     * @return array |[]
     */
    public function getCoursePageList($param, $college)
    {
        $items = [];

        foreach ($param as $key => $value) {
            $data = ProgramDocuments::find()
                ->where(['course_slug' => $key])
                ->andWhere(['college_id' => $college->id])
                ->andWhere(['isCiPage' => true])
                ->one();

            if (isset($data->isCiPage)) {
                $items[$key] = $value;
            }
        }

        return $items ?? [];
    }

    public static function updateCourseDocument(CollegeCourse $collegeCourse)
    {
        $collection = CollectionCollegeCourse::find()
            ->where(['college_id' => (int) $collegeCourse->college_id])
            ->andWhere(['college_course_id' => (int) $collegeCourse->id])
            ->andWhere(['program_slug' => $collegeCourse->slug])
            ->one();

        if (!$collection) {
            $collection = new CollectionCollegeCourse();
        }

        if (empty($collegeCourse->course)) {
            return false;
        }
        $collection->college_course_id = (int) $collegeCourse->id;
        $collection->college_id = (int) $collegeCourse->college_id;
        $collection->program = $collegeCourse->name;
        $collection->program_slug = $collegeCourse->slug;
        $collection->fees = (int) $collegeCourse->fee;
        $collection->duration = $collegeCourse->duration;
        $collection->total_seat = $collegeCourse->total_seat;
        $collection->type = $collegeCourse->type;
        $collection->degree = $collegeCourse->degree;
        $collection->mode = $collegeCourse->mode;
        $collection->application_link = $collegeCourse->application_link;
        $collection->salary = $collegeCourse->salary;
        $collection->stream_id = (int) $collegeCourse->course->stream_id ?? '';
        $collection->stream_slug = $collegeCourse->course->stream->slug ?? '';
        $collection->stream_name = $collegeCourse->course->stream->name ?? '';
        $collection->specialization_id = !empty($collegeCourse->specialization_id) ? (int) $collegeCourse->specialization_id : '';

        if (!empty($collegeCourse->specialization)) {
            $collection->specialization_slug = $collegeCourse->specialization->slug ?? '';
            $collection->specialization_name = $collegeCourse->specialization->display_name ?? $collegeCourse->specialization->name;
        }

        $collection->course_id = $collegeCourse->course->id ?? '';
        $collection->course = $collegeCourse->course->name ?? '';
        $collection->course_short_name = $collegeCourse->course->short_name ?? '';
        $collection->course_slug = $collegeCourse->course->slug ?? '';
        $courseHighlights = CourseService::getCourseFeature($collegeCourse->course->id);

        $collection->course_duration = (!empty($courseHighlights['duration'][0])) ? $courseHighlights['duration'][0] : '';
        $collection->isCiPage = CollegeService::getCiPageInfo($collegeCourse->college_id, $collegeCourse->course->id);

        //brochure
        $broucher = CollegeHelper::getCourseBrochure($collegeCourse->course->id, $collegeCourse->college_id);
        $collection->course_brochure = !empty($broucher) ? $broucher->pdf : '';

        $collegeCourseContent = CollegeService::getCourseContent($collegeCourse->course->id, $collegeCourse->college_id);
        $collection->course_eligibility = !empty($collegeCourseContent) ? $collegeCourseContent->eligibility : '';
        $collection->course_position = $collegeCourse->course->position > 0 ? $collegeCourse->course->position : '';
        $collection->courseAvgFees = CollegeService::getAvgFees($collegeCourse->course->id, $collegeCourse->college_id);

        $exam = ArrayHelper::map($collegeCourse->exams, 'id', 'display_name');
        $collection->exams = implode(',', $exam);
        $collection->status = (int) $collegeCourse->status;
        $collection->pageIndex = (int) $collegeCourse->page_index ?? '';

        if ($collection->save()) {
            echo "{$collection->program_slug} \n";
        } else {
            print_r($collection->getErrors());
        }
    }

    //update the exam on collegeCourse document
    public static function updateCourseDocumentExam($examId, $college_course_id)
    {
        $exams = implode(',', ArrayHelper::map(Exam::find()->where(['id' => $examId])->all(), 'id', 'display_name'));
        $model = ProgramDocuments::find()->where(['college_program_id' => $college_course_id])->one();
        if (empty($model)) {
            return [];
        }
        if (empty($model->exams)) {
            $model->exams = $exams;
            $model->save();
        } else {
            $checkExam = in_array($exams, explode(',', $model->exams));
            if ($checkExam !== true) {
                $model->exams = $exams;
                $model->save();
            }
        }
    }

    public static function getCiPageInfo($collegeId, $courseId)
    {
        $collegeCourse = CollegeProgram::find()
            ->where(['college_id' => $collegeId])
            ->andWhere(['course_id' => $courseId])
            ->andWhere(['status' => CollegeProgram::STATUS_ACTIVE])
            ->count();

        if ($collegeCourse < 0) {
            return false;
        } else {
            return true;
        }
    }

    public static function getAvgFees($courseId, $collegeId)
    {
        $query = new Query();
        $query->select(['cf.fees', 'cf.college_program_id'])
            ->from(CollegeProgramFees::tableName() . ' as cf')
            ->innerJoin(CollegeProgram::tableName() . ' as cp', 'cp.id = cf.college_program_id')
            ->innerJoin(Program::tableName() . ' as pro', 'pro.id = cp.program_id')
            ->where(['cp.college_id' => $collegeId, 'cp.course_id' => $courseId])
            ->andWhere(['cf.status' => CollegeCourse::STATUS_ACTIVE])
            ->andWhere(['cp.status' => CollegeCourse::STATUS_ACTIVE])
            ->andWhere(['pro.type' => 'full_time'])
            ->andWhere(['cf.type' => 'tuition_fees'])
            ->andWhere([
                'and',
                ['not like', 'pro.name', '%' . 'Integrated' . '%', false],
                ['not like', 'pro.name', '%' . 'Hons' . '%', false],
                ['not like', 'pro.name', '%' . 'Diploma' . '%', false],
                ['not like', 'pro.name', '%' . 'Distance' . '%', false],
                ['not like', 'pro.name', '%' . '+' . '%', false],
                ['not like', 'pro.name', '%' . 'Dual Degree' . '%', false],
                ['not like', 'pro.name', '%' . 'Lateral' . '%', false],
                ['not like', 'pro.name', '%' . '{' . '%', false],
                ['not like', 'pro.name', '%' . 'tie up' . '%', false],
                ['not like', 'pro.name', '%' . 'certificate' . '%', false],
                ['not like', 'pro.name', '%' . 'Fellowship' . '%', false]
            ])
            ->andWhere(['or', ['not like', 'pro.name', '%' . '{' . '%', false], ['not like', 'pro.name', '%' . '(' . '%', false]])->all();

        $data = $query->all();
        if (empty($data)) {
            return 0;
        }
        $sum = array_sum(array_column($data, 'fees'));
        $count = count(array_unique(array_column($data, 'college_program_id')));

        $avgFees = ($sum > 0) ? $sum / $count : 0;

        return round($avgFees);
    }

    public function getAdTargetData(College $college)
    {
        $hash = __CLASS__ . __FUNCTION__ . md5(base64_encode(serialize($college)));
        $data = Yii::$app->cache->getOrSet($hash, function () use ($college) {
            $query = ProgramDocuments::find()->select(['degree', 'stream_id', 'course_slug'])
                ->where(['college_id' => $college->id])
                ->all();

            $degree = array_unique(ArrayHelper::getColumn($query, 'degree'));
            $courses = array_unique(ArrayHelper::getColumn($query, 'course_slug'));
            $streamIds = array_unique(ArrayHelper::getColumn($query, 'stream_id'));
            $streams = Stream::find()->select('slug')->where(['id' => $streamIds])->all();

            foreach ($streams as $stream) {
                $streamList[] = ArrayHelper::getValue($stream, 'slug');
            }

            return [
                'CollegeName' => $college->slug ?? '',
                'State' => !empty($college->city) ? $college->city->state->slug : '',
                'City' => !empty($college->city) ? $college->city->slug : '',
                'degree' => $degree ?? '',
                'discipline' => $streamList ?? '',
                'courses' => $courses ?? ''
            ];
        }, 60 * 60 * 6, new TagDependency(['tags' => 'get-ad-target-data-' . $college->id]));

        return $data;
    }

    public function leadSponserCollegeList($course, $qualification, $state_id, $limit = 3, $orderBy = SORT_DESC)
    {
        $query = new Query();
        $query->select('sponser.`college_id`, mapping.`lead_course`,details.`course` course_name, details.`fees`, details.`eligibility`')
            ->from('gmu_leads_course_qualification_mapping as mapping')
            ->leftJoin('gmu_sponsor_colleges as sponser', 'mapping.course = sponser.discipline_course')
            ->leftJoin('gmu_sponsor_colleges_carousel_details as details', 'sponser.college_id = details.college_id AND details.course_name = sponser.discipline_course')
            ->where(['mapping.lead_course' => $course, 'mapping.lead_qualification' => $qualification]);

        if ($state_id == 'undefined') {
            $state_id = 999;
        }

        if (isset($state_id) && !empty($state_id)) {
            $query->andWhere(['sponser.location_id' => $state_id]);
        } else {
            $query->andWhere(['sponser.location_id' => 999]);
        }

        $query->andWhere(['sponser.is_deleted' => 0, 'details.is_deleted' => 0])
            ->orderBy(['sponser.location_id' => $orderBy])
            ->limit($limit);
        $query->distinct();

        $data = $query->all(\Yii::$app->gmudb);

        return $data ?? [];
    }

    public function sponserCollegeLead($course, $qualification, $location, $limit = 3)
    {
        $sponserCollegeList = $this->leadSponserCollegeList($course, $qualification, $location, $limit);
        foreach ($sponserCollegeList as $list) {
            $collegeData = College::find()->active()->where(['old_id' => $list['college_id']])->one();
            $fees = $list['fees'];
            $eligibility = $list['eligibility'];
            $course = $list['lead_course'];
            $displayCourse = $list['course_name'];

            if (!empty($collegeData->name)) {
                $items[] = [
                    'collegeId' => $collegeData->id,
                    'name' => $collegeData->name ?? '',
                    'slug' => $collegeData->slug ?? '',
                    'image' => $collegeData->image ?? '',
                    'fees' => $fees,
                    'interestedCourse' => $course,
                    'displayCourse' => $displayCourse,
                    'eligibility' => $eligibility,
                ];
            }
        }

        return $items ?? [];
    }

    //will remove as all tha pages is revamped
    public function getRedirectionLink($entity, $entityId)
    {
        $currentDate = Carbon::now()->toDateString();

        $redirection = Redirection::find()
            ->where(['entity' => $entity])
            ->andWhere(['entity_id' => $entityId])
            ->andWhere(['status' => Redirection::STATUS_ACTIVE])
            ->one();

        if (!empty($redirection)) {
            $redirectionDate = date('Y-m-d', strtotime($redirection->expired_at));
            if ($currentDate <= $redirectionDate) {
                return $redirection;
            }
        }
    }

    //update the course broucher in document
    public static function updateCourseDocumentBroucher(Brochure $brochure)
    {
        $broucher = CollegeHelper::getCourseBrochure($brochure->course->id, $brochure->college_id);

        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);

        $collection->update(
            ['college_id' => $brochure->college_id, 'course_slug' => $brochure->course->slug],
            ['course_brochure' => !empty($broucher) ? $broucher->pdf : '']
        );
    }

    //update course postion in document
    public static function updateCourseDocumentPosition(Course $course)
    {
        if (!empty($course->position)) {
            $position = $course->position > 0 ? (int) $course->position : '';
        } else {
            $position = '';
        }
        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);

        $collection->update(['course_slug' => $course->slug], ['course_position' => $position]);
    }

    //update course pageIndex in document
    public static function updateCoursePageIndexDocument(CollegeCourseContent $course)
    {
        // if (!empty($course->page_index)) {
        //     $index = (int)$course->page_index > 0 ? (int)$course->page_index : '';
        // } else {
        //     $index = '';
        // }
        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);

        $collection->update(['course_id' => (int)$course->course_id, 'college_id' => (int)$course->college_id], ['coursePageIndex' => $course->page_index]);
    }

    //not in user update the Eligibility
    public static function updateCourseAvgFee(CollegeProgram $collegeCourse)
    {
        $avgFee = self::getAvgFees($collegeCourse->course->id, $collegeCourse->college_id);

        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);
        $arr = ['courseAvgFees' => $avgFee];
        $collection->update(
            ['college_id' => $collegeCourse->college_id, 'course_id' => $collegeCourse->course->id],
            $arr
        );
    }

    public function getplacementHighlights($fetaureGroupId, $collegeId)
    {
        $query = new Query();
        $query->select(['fv.value', 'f.name'])
            ->from('feature f')
            ->leftJoin('feature_value as fv', 'fv.feature_id = f.id')
            ->leftJoin('college_feature_value as cfv', 'cfv.feature_value_id = fv.id')
            ->where(['f.feature_group_id' => $fetaureGroupId])
            ->andWhere(['cfv.college_id' => $collegeId])
            ->andWhere(['fv.status' => FeatureValue::STATUS_ACTIVE]);

        $highlights = $query->all();

        return $highlights ?? [];
    }

    //insert the college course content
    public static function checkCollegeCourseContent(CollegeCourse $collegeCourse)
    {
        $check = CollegeCourseContent::find()
            ->where(['college_id' => $collegeCourse->college_id])
            ->andWhere(['course_id' => $collegeCourse->course_id])
            ->one();

        if ($check) {
            return false;
        }
        $model = new CollegeCourseContent();
        $model->college_id = $collegeCourse->college_id;
        $model->course_id = $collegeCourse->course_id;
        $model->status = CollegeCourseContent::STATUS_INACTIVE;

        $model->save();
    }

    //parent College
    public function getParentCollege($collegeId)
    {
        if (empty($collegeId)) {
            return '';
        }

        $college = College::find()->select(['name', 'display_name', 'slug', 'cover_image', 'logo_image', 'city_id'])
            ->byId($collegeId)
            ->active()
            ->one();

        if (empty($college)) {
            return null;
        }

        return [
            'name' => $college->name,
            'display_name' => $college->display_name,
            'slug' => $college->slug,
            'logo_image' => $college->logo_image,
            'city' => empty($college->city->name) ? '' : $college->city->name,
            'state' => !empty($college->city->state) ? $college->city->state->name : '',
        ];
    }

    public function getCollegeCourseBasedCutOff($collegeId)
    {
        $query = new Query();
        $query->select([
            'cd.college_id as collegeId',
            'cd.course_id as courseId',
            'cd.specialization_id as specializationId',
            'cd.category',
            'cd.gender',
            'cd.year',
            'cd.round',
            'cd.opening_rank',
            'cd.closing_rank',
            'cd.percentile',
            'cd.closing_score',
            'cd.exam_id as examId',
            'e.display_name as examName',
            'cd.year',
            'sp.display_name as specializationName',
            'cr.short_name as courseName'
        ])
            ->from(CutOff::tableName() . ' cd')
            ->leftJoin('college c', 'cd.college_id = c.id')
            ->leftJoin('exam e', 'cd.exam_id = e.id')
            ->leftJoin('course cr', 'cd.course_id = cr.id')
            ->leftJoin(Specialization::tableName() . ' sp', 'cd.specialization_id = sp.id')
            ->where(['cd.college_id' => $collegeId])
            ->orderBy(['cd.year' => SORT_DESC, 'cd.exam_id' => SORT_ASC]);

        $results = $query->all();

        if (empty($results)) {
            return [];
        }
        $items = [];
        $years = array_unique(array_column($results, 'year'));

        $gender = 3;
        $category = 1;
        foreach ($results as $data) {
            if (!empty($data['specializationId'])) {
                if ($data['gender'] == $gender && $data['category'] == $category) {
                    if (!empty($data['closing_score'])) {
                        $items['scoreArr'][$data['examId'] . '_' . $data['examName'] . '_' . $data['year']][$data['specializationName']] = [
                            'courseName' => $data['courseName'] . ' ' . $data['specializationName'],
                            'closing_score' => $data['closing_score']
                        ];
                    }
                    if (!empty($data['percentile'])) {
                        $items['percentileArr'][$data['examId'] . '_' . $data['examName'] . '_' . $data['year']][$data['specializationName']] = [
                            'courseName' => $data['courseName'] . ' ' . $data['specializationName'],
                            'percentile' => $data['percentile']
                        ];
                    }
                    if (!empty($data['opening_rank']) && !empty($data['closing_rank'])) {
                        $items['opclArr'][$data['examId'] . '_' . $data['examName'] . '_' . $data['year']][$data['specializationName']] = [
                            'courseName' => $data['courseName'] . ' ' . $data['specializationName'],
                            'open_rank' => $data['opening_rank'],
                            'close_rank' => $data['closing_rank']
                        ];
                    }
                }
            }

            $items['year'] = $years[0];
        }

        if (!empty($items['opclArr'])) {
            $examid = explode('_', array_key_first($items['opclArr']));
            $items['opclArr'] = array_shift($items['opclArr']);
            $items['opclArr_examName'] = $examid[1];
        }

        if (!empty($items['scoreArr'])) {
            $examid = explode('_', array_key_first($items['scoreArr']));
            $items['scoreArr'] = array_shift($items['scoreArr']);
            $items['scoreArr_examName'] = $examid[1];
        }

        if (!empty($items['percentileArr'])) {
            $examid = explode('_', array_key_first($items['percentileArr']));
            $items['percentileArr'] = array_shift($items['percentileArr']);
            $items['percentileArr_examName'] = $examid[1];
        }

        return $items ?? [];
    }

    /** get the child course */
    public function getParentSpecialization($course, $limit = 10)
    {
        //hide the specialization of other courses
        if ($course->parent_id == 610) {
            return [];
        }

        $courses = Course::find()
            ->select(['name', 'short_name', 'slug'])
            ->byExcludeId($course->id)
            ->byParentId(Course::tableName(), $course->parent_id ?? $course->id)
            ->innerJoinWith('courseContent')
            ->andWhere(['course.status' => Course::STATUS_ACTIVE])
            ->limit($limit)
            ->orderBy('short_name')
            ->groupBy('slug')
            ->all();

        return $courses ?? [];
    }

    /** get the specialization based on location */
    public function getSpecializaionByLocation($course)
    {
        $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
        $courses = $collection->aggregate([
            ['$unwind' => ['path' => '$course', 'preserveNullAndEmptyArrays' => true]],
            [
                '$match' => ['course.course_slug' => $course->slug],
                'is_sponsored' => College::SPONSORED_NO,
                'status' => College::STATUS_ACTIVE
            ],
            ['$unwind' => ['path' => '$course.specialization']],
            ['$group' => ['_id' => '$course.specialization', 'college' => ['$addToSet' => '$college_id']]],
            ['$project' => ['_id' => 0, 'specialization' => '$_id', 'collegecount' => ['$size' => '$college']]],
            ['$sort' => ['collegecount' => -1]],
            ['$limit' => 10]
        ]);
        return $courses ?? [];
    }

    /** get the city name  based on course fron the filter */
    public function getStateListByCourse($course, $state, $slugType = 'course', $specialization = '', $limit = 10)
    {
        // if (empty($state)) {
        //     return [];
        // }

        $queries = Filter::find()->where(['in', 'slug', $course->slug])->one();
        $slugTypes = ($slugType == 'course') ? 'course_slug' : 'stream_slug';
        if (!empty($queries)) {
            $courseSlug = !isset(DataHelper::$collegeFilter301Url[$course->slug]) ? $course->slug : DataHelper::$collegeFilter301Url[$course->slug];
            $stateSlug = $state->slug ?? '';
            $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
            $match = [
                'course.' . $slugTypes => $courseSlug,
                'state_slug' => ['$ne' => $stateSlug],
                'is_sponsored' => College::SPONSORED_NO,
                'status' => College::STATUS_ACTIVE
            ];
            if (!empty($specialization)) {
                $match['course.specialization'] = $specialization;
            }
            $courses = $collection->aggregate(
                [
                    [
                        '$match' => $match
                    ],
                    ['$group' => ['_id' => ['state_name' => '$state_name', 'state_slug' => '$state_slug'], 'colleges' => ['$addToSet' => '$college_id']]],
                    ['$project' => ['_id' => 0, 'stateSlug' => '$_id.state_slug', 'stateName' => '$_id.state_name', 'collegeCount' => ['$size' => '$colleges']]],
                    ['$sort' => ['collegeCount' => -1]],
                    ['$limit' => $limit]
                ]
            );

            $result = [];
            foreach ($courses as $course) {
                if ($course['collegeCount'] > 1) {
                    $result[] = $course;
                }
            }

            return $result;
        }
        return [];
    }

    /** get the city name  based on course fron the filter */
    public function getCityListByCourse($course, $city, $slugType = 'course', $specialization = '', $limit = 10)
    {
        // if (empty($city)) {
        //     return [];
        // }
        $queries = Filter::find()->where(['in', 'slug', $course->slug])->one();
        $slugTypes = ($slugType == 'course') ? 'course_slug' : 'stream_slug';
        if (!empty($queries)) {
            $courseSlug = !isset(DataHelper::$collegeFilter301Url[$course->slug]) ? $course->slug : DataHelper::$collegeFilter301Url[$course->slug];
            $citySlug = $city->slug ?? '';
            $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
            $match = [
                'course.' . $slugTypes => $courseSlug,
                'state_slug' => ['$ne' => $citySlug],
                'is_sponsored' => College::SPONSORED_NO,
                'status' => College::STATUS_ACTIVE
            ];
            if (!empty($specialization)) {
                $match['course.specialization'] = $specialization;
            }
            $courses = $collection->aggregate(
                [
                    [
                        '$match' => $match
                    ],
                    ['$group' => ['_id' => ['city_name' => '$city_name', 'city_slug' => '$city_slug'], 'colleges' => ['$addToSet' => '$college_id']]],
                    ['$project' => ['_id' => 0, 'citySlug' => '$_id.city_slug', 'cityName' => '$_id.city_name', 'collegeCount' => ['$size' => '$colleges']]],
                    ['$sort' => ['collegeCount' => -1]],
                    ['$limit' => $limit]
                ]
            );

            $result = [];
            foreach ($courses as $course) {
                if ($course['collegeCount'] > 1) {
                    $result[] = $course;
                }
            }

            return $result;
        }
        return [];
    }

    /** get the college page */
    public function getCollegePage($collegeId)
    {
        $data = array_keys($this->getAllContents($collegeId));
        $items = [];

        if (empty($data)) {
            return [];
        }

        foreach ($data as $d) {
            $items[$d] = isset(CollegeHelper::$collegePageCi[$d]) ? CollegeHelper::$collegePageCi[$d] : '';
        }
        return $items;
    }

    public function getRecentActivityByEntity($entity, $entityId, $page = null)
    {
        $subpage = ['books', 'sample-papers', 'reference-books', 'previous-years-papers', 'mock-sample-tests', 'images-videos', 'qna', 'reviews', 'verdict'];
        $data_exist = array_keys($subpage, $page);
        if (!empty($data_exist)) {
            return [];
        }
        $expiry = date('Y-m-d H:i:s');
        $scheduledDate = date('Y-m-d H:i:s');
        $entityId = (int) $entityId;
        $arrEntity = RecentActivity::entityType();
        if (is_array($entity)) {
            $entity_Id = array_search(substr($entity['entity'], 0, -1), $arrEntity);
            $entity_name = substr($entity['entity'], 0, -1);
        } else {
            if ($entity == 'articles') {
                $entity = substr($entity, 0, -1);
            }
            $entity_Id = array_search($entity, $arrEntity);
            $entity_name = $entity;
        }

        if (is_array($entity) && !empty($entity['entityName'])) {
            $entityInfo = substr($entity['entityName'], 0, -1);
            $table_name = 'article_' . $entityInfo;
            $column_name = $entityInfo . '_id';
            $subQuery = (new \yii\db\Query())->select(['article_id'])->from($table_name)->where([$column_name => $entityId]);
        }

        $data1 = (new \yii\db\Query())
            ->select(['text', 'created_at', 'updated_at', 'entity', 'entity_id', 'page', 'recent_activity.id'])
            ->from('recent_activity')
            ->innerJoin('recent_activity_tracker', 'recent_activity.id = recent_activity_tracker.recent_activity_id')
            ->where(['entity' => $entity_Id]);

        if (is_array($entity) && !empty($entity['entityName'])) {
            $entityInfo = substr($entity['entityName'], 0, -1);
            $table_name = 'article_' . $entityInfo;
            $column_name = $entityInfo . '_id';
            $subQuery = (new \yii\db\Query())->select(['article_id'])->from($table_name)->where([$column_name => $entityId]);
            $data1 = $data1->andWhere(['in', 'entity_id', $subQuery]);
        } else {
            $data1 = $data1->andWhere(['entity_id' => $entityId]);
        }

        $data1 = $data1->andWhere(['or', ['page' => [$page, null]]])
            ->andWhere('expiry>:expiry', [':expiry' => $expiry])
            ->andWhere('scheduled_at <= :scheduled_at OR scheduled_at IS NULL', [':scheduled_at' => $scheduledDate])
            ->andWhere(['status' => RecentActivity::RECENT_ACTIVITY_STATUS_ACTIVE]);
        if ($entity_name != 'board') {
            $data6 = (new \yii\db\Query())
                ->select(['text', 'created_at', 'updated_at', 'entity', 'entity_id', 'page', 'recent_activity.id'])
                ->from('recent_activity')
                ->innerJoin('recent_activity_tracker', 'recent_activity.id = recent_activity_tracker.recent_activity_id');
            if ($entity_name == 'college') {
                $clg = (new \yii\db\Query())->select(['exam_id'])->from('college_exam')->where(['college_id' => $entityId])->all();
                $examID = self::getExamLatestRecord($clg, 'college');
                $data6 = $data6->where(['in', 'recent_activity_tracker.entity_id', $examID]);
            } else if ($entity_name == 'course') {
                $crs = (new \yii\db\Query())->select(['exam_id'])->from('exam_course')->where(['course_id' => $entityId])->all();
                $examID = self::getExamLatestRecord($crs, 'course');
                $data6 = $data6->where(['in', 'recent_activity_tracker.entity_id', $examID]);
            } else if ($entity_name == 'exam') {
                $data6 = $data6->where(['recent_activity_tracker.entity_id' => $entityId])
                    ->andWhere(['or', ['page' => [$page, null]]]);
            } else if ($entity_name == 'article') {
                $data6 = $data6->where(['recent_activity_tracker.entity_id' => $entityId]);
            }
            $data6->andWhere(['recent_activity_tracker.entity' => 3])
                ->andWhere('recent_activity.expiry>:expiry', [':expiry' => $expiry])
                ->andWhere('recent_activity.scheduled_at <= :scheduled_at OR recent_activity.scheduled_at IS NULL', [':scheduled_at' => $scheduledDate])
                ->andWhere(['recent_activity.status' => RecentActivity::RECENT_ACTIVITY_STATUS_ACTIVE]);
        } else {
            $data6 = '';
        }
        if ($data6 == '') {
            $data = (new yii\db\Query())
                ->select('*')
                ->from($data1)
                ->orderBy(['created_at' => SORT_DESC])
                ->all();
        } else {
            $data = (new yii\db\Query())
                ->select('*')
                ->from($data1->union($data6))
                ->orderBy(['created_at' => SORT_DESC])
                ->all();
        }
        return $data ?? [];
    }

    public static function getExamLatestRecord($examData, $entity = '')
    {
        if (empty($examData)) {
            return [];
        }
        $expiry = date('Y-m-d H:i:s');

        $collegeUniversityExam = [];
        $collegeUniversityExamState = [];
        $collegeUniversityExamNational = [];
        $totalLimit = RecentActivityTracker::LIMIT_RECORD;

        $exam_ids = [];
        foreach ($examData as $cl) {
            $exam_ids[] = $cl['exam_id'];
        }
        $query = (new Query())
            ->select(['entity_id', 'COUNT(recent_activity.id) as count'])
            ->from('recent_activity')
            ->innerJoin('recent_activity_tracker', 'recent_activity.id = recent_activity_tracker.recent_activity_id')
            ->where(['recent_activity_tracker.entity' => 3])
            ->andWhere(['in', 'recent_activity_tracker.entity_id', $exam_ids])
            ->andWhere(['>', 'recent_activity.expiry', $expiry])
            ->andWhere(['recent_activity.status' => RecentActivity::RECENT_ACTIVITY_STATUS_ACTIVE])
            ->groupBy('recent_activity_tracker.entity_id')
            ->orderBy(['updated_at' => SORT_DESC]);

        $examCount = $query->all();
        $examEntityCount = [];
        foreach ($exam_ids as $key => $value) {
            foreach ($examCount as $count) {
                if ($count['entity_id'] == $value) {
                    $examEntityCount[$count['entity_id']] = $count['count'];
                    break;
                } else {
                    $examEntityCount[$exam_ids[$key]] = 0;
                }
            }
        }

        if (empty($examEntityCount)) {
            return [];
        }

        foreach ($examData as $cl) {
            if ($examEntityCount[$cl['exam_id']] == 0) {
                continue;
            }

            $countUniversityExam = (new \yii\db\Query())
                ->from('exam')
                ->andWhere(['id' => $cl['exam_id']])
                ->andWhere(['exam_type_id' => 1])
                ->count();

            if ($countUniversityExam) {
                $collegeUniversityExam[] = $cl['exam_id'];
                continue;
            }
            //State Exam
            $countUniversityExamState = (new \yii\db\Query())
                ->from('exam')
                ->where(['id' => $cl['exam_id']])
                ->andWhere(['exam_type_id' => 2])
                ->count();

            if ($countUniversityExamState) {
                $collegeUniversityExamState[] = $cl['exam_id'];
                continue;
            }
            // National Exam
            $countUniversityExamNational = (new \yii\db\Query())
                ->from('exam')
                ->where(['id' => $cl['exam_id']])
                ->andWhere(['exam_type_id' => 3])
                ->count();

            if ($countUniversityExamNational) {
                $collegeUniversityExamNational[] = $cl['exam_id'];
                continue;
            }
        }
        if ($entity == 'college') {
            $examArray = array_merge($collegeUniversityExam, $collegeUniversityExamState, $collegeUniversityExamNational);
        } else {
            $examArray = array_merge($collegeUniversityExamNational, $collegeUniversityExamState, $collegeUniversityExam);
        }

        if (count($examArray) <= $totalLimit) {
            return $examArray;
        } else {
            $removeElement = count($examArray) - $totalLimit;
            for ($k = 0; $k < $removeElement; $k++) {
                array_pop($examArray);
            }

            return $examArray;
        }
    }

    public static function getFeaturedColleges($streamId, $limit = 10)
    {
        $key = 'feature-colleges-' . $streamId;
        $data = Yii::$app->cache->getOrSet($key, function () use ($streamId, $limit) {
            $query = new Query();
            $query->select(['college.name AS collegeName', 'college.slug', 'college.logo_image', 'city.name AS cityName', 'state.name AS stateName', 'stream.name as streamName'])
                ->from('college')
                ->innerJoin('city', 'city.id = college.city_id')
                ->innerJoin('state', 'state.id = city.state_id')
                ->innerJoin(CollegeProgram::tableName() . ' as cp', 'cp.college_id = college.id')
                ->innerJoin('course', 'course.id = cp.course_id AND course.stream_id = ' . $streamId)
                ->innerJoin('stream', 'stream.id = course.stream_id')
                ->where(['college.status' => 1])
                ->andWhere(['college.is_featured' => College::IS_FEATURED_YES])
                ->andWhere(['not', ['college.logo_image' => 'NULL']])
                ->groupBy('college.id')
                ->limit($limit)
                ->orderBy(['college.position' => SORT_ASC]);

            $results = $query->all();

            if (!empty($results)) {
                $results[0]['streamName'] = str_replace(' ', '', $results[0]['streamName']);
            }
            return $results ?? [];
        }, 60 * 60 * 7, new TagDependency(['tags' => 'feature-colleges-' . $streamId]));

        return $data ?? [];
    }

    public function sponsorCollege($request, $limit = 4)
    {
        if (empty($request) && empty($request['stream']) && (empty($request['currentCity']) || empty($request['currentCityIp']))) {
            return [];
        }

        $streamSlug = Stream::find()->select(['slug', 'name'])->where(['id' => (int) $request['stream']])->one();
        $cityId = '';

        if ($request['entity'] ==  College::ENTITY_COLLEGE || $request['entity'] == College::ENTITY_COLLEGE_LISTING) {
            $collegeCityId = College::find()->select(['city_id'])->where(['id' => $request['entity_id']])->one();
            $cityId = !empty($collegeCityId) && !empty($collegeCityId->city_id) ? $collegeCityId->city_id : '';
        } else {
            $cityId = !empty($request['currentCity']) ? (int) $request['currentCity'] : (!empty($request['currentCityIp']) ? (int) $request['currentCityIp'] : '');
        }

        $query = new Query();
        $query->select(['college.id', 'college.display_name', 'college.logo_image'])
            ->from([College::tableName()])
            ->leftJoin(CollegeCourseMapping::tableName() . ' as ccm', 'ccm.college_id = college.id')
            ->leftJoin(Course::tableName() . ' as c', 'c.id = ccm.course_id')
            ->where(['c.stream_id' => $request['stream']])
            ->andWhere(['college.city_id' => $cityId])
            ->andWhere(['college.status' => College::STATUS_ACTIVE])
            ->groupBy('ccm.college_id')
            ->orderBy(['ccm.college_id' => SORT_ASC]);

        $sponsorCollegesQuery = $query->all();

        if (empty($sponsorCollegesQuery)) {
            return [];
        }

        // Shuffle the array of results
        shuffle($sponsorCollegesQuery);

        // Select a random subset of up to 5 elements
        $resultantData = array_slice($sponsorCollegesQuery, 1, $limit);

        return [
            'colleges' => $resultantData ?? [],
            'stream_name' => $streamSlug->name
        ];
    }

    public static function getStateByCityId($cityId)
    {
        $stateId = City::find()->select(['state_id'])->where(['id' => $cityId])->one();

        if (empty($stateId)) {
            return null;
        }

        return $stateId['state_id'] ?? null;
    }

    //get the child courses
    public function getOtherCourseList($course)
    {
        if (empty($course->id)) {
            return [];
        }

        $courses = Course::find()
            ->innerJoinWith('courseContent')
            ->where(['course.parent_id' => $course->id])
            ->limit(10)
            ->all();

        return $courses;
    }

    //get the other program page
    public function getCollegeBasedOnSpecialization($program)
    {
        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);
        $aggregate[] = ['$addFields' => [
            'program_position_null' => [
                '$cond' => [
                    'if' => ['$eq' => ['$program_position', null]],
                    'then' => 1,
                    'else' => 0
                ]
            ],
            'course_position_null' => [
                '$cond' => [
                    'if' => ['$eq' => ['$course_position', null]],
                    'then' => 1,
                    'else' => 0
                ]
            ]
        ]];
        $sort = [
            'program_position_null' => 1,
            'course_position_null' => 1,
            'program_position' => 1,
            'course_position' => 1
        ];

        $aggregate[] = [
            '$match' => [
                'college_id' => $program->college_id,
                'course_id' => $program->course_id,
                'type' => 'full_time',
                'status' => CollegeCourse::STATUS_ACTIVE,
                '$and' => [
                    [
                        'program' => [
                            '$not' =>
                            [
                                '$regex' => '[+{}]|tie up|Hons|Integrated|Diploma|Distance|Dual Degree|Lateral|certificate|Fellowship'
                            ]
                        ],
                        'program_slug' => ['$ne' => $program->program_slug]
                    ]
                ]
            ]
        ];

        $aggregate[] = [
            '$sort' => $sort
        ];

        $programList = $collection->aggregate($aggregate);

        $items = [];
        foreach ($programList as $list) {
            if (empty($list['specialization_id'])) {
                continue;
            }
            $items[$list['specialization_slug']] = [
                'slug' => $list['program_slug'],
                'name' => $list['course_short_name'] . ' ' . $list['specialization_name'],
                'program' => $list['program'] ?? '',
                'duration' => $list['duration'],
                'fee' => $list['fees'],
                'page_index' => $list['pageIndex'] ?? '',
                'specialization' => isset($list['specialization_name']) ? $list['specialization_name'] : '',
            ];
        }

        return $items ?? [];
    }

    /** Get city list based on specialization*/
    public function getSpecializationBasedCityList($program, $city)
    {
        if (empty($program->specialization_slug)) {
            return null;
        }
        $slug = $program->course_slug . '-' . $program->specialization_slug;
        $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
        $courses = $collection->aggregate(
            [
                [
                    '$match' =>
                    [
                        'course.specialization' => ['$in' => [$slug]],
                        'city_slug' => ['$ne' => $city],
                        'is_sponsored' => College::SPONSORED_NO,
                        'status' => College::STATUS_ACTIVE
                    ]
                ],
                ['$group' => ['_id' => ['slug' => '$city_slug', 'name' => '$city_name'], 'count' => ['$sum' => 1]]],
                ['$sort' => ['count' => -1]],
                ['$project' => ['cityName' => '$_id.name', 'citySlug' => '$_id.slug', 'count' => 1]],
                ['$limit' => 10]
            ]
        );
        return $courses ?? [];
    }

    /** Get state list based on specialization*/
    public function getSpecializationBasedStateList($program, $state)
    {
        if (empty($program->specialization_slug)) {
            return null;
        }
        $slug = $program->course_slug . '-' . $program->specialization_slug;
        $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
        $courses = $collection->aggregate(
            [
                [
                    '$match' =>
                    [
                        'course.specialization' => ['$in' => [$slug]],
                        'state_slug' => ['$ne' => $state],
                        'is_sponsored' => College::SPONSORED_NO,
                        'status' => College::STATUS_ACTIVE
                    ]
                ],
                ['$group' => ['_id' => ['slug' => '$state_slug', 'name' => '$state_name'], 'count' => ['$sum' => 1]]],
                ['$sort' => ['count' => -1]],
                ['$project' => ['stateName' => '$_id.name', 'stateSlug' => '$_id.slug', 'count' => 1]],
                ['$limit' => 10]
            ]
        );

        return $courses ?? [];
    }

    /** Get Course list by exclude course*/
    public function getExcludedCourselist($course, $college)
    {

        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);
        $courses = $collection->aggregate([
            [
                '$match' => [
                    'college_id' => $college->id,
                    'course_slug' =>
                    ['$not' => ['$in' => [$course->slug, 'other']]]
                ]
            ],
            [
                '$group' => [
                    '_id' => ['course_slug' => '$course_slug', 'course_position' => '$course_position', 'course_name' => '$course_short_name',],
                    'courseAvgFees' => ['$push' => '$courseAvgFees'],
                    'courseDuration' => ['$push' => '$duration']
                ]
            ],
            [
                '$group' => [
                    '_id' => ['course_slug' => '$_id.course_slug', 'course_name' => '$_id.course_name'],
                    'course_position' => ['$push' => '$_id.course_position'],
                    'courseAvgFess' => ['$push' => ['$first' => '$courseAvgFees']],
                    'courseDuration' => ['$push' => ['$first' => '$courseDuration']]
                ]
            ],
            [
                '$project' => [
                    '_id' => 0,
                    'course_slug' => '$_id.course_slug',
                    'course_name' => '$_id.course_name',
                    'courseFess' => ['$first' => '$courseAvgFess'],
                    'courseDuration' => ['$first' => '$courseDuration'],
                    'coursePosition' => ['$first' => '$course_position']
                ]
            ],
            ['$sort' => ['coursePosition' => 1]]
        ]);

        return $courses ?? [];
    }

    /** Get college list based on college*/
    public function getSpecializationBasedCollegeList($program)
    {
        $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
        $collegeList = $collection->aggregate([
            [
                '$match' => [
                    'course.specialization' => $program->specialization_slug,
                    'college_id' => ['$ne' => $program->college_id],
                    'is_popular' => 1,
                    'status' => College::STATUS_ACTIVE
                ]
            ],
            [
                '$group' => [
                    '_id' => ['display_name' => '$display_name', 'slug' => '$slug'],
                    'colleges' => ['$addToSet' => '$college_id']
                ]
            ],
            [
                '$project' => [
                    '_id' => 0,
                    'slug' => '$_id.slug',
                    'display_name' => '$_id.display_name',
                    'collegeCount' => ['$size' => '$colleges']
                ]
            ],
            ['$sort' => ['collegeCount' => -1]],
            ['$limit' => 10]
        ]);

        return $collegeList ?? [];
    }

    /** Get the company list based college id and course id */
    public function getCompanyList($collegeId, $courseId = null)
    {
        $query = new Query();
        $query->select(['c.name'])
            ->distinct()
            ->from('company as c')
            ->innerJoin('college_course_company as cc', 'cc.company_id = c.id')
            ->where(['cc.college_id' => $collegeId]);

        if (!empty($courseId)) {
            $query->andWhere(['cc.course_id' => $courseId]);
        }
        $result = $query->all();
        // dd($courseId);

        return $result ?? [];
    }

    /** Get the Mysql recorde program */
    public function getMysqlProgram($id)
    {
        return CollegeProgram::find()->where(['id' => $id])->one();
    }

    //update course Duration in document
    public static function updateCourseDocumentDuration(Course $course)
    {
        if (empty($course)) {
            return false;
        }
        $courseHighlights = CourseService::getCourseFeature($course->id);
        $duration = (!empty($courseHighlights['duration'][0])) ? $courseHighlights['duration'][0] : '';

        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);

        $collection->update(['course_slug' => $course->slug], ['course_duration' => $duration]);

        return true;
    }

    /** Get the status of College page */
    public static function getStatus($collegeId)
    {
        if (empty($collegeId)) {
            return [];
        }
        $collegeContentStatus = CollegeContent::find()
            ->select(['status', 'sub_page'])
            ->where(['sub_page' => 'courses-fees'])
            ->orWhere(['sub_page' => 'reviews'])
            ->andWhere(['entity_id' => $collegeId])
            ->all();

        foreach ($collegeContentStatus as $value) {
            $data[$value['sub_page']] = $value['status'];
        }

        return $data ?? [];
    }

    /***** Get College Education Body*******/

    public function getCollegeEducationBody($collegeId)
    {
        if (empty($collegeId)) {
            return [];
        }
        $collegeEducationId = College::find()->select(['type'])
                              ->where(['id'=>$collegeId])
                              ->one();
        $educationBodyType = ArrayHelper::getValue(DataHelper::getConstantList('TYPE', College::class), $collegeEducationId->type);
        return $educationBodyType ?? '';
    }
    

    /**Get the get rank based on course and collegeid */
    public function getCoursePressRanking($course, $collegeId)
    {
        if (empty($course->stream->slug)) {
            return [];
        }
        $rankings = CollegeRankings::find()->where(['college_id' => $collegeId])
            ->andWhere(['status' => CollegeRankings::STATUS_ACTIVE])
            ->andWhere(['criteria_id' => [$course->stream->id, $course->id]])
            ->andWhere(['publisher_id' => CollegeRankings::PUBLISHER_PI])
            ->orderBy(['year' => SORT_DESC])
            ->one();

        return empty($rankings) ? [] : $rankings;
    }

    //get the eligibility based on main course
    public function getCourseEligibility($courseId)
    {
        $courses = CourseEligibility::find()->select(['eligibility_title', 'eligibility_description'])->where(['course_id' => $courseId])->all();

        return $courses;
    }

    /**
     * Get the available exam details for course
     * @param int |Id Program Id
     * @return object
     */
    public function getProgramExamNew($id = '', $college_id = '', $course_id = '')
    {
        if (!empty($college_id) && !empty($course_id)) {
            $exams = CollegeProgramExam::find()
                ->leftJoin(CollegeProgram::tableName() . ' as cp', 'cp.id = college_program_exam.college_program_id')
                ->where(['cp.college_id' => $college_id])
                ->andWhere(['cp.course_id' => $course_id])
                ->andWhere(['cp.status' => CollegeProgram::STATUS_ACTIVE])
                ->groupBy('college_program_exam.exam_id')
                ->all();
        } else {
            $exams = CollegeProgramExam::find()->where(['college_program_id' => $id])->all();
        }

        if (!$exams) {
            return [];
        }

        $item = [];
        foreach ($exams as $exam) {
            $data = (new ServicesExamService())->getDetail($exam['exam_id']);
            $examDates = $this->getDate($exam['exam_id']);
            if (empty($data)) {
                continue;
            }

            $item[$data->slug] = [
                'name' => $data->name,
                'display_name' => $data->display_name,
                'slug' => $data->slug,
                'image' => $data->cover_image,
                'exam_type' => $data->exam_type_id,
                'exam_id' => $exam['exam_id']
            ];
            if (!empty($examDates)) {
                $dateArr = [];
                foreach ($examDates as $date) {
                    $dateArr += [
                        str_replace('-', '_', $date->slug) => $date->start . '|' . $date->end
                    ];
                }
                $item[$data->slug]['date'] = $dateArr;
                unset($dateArr);
            }
        }
        //sort based on exam_type
        $keys = array_column($item, 'exam_type');
        if (!empty($keys)) {
            array_multisort($keys, SORT_ASC, $item);
        }
        return $item;
    }

    //Not in Use
    /** get the exam based on course and college */
    public function getCollegeCourseExam($courseId, $collegeId)
    {
        $exams = CourseExamCollege::find()->select(['exam_id'])->where(['course_id' => $courseId])
            ->andWhere(['college_id' => $collegeId])
            ->asArray()->all();

        if (!$exams) {
            return [];
        }

        $item = [];
        foreach ($exams as $exam) {
            $data = (new ServicesExamService())->getDetail($exam['exam_id']);
            $examDates = $this->getDate($exam['exam_id']);
            if (empty($data)) {
                continue;
            }

            $item[$data->display_name] = [
                'name' => $data->name,
                'display_name' => $data->display_name,
                'slug' => $data->slug,
                'image' => $data->cover_image,
                'exam_type' => $data->exam_type_id,
            ];
            if (!empty($examDates)) {
                $dateArr = [];
                foreach ($examDates as $date) {
                    $dateArr += [
                        str_replace('-', '_', $date->slug) => $date->start . '|' . $date->end
                    ];
                }
                $item[$data->display_name]['date'] = $dateArr;
                unset($dateArr);
            }
        }
        //sort based on exam_type
        $keys = array_column($item, 'exam_type');
        if (!empty($keys)) {
            array_multisort($keys, SORT_ASC, $item);
        }

        return $item;
    }

    //update course Name in document
    public static function updateCourseNameDocument(Course $course)
    {
        if (empty($course)) {
            return false;
        }

        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);

        $collection->update(['course_slug' => $course->slug], ['course_short_name' => $course->short_name, 'course' => $course->name]);

        return true;
    }

    //update course status in document
    public static function updateCourseStatusDocument(Course $course)
    {
        if (empty($course)) {
            return false;
        }

        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);

        $collection->update(['course_slug' => $course->slug], ['status' => $course->status]);

        return true;
    }

    public function getSubPageDropdown($college)
    {
        $dropDownArr = [];
        $collegeContentPage = CollegeContent::find()->select(['id', 'sub_page'])
            ->with('author')
            ->andWhere(['entity_id' => $college->id, 'parent_id' => null, 'status' => 1])->all();

        foreach ($collegeContentPage as $key => $value) {
            $collegeContentPage = CollegeContent::find()->select(['id', 'sub_page'])
                ->andWhere(['parent_id' => $value->id, 'status' => 1])->asArray()->all();
            if (!empty($collegeContentPage)) {
                foreach ($collegeContentPage as $dropDownContent) {
                    $dropDownArr[$value->sub_page][] = $dropDownContent;
                }
            }
        }
        if (!empty($dropDownArr['syllabus'])) {
            foreach ($dropDownArr['syllabus'] as $key => $value) {
                $courseData = Course::find()->select(['id', 'short_name', 'slug'])->where(['slug' => $value['sub_page']])->one();
                $dropDownArr['syllabus'][$key]['slug'] = $courseData->slug;
                $dropDownArr['syllabus'][$key]['sub_page'] = $courseData->short_name;
            }
        }
        if (!empty($dropDownArr['cut-off'])) {
            foreach ($dropDownArr['cut-off'] as $key => $value) {
                $courseData = Exam::find()->select(['id', 'display_name', 'slug'])->where(['slug' => $value['sub_page']])->one();
                $dropDownArr['cut-off'][$key]['slug'] = $courseData->slug;
                $dropDownArr['cut-off'][$key]['sub_page'] = $courseData->display_name;
            }
        }
        // Sort data
        if (!empty($dropDownArr['cut-off'])) {
            $subPageSort = array_column($dropDownArr['cut-off'], 'sub_page');
            array_multisort($subPageSort, SORT_ASC, $dropDownArr['cut-off']);
        }
        if (!empty($dropDownArr['syllabus'])) {
            $subPageSort = array_column($dropDownArr['syllabus'], 'sub_page');
            array_multisort($subPageSort, SORT_ASC, $dropDownArr['syllabus']);
        }
        if (!empty($dropDownArr['admission'])) {
            $subPageSort = array_column($dropDownArr['admission'], 'sub_page');
            array_multisort($subPageSort, SORT_ASC, $dropDownArr['admission']);
        }

        return $dropDownArr;
    }

    /** update the College Program Collection */
    public static function updateProgramDocument(CollegeProgram $collegeProgram)
    {
        if (empty($collegeProgram->program_id)) {
            return false;
        }

        $program = self::getProgramDetail($collegeProgram->program_id);

        if (empty($program)) {
            return false;
        }

        $programMapping = self::getProgramMapping($collegeProgram->program_id);
        $courseIndex = self::getCollegeCourseIndex($collegeProgram);

        $collection = ProgramDocuments::find()
            ->where(['college_id' => (int) $collegeProgram->college_id])
            ->andWhere(['college_program_id' => (int) $collegeProgram->id])
            ->andWhere(['program_id' => (int) $collegeProgram->program_id])
            ->andWhere(['program_slug' => $program->slug])
            ->one();

        if (!$collection) {
            $collection = new ProgramDocuments();
        }

        if (empty($collegeProgram->course)) {
            return false;
        }

        $collection->college_program_id = (int) $collegeProgram->id;
        $collection->college_id = (int) $collegeProgram->college_id;
        $collection->program_id = $program->id;
        $collection->program = $program->name;
        $collection->program_slug = $program->slug;
        $collection->program_position = $program->position;
        $collection->fees = self::getProgramFee($collegeProgram->id);
        $collection->duration = $collegeProgram->duration;
        $collection->duration_type = !empty($collegeProgram->duration_type) ? $collegeProgram->duration_type : 1;
        $collection->type = $program->type;
        $collection->mode = $program->mode;
        $collection->total_seat = $collegeProgram->seat;
        $collection->application_link = $collegeProgram->application_link;
        $collection->salary = $collegeProgram->salary;
        $collection->stream_id = (int) $collegeProgram->course->stream_id ?? '';
        $collection->stream_slug = $collegeProgram->course->stream->slug ?? '';
        $collection->stream_name = $collegeProgram->course->stream->name ?? '';
        $collection->specialization_id = !empty($programMapping->specialization_id) ? (int) $programMapping->specialization_id : '';

        if (!empty($programMapping->specialization)) {
            $collection->specialization_slug = $programMapping->specialization->slug ?? '';
            $collection->specialization_name = $programMapping->specialization->display_name ?? $programMapping->specialization->name;
        }

        $collection->course_id = $collegeProgram->course->id ?? '';
        $collection->course = $collegeProgram->course->name ?? '';
        $collection->course_short_name = $collegeProgram->course->short_name ?? '';
        $collection->course_slug = $collegeProgram->course->slug ?? '';
        $courseHighlights = CourseService::getCourseFeature($collegeProgram->course->id);

        $collection->course_duration = (!empty($courseHighlights['duration'][0])) ? $courseHighlights['duration'][0] : '';
        $collection->degree = $collegeProgram->course->degree ?? '';
        $collection->isCiPage = CollegeService::getCiPageInfo($collegeProgram->college_id, $collegeProgram->course->id);

        //brochure
        $broucher = CollegeHelper::getCourseBrochure($collegeProgram->course->id, $collegeProgram->college_id);
        $collection->course_brochure = !empty($broucher) ? $broucher->pdf : '';

        $collegeCourseContent = CollegeService::getCourseContent($collegeProgram->course->id, $collegeProgram->college_id);
        $collection->course_eligibility = !empty($collegeCourseContent) ? $collegeCourseContent->eligibility : '';
        $collection->course_position = $collegeProgram->course->position > 0 ? $collegeProgram->course->position : '';
        $collection->courseAvgFees = CollegeService::getAvgFees($collegeProgram->course->id, $collegeProgram->college_id);

        $exam = ArrayHelper::map($collegeProgram->exams, 'id', 'display_name');
        $collection->exams = implode(',', $exam);
        if ($collegeProgram->course->status == CollegeProgram::STATUS_ACTIVE) {
            $collection->status = (int)$collegeProgram->status;
        } else {
            $collection->status = (int) $collegeProgram->course->status;
        }

        $collection->pageIndex = (int) $collegeProgram->page_index ?? '';
        $collection->coursePageIndex = !empty($courseIndex) ? $courseIndex->page_index : 0;
        if ($collection->save()) {
            echo "{$collection->program_slug} \t {$collection->college_program_id} \n";
        } else {
            print_r($collection->getErrors());
        }
    }

    /** get the program Tuition Fees */
    public function getProgramFee($programId)
    {
        $query = new Query();
        $query->select('fees')
            ->from(CollegeProgramFees::tableName())
            ->where(['college_program_id' => $programId])
            ->andWhere(['type' => 'tuition_fees'])
            ->andWhere(['status' => CollegeProgram::STATUS_ACTIVE]);

        $sum = $query->sum('fees');

        return $sum ?? 0;
    }

    /** update the program status to college_program Collection */
    public static function updateProgramStatus(Program $program)
    {

        CollegeProgram::updateAll(['status' => $program->status], 'program_id =' . $program->id);

        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);

        $arr = ['status' => $program->status];
        $collection->update(
            ['program_id' => $program->id],
            $arr
        );
    }

    /** get the program detail */
    public function getProgramDetail($programId)
    {
        return Program::find()
            ->select(['id', 'name', 'slug', 'mode', 'type', 'position'])
            ->where(['id' => $programId])
            ->andWhere(['status' => College::STATUS_ACTIVE])
            ->one();
    }

    /** get the program mapping detail */
    public function getProgramMapping($programId)
    {
        return ProgramCourseMapping::find()
            ->select(['program_id', 'course_id', 'specialization_id'])
            ->where(['program_id' => $programId])
            ->one();
    }

    public function getCollegeCourseIndex(CollegeProgram $collegeProgram)
    {
        return CollegeCourseContent::find()
            ->where(['college_id' => $collegeProgram->college_id])
            ->andWhere(['course_id' => $collegeProgram->course_id])
            ->one();
    }

    public static function checkFilterPageStatus($colleges = [], $citySlug = '', $cityId = '', $courseId = '', $examSlug = '')
    {
        $sponsored = DocumentsCollege::find()->select(['is_sponsored', 'exams', 'status']);

        if (!empty($examSlug)) {
            $sponsored->where(['exams' => $examSlug])->andWhere(['is_sponsored' => College::SPONSORED_NO])->andWhere(['status' => College::STATUS_ACTIVE]);
        }
        if (!empty($cityId)) {
            $sponsored->where(['city_id' => (int) $cityId]);
        } elseif (!empty($citySlug)) {
            $sponsored->where(['city_slug' => $citySlug]);
        }
        if (!empty($courseId)) {
            $sponsored->andWhere(['course.course_id' => $courseId]);
        } elseif (!empty($colleges) && !empty($colleges[0])) {
            $sponsored->andWhere(['course.stream_id' => (int) $colleges[0]['streamId']]);
        }

        $checkCollegeFilterSponsorStatus = $sponsored->all();

        if (!empty($examSlug)) {
            $examStatus = empty($checkCollegeFilterSponsorStatus) ? Exam::STATUS_INACTIVE : Exam::STATUS_ACTIVE;

            return $examStatus;
        } else {
            $sponsorStatus = empty($checkCollegeFilterSponsorStatus) ? '' : in_array(College::SPONSORED_NO, array_column($checkCollegeFilterSponsorStatus, 'is_sponsored'));

            $status = empty($sponsorStatus) ? College::SPONSORED_YES : College::SPONSORED_NO;

            return $status;
        }
    }

    /** Get Cut off Category */
    public static function getCategory()
    {
        $category = CutoffCategory::find()
            ->select(['id', 'name'])
            ->asArray()
            ->orderBy(['id' => SORT_ASC])->all();

        return ArrayHelper::map($category, 'name', 'id');
    }

    /** update the program status to college_program Collection */
    public static function updateProgramName(Program $program)
    {

        $collection = Yii::$app->mongodb->getCollection(ProgramDocuments::COLLECTION_NAME);

        $arr = ['program' => $program->name];
        $collection->update(
            ['program_slug' => $program->slug],
            $arr
        );
    }

    public function getStreamBasedPressRanking($course, $collegeId)
    {
        if (empty($course->stream->slug)) {
            return [];
        }
        $rankings = CollegeRankings::find()
            ->where(['college_id' => $collegeId])
            ->andWhere(['status' => CollegeRankings::STATUS_ACTIVE])
            ->andWhere(['criteria_id' => $course->stream->id])
            ->andWhere(['criteria' => 'stream'])
            ->orderBy(['year' => SORT_DESC])
            ->all();

        return empty($rankings) ? [] : $rankings;
    }

    public function getCollegeBasedStudentDiversityData($collegeId)
    {
        $results = CollegeProgramStudentEnrollment::find()
            ->where(['college_id' => $collegeId])->andWhere(['status' => 1])->all();
    }


    public function getCollegeStudentStaffData($collegeId)
    {
        $gender = array_flip(CollegeHelper::$cutoffGender);
        $results = CollegeStudentOverallEnrollment::find()
            ->where(['college_id' => $collegeId])
            ->andWhere(['status' => 1])
            ->orderBy(['gender' => SORT_DESC])
            ->all();
        if (empty($results)) {
            return [];
        }

        $items = [];
        foreach ($results as $res) {
            $items[CollegeHelper::$enrollementType[$res->type]][$gender[$res->gender]] = [
                'count' => $res->count
            ];
        }

        if (!empty($items['Student Enrolment'])) {
            $items['Student Enrolment']['Total'] = array_sum(array_column($items['Student Enrolment'], 'count'));
        }

        if (!empty($items['Teaching Staff'])) {
            $items['Teaching Staff']['Total'] = array_sum(array_column($items['Teaching Staff'], 'count'));
        }

        return $items;
    }

    public function getCollegeHostelData($collegeId)
    {
        $results = CollegeHostel::find()
            ->where(['college_id' => $collegeId])
            ->all();

        if (empty($results)) {
            return [];
        }
        $items = [];
        foreach ($results as $res) {
            $items[$res->type_of_hostel] = [
                'type' => CollegeHelper::$hostelType[$res->type_of_hostel],
                'intake_capcity' => $res->intake_capacity,
                'student_residing' => $res->student_residing,
                'count' => $res->count ??  'NA'
            ];
        }

        return $items;
    }

    public function getCollegeStudentDiversityData($collegeId, $courseId = null, $programId = null)
    {
        // dd($collegeId);
        $enrollment = array_flip(CollegeHelper::$casteCategory);
        $genders = array_flip(CollegeHelper::$cutoffGender);
        $query = CollegeProgramStudentEnrollment::find()
            ->where(['college_id' => $collegeId])
            ->andWhere(['status' => 1]);

        if (!empty($courseId)) {
            $query->andWhere(['course_id' => $courseId]);
        }

        if (!empty($programId)) {
            $query->andWhere(['program_id' => $programId]);
        }

        $results = $query->all();

        if (empty($results)) {
            return [];
        }

        $items = [];
        foreach ($results as $res) {
            $items[CollegeHelper::$casteCategory[$res->category]][$genders[$res->gender]][] = [
                'count' => $res->count
            ];
        }

        $data = [];

        foreach ($items as $key => $item) {
            foreach ($item as $ke => $it) {
                $data[$ke][$key] = array_sum(array_column($it, 'count'));
            }
            if (!empty($data['All'][$key])) {
                if (!empty($data['Female'][$key])) {
                    $data['Male'][$key] = abs($data['All'][$key] - $data['Female'][$key]);
                } else {
                    $data['Male'][$key] = $data['All'][$key];
                }
            } else {
                $data['Male'][$key] = '';
            }

            // unset($data["All"]);
        }

        krsort($data);
        $data['category'] = array_keys($items);

        return $data;
    }

    public function getScholarshipCategoryInfo($collegeId)
    {
        $gender = array_flip(CollegeHelper::$cutoffGender);
        $caste = (CollegeHelper::$casteCategory);

        $data = CollegeStudentScholarship::find()
            ->where(['college_id' => $collegeId])
            ->orderBy(['category' => SORT_DESC])
            ->all();
        $items = [];
        foreach ($data as $d) {
            $items['header'][$caste[$d['category']]] = $caste[$d['category']];
            $items['data'][$gender[$d['gender']]][$caste[$d['category']]] = $d['count'];
        }

        return $items;
    }

    public function getRakingType($arr = [])
    {
        if (empty($arr)) {
            return [];
        }
        $data = ArrayHelper::map($arr, 'criteria_id', 'criteria');

        foreach ($data as $k => $v) {
            $items[] = CollegeRanksDataHelper::getCriteria($v, $k);
        }

        return $items[0];

        // return implode(', ', $items);
    }

    public function getSpecilizationOfProgram($programId, $courseId)
    {
        return ProgramCourseMapping::find()->select('specialization_id')
            ->where(['program_id' => $programId])
            ->andWhere(['course_id' => $courseId])
            ->one();
    }

    public function getCollegefromCollection($collegeSlug)
    {
        return DocumentsCollege::find()
            ->where(['slug' => $collegeSlug])
            ->andWhere(['status' => College::STATUS_ACTIVE])
            ->one();
    }

    public function dynamicMerge($arrays)
    {
        return call_user_func_array('array_merge_recursive', $arrays);
    }

    public function getRakingBody($arr = [])
    {
        if (empty($arr)) {
            return [];
        }

        foreach ($arr as $k => $v) {
            $items[] = CollegeRankingPublisher::find()->select(['name'])->where(['id' => $v])->one();
        }

        return $items[0]['name'];
        //return implode(',', array_column($items, 'name'));
    }

    public function getContentTemplateFormate(
        College $college,
        $templateData = [],
        Course $course = null,
        CollegeProgram $collegeProgram = null
    ) {
        if (empty($templateData['templateContent']->content)) {
            return [];
        }

        $collegeCollection = $this->getCollegefromCollection($college->slug);

        // Create the Twig environment with an ArrayLoader
        $loader = new ArrayLoader();

        // Create the Twig environment
        $twig = new Environment($loader);
        $template = $twig->createTemplate($templateData['templateContent']->content);

        $data = [];
        $data['city'] = !empty($collegeCollection->city_name) ? $collegeCollection->city_name : '';
        $data['boys_hostel_count'] = !empty($templateData['hostelData'][1]['count']) ? $templateData['hostelData'][1]['count'] : '';
        $data['girls_hostel_count'] = !empty($templateData['hostelData'][2]['count']) ? $templateData['hostelData'][2]['count'] : '';
        $data['placement_companies'] = !empty($templateData['companyList']) ? implode(', ', array_column($templateData['companyList'], 'name')) : '';
        $data['college_name'] = $collegeCollection->name ?? '';
        $data['college_short_name'] = $collegeCollection->display_name ?? ($collegeCollection->name ?? '');
        $data['count_of_colleges_affiliated'] = $college->college_count;
        $data['state'] = !empty($collegeCollection->state_name) ? $collegeCollection->state_name : '';
        $data['student_teacher_ratio'] = !empty($templateData['highlights']['Faculty Student Ratio']['value']) ? $templateData['highlights']['Faculty Student Ratio']['value'] : '';
        $data['year_of_establishment'] = !empty($templateData['highlights']['Year of Establishment']['value']) ? $templateData['highlights']['Year of Establishment']['value'] : '';
        $data['area_in_acre'] = !empty($templateData['highlights']['Total Area (In Acre)']['value']) ? $templateData['highlights']['Total Area (In Acre)']['value'] : '';
        $data['accredited'] = !empty($templateData['accredited']) ? $templateData['accredited'] : '';
        $data['recognised'] = !empty($templateData['recognised']) ? $templateData['recognised'] : '';
        $data['approval'] = !empty($templateData['approval']) ? $templateData['approval'] : '';

        if (!empty($templateData['exam'])) {
            $data['exam'] = implode(',', array_column($templateData['exam'], 'display_name'));
        }

        if (!empty($templateData['parent_college'])) {
            $data['affiliat_university'] = $templateData['parent_college']['display_name'] ?? $templateData['parent_college']['name'];
        }
        $data['specialization_list'] = array_unique($this->dynamicMerge(array_column($collegeCollection->course, 'specialization')));

        if (!empty($collegeCollection->course)) {
            $degrees = array_map(function ($course) {
                if (is_array($course['degree'])) {
                    return $course['degree'][0];
                } else {
                    return $course['degree'];
                }
            }, $collegeCollection->course);
            foreach (array_unique($degrees) as $degree) {
                $items[] = !empty(CourseHelper::$degree[$degree]) ? CourseHelper::$degree[$degree] : '';
            }
            $data['college_total_tution_fees'] = $collegeCollection->avgFees;
        }

        $data['course_level_list'] = !empty($items) ? implode(', ', $items) : '';
        $data['undergraduate_course_count'] = '';
        $data['postgraduate_course_count'] = '';

        if (!empty($collegeProgram->program)) {
            $data['program_name'] = $collegeProgram->program->name;
            $data['salary'] = $collegeProgram->salary;
            $data['program_total_tuition_fees'] = $templateData['programFees']['totalFees'];
            // $data['{program_duration}'] = CollegeHelper::yearsFormatCourses($collegeProgram->duration, $college->duration_type ?? 0);
            $courseHighlights = CourseService::getCourseFeature($collegeProgram->course->id);

            $data['program_duration'] = (!empty($courseHighlights['duration'][0])) ? CollegeHelper::yearsFormatCourses($courseHighlights['duration'][0], 'Months') : '';
            $data['program_specialization'] = $this->getSpecilizationOfProgram($collegeProgram->program_id, $collegeProgram->course_id);
        }

        if (!empty($collegeProgram->course)) {
            $data['program_stream'] = $collegeProgram->course->stream->name;
            $data['program_level'] = DataHelper::$collegeCourseDegreeList[$collegeProgram->course->degree];
        }

        if (!empty($templateData['rank_type'])) {
            $data['rank'] = array_unique(array_column($templateData['rank_type'], 'rank'))[0];
            $data['rank_body'] = $this->getRakingBody(array_unique(array_column($templateData['rank_type'], 'publisher_id')));
            $data['rank_type'] = $this->getRakingType($templateData['rank_type']);
            $data['rank_year'] = array_unique(array_column($templateData['rank_type'], 'year'))[0];
        }

        if (!empty($templateData['enrollment_count']['Student Enrolment'])) {
            $data['enrollment_count'] = $templateData['enrollment_count']['Student Enrolment']['Total'];
        }

        if (!empty($templateData['facilities'])) {
            $data['college_facilities'] = str_replace(' ,', '', ucwords(str_replace(',', ', ', $templateData['facilities'])));
        }

        if (!empty($college->college_type)) {
            $data['college_type'] = CollegeHelper::$collegeType[$college->college_type];
        }

        if (!empty($templateData['courses'])) {
            $data['course_count'] = $templateData['course_count'];
            $data['course_list'] = implode(', ', array_column($templateData['courses'], 'short_name'));
            $data['course_stream_list'] = implode(', ', array_unique(array_column($templateData['courses'], 'stream_name')));
        }
        if (!empty($templateData['program_list'])) {
            $data['program_list'] = implode(', ', array_column($templateData['program_list'], 'name'));
            $data['program_count'] = count(array_column($templateData['program_list'], 'name'));
        }

        $output = $template->render($data);

        return $output;
    }

    public function getContentTemplate($templateId, $page)
    {
        return ContentTemplate::find()->select('content')
            ->where(['id' => $templateId])
            ->andWhere(['page' => $page])
            ->one();
    }

    public function getExamContentTemplate($college, $page)
    {
        $examArr = [];
        $examMap = [];
        $examSlugMap = [];

        $exams = $this->getExamList($college->id);

        foreach ($exams as $exam) {
            $exam_id['id'] = $exam->id;
            $exam_id['name'] = $exam->display_name;
            $examMap[$exam->id] = $exam->display_name;
            $examSlugMap[$exam->id] = $exam->slug;
            $examArr[] = $exam_id;
        }
        $exam_ids = array_map(function ($exam) {
            return $exam['id'];
        }, $examArr);

        $result = ContentTemplate::find()->select(['content', 'entity_id', 'created_at', 'updated_at'])
            ->where(['entity_type' => 'exam'])
            ->andWhere(['page' => $page])
            ->andWhere(['IN', 'entity_id', $exam_ids])
            ->andWhere(['status' => ContentTemplate::STATUS_ACTIVE])
            ->orderBy(['updated_at' => SORT_DESC])
            ->all();

        $dataResult = [];
        $data = [];
        foreach ($result as $examContent) {
            $contentData = ContentHelper::getDateUpdateCollegePage($examContent->updated_at);
            if ($contentData) {
                $features = $this->getFeatures($college);
                $recognised = $this->getFeatureStringValues($features, 'Recognitions');
                $accredited = $this->getFeatureStringValues($features, 'Accreditations');
                $approval = $this->getFeatureStringValues($features, 'Approvals');
                $type = $this->getInstitutionType($features);
                $parentCollege =  $this->getParentCollege($college->parent_id);
                $featuresGroup = $this->getFeaturesByGroup($features);
                $coursesList = $this->getCourseHighlights($college, $page, ['pgdm', 'diploma', 'certificate', 'fellowship-programme', 'other']);
                $coursesAvgFeesCount = $this->getCountCourseAvgFees($college, $page, ['pgdm', 'diploma', 'certificate', 'fellowship-programme', 'other']);
                $hostelData = $this->getCollegeHostelData($college->id);
                $examList = $this->getExamList($college->id);
                $collegeStudentStaffData = $this->getCollegeStudentStaffData($college->id);
                $facilities = $this->getFeatureStringValues($features, 'Facilities');
                $rankList = $this->getCollegePressRanking($college->id);
                $companyList = $this->getCompanyList($college->id);

                $data['exam_id'] = $examContent->entity_id;
                $data['name'] = isset($examMap[$examContent->entity_id]) ? $examMap[$examContent->entity_id] : '';
                $data['slug'] = isset($examSlugMap[$examContent->entity_id]) ? $examSlugMap[$examContent->entity_id] : '';
                $data['updated_at'] = $contentData;
                if (!empty($examContent->content)) {
                    $examTemplate['templateContent'] = new ContentTemplate();
                    $examTemplate['exam'][0] = ['display_name' => $data['name']];
                    $examTemplate['accredited'] = $accredited;
                    $examTemplate['recognised'] = $recognised;
                    $examTemplate['approval'] = $approval;
                    $examTemplate['institute_type'] = $type;
                    $examTemplate['parent_college'] = $parentCollege;
                    $examTemplate['courses'] = $coursesList;
                    $examTemplate['course_count'] = count($coursesList);
                    $examTemplate['hostelData'] = $hostelData;
                    $examTemplate['facilities'] = $facilities;
                    $examTemplate['highlights'] = $features;
                    $examTemplate['companyList'] = $companyList;
                    $examTemplate['enrollment_count'] = $collegeStudentStaffData;
                    $examTemplate['rank_type'] = $rankList;
                    $examTemplate['templateContent']->content = $examContent->content ?? '';
                    $examContent = $this->getContentTemplateFormate($college, $examTemplate, null, null);
                    $data['content'] = $examContent;
                }
                $dataResult[] = $data;
            }
        }
        return $dataResult ?? [];
    }

    public function getInfoForCollegeCompare($collegeAttributeArr)
    {
        $count = 1;
        $data = [];
        $featureArr = [];

        if (empty($collegeAttributeArr)) {
            return $data;
        }

        foreach ($collegeAttributeArr as $collegeProgram) {
            $collegeId = $collegeProgram['college_id'];
            $programId = $collegeProgram['program_id'];

            $programSlug = Program::find()->byId($programId)->one();
            $college = $this->getById($collegeId);
            $program = $this->getProgramBySlug($programSlug->slug, $college->id);
            $collegeFeatures = $this->getFeatures($college);

            $data[$count]['college_slug'] = $college->slug;
            $data[$count]['program_slug'] = $programSlug->slug;

            /* ************************* Institute Info ************************** */

            $collegeFeatureGroups = $this->getFeaturesByGroup($collegeFeatures);
            foreach (CollegeHelper::$collegeHighlightsCollegeCompare as $slug => $value) {
                $highlightsFound = false;
                if (!empty($collegeFeatureGroups['Highlights'])) {
                    foreach ($collegeFeatureGroups['Highlights'] as $item) {
                        if ($item['featureSlug'] === $slug) {
                            $name = ($slug == 'campus-area') ? $value : $item['featureName'];
                            $featureArr[$name] = !empty($item['value']) ? $item['value'] : '--';
                            $highlightsFound = true;
                            break;
                        }
                    }

                    if (!$highlightsFound) {
                        $featureArr[$value] = '--';
                    }
                }
            }

            $data[$count]['institute_info'] = $featureArr;
            $featureArr = [];

            /* ***************************** Location **************************** */

            $data[$count]['location'] = $college->address;

            /* ***************************** Placement *************************** */

            if (!empty($program)) {
                $mysqlProgram = $this->getMysqlProgram($program->college_program_id);
                if (!empty($mysqlProgram) && !empty($mysqlProgram->salary)) {
                    $data[$count]['placement'] = '₹ ' . CollegeHelper::feesFormat($mysqlProgram->salary, College::ENTITY_COLLEGE_COMPARE);
                } else {
                    $data[$count]['placement'] = null;
                }
            } else {
                $data[$count]['placement'] = null;
            }

            /* ***************************** Total Fee *************************** */

            if (!empty($program) && !empty($program->college_program_id)) {
                $totalFees = $this->getProgramFeeDetails($program->college_program_id);
                $data[$count]['total_fee'] = !empty($totalFees) && !empty($totalFees['totalFees']) ? '₹ ' . number_format($totalFees['totalFees']) . ' (for ' . CollegeHelper::yearsFormat($program->duration) . ')' : null;
            } else {
                $data[$count]['total_fee'] = null;
            }

            /* ************************ Course Duration & Mode ****************** */

            if (!empty($program)) {
                $data[$count]['course_duration_mode']['duration'] = CollegeHelper::yearsFormat($program->duration);
                $data[$count]['course_duration_mode']['mode'] = CollegeCourse::$type[$program->type] ?? '--';
            } else {
                $data[$count]['course_duration_mode'] = null;
            }

            /* ************************ Approval ****************** */
            $data[$count]['approval'] = $this->getFeatureStringValues($collegeFeatures, 'Approvals');

            /* ************************ Affiliated ****************** */
            $data[$count]['affiliatedCollege'] = $this->getAffiliatedCollegeCompareColleges($college->id, 10);

            /* ************************ College Rank ****************** */
            $ranking = $this->getCollegePressRanking($college->id);

            if (empty($ranking)) {
                $data[$count]['ranking'] = [];
            }
            foreach ($ranking as $key => $rank) {
                $data[$count]['ranking'][] = [
                    'criteria' => CollegeRanksDataHelper::getCriteria($rank->criteria, $rank->criteria_id),
                    'rank' => $rank->rank,
                    'year' => $rank->year,
                    'image' => DataHelper::s3Path(null, 'ranking', 'path') . '/' . $rank->publisher->publisher_image,
                ];
            }

            /* ************************ Studnet Rating And Reviews ****************** */
            $data[$count]['reviews'] = (new ReviewService)->getCollegeReviews($college->id, 1);
            $data[$count]['revCategoryRating'] = (new ReviewService)->getCollegeBasedCategoryRating($college->id);
            $data[$count]['reviewCount'] = (new ReviewService)->getCollegeReviewCount($college->id);

            /* ************************ Facilities ****************** */
            // $data[$count]['facility'] = explode(', ', $this->getFeatureStringValues($collegeFeatures, 'Facilities'));
            $facilities = explode(', ', $this->getFeatureStringValues($collegeFeatures, 'Facilities'));
            $excludeFacilites = ['playground', 'Theatre', 'Conference hall', 'Health Center', 'Indoor stadium', 'Common room', 'Guest house'];
            $filteredArray = [];

            // Exclude values mentioned in $excludeFacilities
            if (!empty($facilities)) {
                $filteredArray = array_diff($facilities, $excludeFacilites);
            }

            $data[$count]['facility'] = $filteredArray;
            $count++;
        }

        return $data;
    }

    public function getSponsoredStatus($collegeId)
    {
        if (empty($collegeId)) {
            return College::SPONSORED_NO;
        }

        $college = College::find()->select(['is_sponsored'])->where(['id' => $collegeId])->one();

        if (empty($college) || empty($college['is_sponsored'])) {
            return College::SPONSORED_NO;
        }

        return $college['is_sponsored'];
    }

    public function getCourseCount($collegeId)
    {
        $query = new Query();
        $query->select(['college_id', 'COUNT(college_id) AS college_count', 'course_id'])
            ->from([CollegeCourseMapping::tableName()])
            ->where(['college_id' => $collegeId])
            ->groupBy(['college_id'])
            ->having(['COUNT(college_id)' => 1]);

        $college = $query->one();

        if (empty($college)) {
            return '';
        }

        return $college['course_id'] ?? '';
    }

    public function getExam($slug)
    {
        return Exam::find()->select('id')->where(['slug' => $slug])->andWhere(['status' => Exam::STATUS_ACTIVE])->one();
    }

    /* Function for check Avg Course Fees not empty*/

    public function getCountCourseAvgFees(College $college, $page = null, $exclude = [])
    {
        $query = ProgramDocuments::find()
            ->where(['college_id' => $college->id])
            ->andWhere(['status' => CollegeCourse::STATUS_ACTIVE])
            ->andWhere(['!=', 'courseAvgFees', 0]);
        $courseCount =  $query->count();
        return $courseCount;
    }

    public function getNewsByCollege($collegeId, $offset = null)
    {
        $query = new Query();
        $query->select(['ns.id', 'ns.display_name', 'ns.name', 'ns.banner_image', 'ns.slug'])
            ->from(['news_subdomain ns'])
            ->innerJoin('college_news_subdomain cns', 'ns.id = cns.news_id')
            ->innerJoin('college c', 'cns.college_id = c.id')
            ->where(['c.id' => $collegeId])
            ->andWhere(['ns.status' => News::STATUS_ACTIVE]);
        $totalCount = $query->count();
        $offset ? $query->offset($offset) : '';
        $query->limit(12)
            ->orderBy(['ns.updated_at' => SORT_DESC]);

        return ['query' => $query, 'totalNewsCount' => $totalCount];
    }

    public function getArticlesByCollege($collegeId, $offset = null)
    {
        $query = new Query();
        $query->select(['a.id', 'a.title', 'a.cover_image', 'a.slug', 'a.h1', 'a.author_id', 'u.name'])
            ->from(['article a'])
            ->innerJoin('article_college ac', 'a.id = ac.article_id')
            ->innerJoin('college c', 'c.id = ac.college_id')
            ->innerJoin('user u', 'u.id = a.author_id')
            ->where(['c.id' => $collegeId])
            ->andWhere(['a.status' => Article::STATUS_ACTIVE]);
        $totalCount = $query->count();
        $offset ? $query->offset($offset) : '';
        $query->limit(12)
            ->orderBy(['a.updated_at' => SORT_DESC]);

        return ['query' => $query, 'totalArticlesCount' => $totalCount];
    }

    public function getAdmissionColleges()
    {
        $query = new Query();
        $result = $query->select(['c.name', 'display_name', 'c.id as college_id', 'c.slug as college_slug', 'rank', 'cover_image', 'logo_image', 'city.name as city_name', 'state.name as state_name', 'c.city_id', 'city.state_id'])
            ->from('college_content as cc')
            ->innerJoin('college as c', 'c.id = cc.entity_id')
            ->innerJoin('city', 'city.id = c.city_id')
            ->innerJoin('state', 'state.id = city.state_id')
            ->where(['sub_page' => 'admission'])
            ->andWhere(['c.status' => College::STATUS_ACTIVE])
            ->andWhere(['cc.status' => CollegeContent::STATUS_ACTIVE])
            ->orderBy(['cc.updated_at' => SORT_DESC])
            ->limit(20);

        $pages = new Pagination([
            'totalCount' => $result->count(),
            'pageSize' => 20
        ]);

        $model = $result->offset($pages->offset)
            ->limit($pages->limit)
            ->all();

        return [
            'results' => $model,
            'pagination' => $pages
        ];
    }


    public static function isArticlesNewsAllowed($collegeId)
    {
        $articles = new Query();
        $articles->select('a.id')
            ->from(['article a'])
            ->innerJoin('article_college ac', 'a.id = ac.article_id')
            ->where(['ac.college_id' => $collegeId])
            ->andWhere(['a.status' => Article::STATUS_ACTIVE]);

        $news = new Query();
        $news->select('id')
            ->from(['news_subdomain ns'])
            ->innerJoin('college_news_subdomain cns', 'ns.id = cns.news_id')
            ->where(['cns.college_id' => $collegeId])
            ->andWhere(['ns.status' => News::STATUS_ACTIVE]);

        if ($articles->count() >= 4 || $news->count() >= 4) {
            return true;
        } else {
            return false;
        }
    }

    public function getCollegeNotificationUpdate($college_id, $page)
    {
        $notification = [];
        $allNotification = CollegeNotificationUpdate::find()
            ->where(['college_id' => $college_id])
            ->andWhere(['sub_page' => $page])
            ->andWhere(['<=', 'date(start_date)', date('Y-m-d')])
            ->andWhere(['status' => CollegeNotificationUpdate::STATUS_ACTIVE])
            ->orderBy(['publish_at' => SORT_DESC])
            ->limit(7)
            ->all();

        foreach ($allNotification as $update) {
            if (!empty($update->end_date)) {
                $endData = strtotime(date('Y-m-d', strtotime($update->end_date)));
                $todayDate = strtotime(date('Y-m-d'));
                if ($endData <  $todayDate) {
                    continue;
                }
            }
            $notification[] = $update;
        }
        return $notification ?? [];
    }

    public function getCollegeRanking($collegeId)
    {

        $rankings = CollegeRankings::find()->where(['college_id' => $collegeId])
            ->andWhere(['status' => CollegeRankings::STATUS_ACTIVE])
            ->orderBy(['year' => SORT_DESC, 'rank' => SORT_ASC])
            ->all();
        $rankData = [];
        $rankDataCriteria = [];
        $rankDataPublisher = [];
        $year = [];
        if (!empty($rankings)) {
            $j = 0;
            foreach ($rankings as $rank) {
                $rankDataCriteria[$rank->publisher->name][CollegeRanksDataHelper::getCriteria($rank->criteria, $rank->criteria_id)][$rank->year]['criteria'] = $rank->criteria;
                $rankDataCriteria[$rank->publisher->name][CollegeRanksDataHelper::getCriteria($rank->criteria, $rank->criteria_id)][$rank->year]['rank'] = $rank->rank;
                $rankDataCriteria[$rank->publisher->name][CollegeRanksDataHelper::getCriteria($rank->criteria, $rank->criteria_id)][$rank->year]['criteria_value'] = CollegeRanksDataHelper::getCriteria($rank->criteria, $rank->criteria_id);
                $rankDataPublisher[$rank->publisher->name][$rank->year][$j]['criteria'] = $rank->criteria;
                $rankDataPublisher[$rank->publisher->name][$rank->year][$j]['rank'] = $rank->rank;
                $rankDataPublisher[$rank->publisher->name][$rank->year][$j]['criteria_value'] = CollegeRanksDataHelper::getCriteria($rank->criteria, $rank->criteria_id);
                $j++;
            }

            $rankData['criteria'] = $rankDataCriteria;
            $rankData['publisher'] = $rankDataPublisher;
        }
        // echo "<pre>"; print_r($rankData); die;
        return empty($rankData) ? [] : $rankData;
    }

    public function getCourseProgramDates($entityId, $entityType)
    {
        $dates = CourseProgramDates::find()
            ->select(['name', 'slug', 'start', 'end'])
            ->where(['entity_id' => $entityId])
            ->andWhere(['entity_type' => $entityType])
            ->andWhere(['status' => CourseProgramDates::STATUS_ACTIVE])
            ->andWhere(['type' => CourseProgramDates::TYPE_OFFICIAL])
            ->all();

        return $dates ?? [];
    }

    public function getCollegeListingMapping($listingPage)
    {
        $filterService = new FilterService();
        $query = (new \yii\db\Query())
            ->select(['cm.listing_page_id', 'clg.group_id', 'listing_page.slug as listingPage', 'cg.slug as groupingPage', 'cm.status'])
            ->from('college_listing_mapping cm')
            ->leftJoin('college_listing_grouping clg', 'cm.id = clg.listing_id')
            ->leftJoin('filter_page_seo cg', 'clg.group_id = cg.id')
            ->leftJoin('filter_page_seo listing_page', 'listing_page_id = listing_page.id')
            ->where(['listing_page.slug' => $listingPage, 'cm.status' => CollegeListingMapping::STATUS_ACTIVE]);

        $results = $query->all();
        $dataResults = [];
        foreach ($results as $key => $value) {
            $filterArray = preg_split('/[\/]/', $value['groupingPage']);
            $seoInfo = $filterService->getSeoInfo($filterArray[0], $filterArray[1], null, false, true);
            $data['listingPage'] = $value['listingPage'];
            $data['groupingPage'] = $value['groupingPage'];
            $data['h1'] = $seoInfo['h1'];
            $data['status'] = $value['status'];
            $dataResults[] = $data;
        }
        return $dataResults ?? [];
    }
}
