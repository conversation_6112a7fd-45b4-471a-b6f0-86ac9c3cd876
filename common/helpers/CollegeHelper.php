<?php

namespace common\helpers;

use Carbon\Carbon;
use common\models\Brochure;
use common\models\Category;
use DOMDocument;
use DOMXPath;
use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Inflector;
use frontend\helpers\Url;
use common\helpers\DataHelper;
use common\models\College;

class CollegeHelper
{
    const YEAR = '2025';
    const PREVIOUS_YEAR = '2024';

    public static $hashTagLists = [
        '#admission_year',
        '#placement_year',
        '#result_year',
        '#cutoff_year',
    ];

    public static $category = [
        [
            'id' => 1,
            'name' => 'Indian Colleges',
            'slug' => 'indian-colleges',
        ]
    ];

    public static $subPages = [
        'info' => 'Info',
        'courses-fees' => 'Courses & Fees',
        'admission' => 'Admission',
        'cut-off' => 'Cut Off',
        'reviews' => 'Reviews',
        'placements' => 'Placements',
        'result' => 'Result',
        'facilities' => 'Infrastructure',
        'images-videos' => 'Gallery',
        'scholarships' => 'Scholarship',
        'qna' => 'Forum',
        'compare-college' => 'Compare Colleges',
        'ranking' => 'Ranking',
        'alumni' => 'Alumni',
        'hostel' => 'Hostel',
        'application-form' => 'Application Form',
        'syllabus' => 'Syllabus',
        'verdict' => 'Verdict',
        'news' => 'Articles & News',
        'timings'=>'Timings',
        'average-package'=>'Average Package',
        'address'=>'Address',
        'previous-year-papers'=>'Previous Year Papers',
        'admission-form'=>'Admission Form',
        'marksheet'=>'Marksheet',
        'nacc-grade'=>'Nacc Grade',
        'dress-code'=>'Dress Code',
        'refund'=>'Refund',
        'cgpa-to-percentage'=>'CGPA to Percentage',
        'fees' => 'Fees',
        'eligibility' => 'Eligibility'

    ];

    public static $subPageTitles = [
        'info' => '',
        'courses-fees' => 'Courses & Fees',
        'admission' => 'Admission',
        'cut-off' => 'Cutoff',
        'reviews' => 'Reviews',
        'placements' => 'Placements',
        'result' => 'Result',
        'facilities' => 'Facilities',
        'images-videos' => 'Photos & Videos',
        'scholarships' => 'Scholarships',
        'qna' => 'Forum',
        'compare-college' => 'Compare Colleges',
        'ranking' => 'Ranking',
        'alumni' => 'Alumni',
        'hostel' => 'Hostel',
        'application-form' => 'Application Form',
        'syllabus' => 'Syllabus',
        'verdict' => 'Verdict',
        'news' => 'Articles & News'
    ];

    public static $_collegeSlugCourseFeesTablePage = [
        'jai-narain-vyas-university-jnvu-jodhpur',
        'monad-university-mu-hapur',
        'gossner-college-ranchi',
        'st-anthonys-college-shillong',
        'psg-college-of-technology-psgct-coimbatore',
        'banaras-hindu-university-bhu-varanasi',
        'uttar-pradesh-rajarshi-tandon-open-university-allahabad',
        'acharya-nagarjuna-university-anu-guntur',
        'chandigarh-group-of-colleges-cgc-landran-mohali',
        'north-eastern-hill-universitynehu-shillong',
        'coimbatore-institute-of-technology-cit-coimbatore',
        'alagappa-university-directorate-of-distance-education-audde-kanchipuram',
        'sri-krishna-college-of-engineering-and-technology-coimbatore',
        'patna-womens-college-pwc-patna',
        'deen-dayal-upadhyaya-gorakhpur-university-gorakhpur',
        'kalasalingam-university-ku-krishnankovil',
        'swami-vivekanand-subharti-university-svsu-meerut',
        'bannari-amman-institute-of-technology-bait-coimbatore',
        'rajalakshmi-engineering-college-rec-chennai',
        'ewing-christian-college-ecc-allahabad',
        'indian-institute-of-management-iim-ahmedabad',
        'rv-college-of-engineering-rvce-bangalore',
        'university-of-calicut-uc-calicut',
        'vit-university-vit-vellore',
        'indian-institute-of-technology-iit-chennai'
       
    ];

    public static function subPageTitle($college, $key, $dropPage = null)
    {
        $collegeName = !empty($college->display_name) ? $college->display_name : $college->name;

        if ($key != 'info') {
            $titleExtension = ArrayHelper::getValue(CollegeHelper::$subPageTitles, $key);
            if (!empty($dropPage)) {
                $title = $collegeName . ' ' . $dropPage . ' ' . $titleExtension;
            } else {
                $title = $collegeName . ' ' . $titleExtension;
            }
        } else {
            $title = 'All about ' . $collegeName;
        }

        return $title ?? '';
    }


    public static $subPagesColumnMapping = [
        'info' => ['content' => 'abt_college', 'title' => 'info_title', 'h1' => 'info_h1', 'meta_description' => 'info_meta_des'],
        'admission' => ['content' => 'admissions', 'title' => 'admission_title', 'h1' => 'admission_h1', 'meta_description' => 'admission_meta_des'],
        'courses-fees' => ['content' => 'fees_page_info', 'title' => 'fees_title', 'h1' => 'fees_h1', 'meta_description' => 'fees_meta_des'],
        'placements' => ['content' => 'career', 'title' => 'placement_title', 'h1' => 'placement_h1', 'meta_description' => 'placement_meta_des'],
        'facilities' => ['content' => 'abt_infra', 'title' => 'infrastructure_title', 'h1' => 'infrastructure_h1', 'meta_description' => 'infrastructure_meta_des'],
        'scholarships' => ['content' => 'scho', 'title' => 'scholarship_title', 'h1' => 'scholarship_h1', 'meta_description' => 'scholarship_meta_des'],
        'cut-off' => ['content' => '', 'title' => 'cut_off_title', 'h1' => 'cut_off_h1', 'meta_description' => 'cut_off_meta_des'],
        'reviews' => ['content' => 'reviews', 'title' => 'review_title', 'h1' => 'review_h1', 'meta_description' => 'review_meta_des'],
        'qna' => ['content' => 'forum', 'title' => 'forum_title', 'h1' => 'forum_h1', 'meta_description' => 'forum_meta_des'], //todo
        'result' => ['content' => 'result', 'title' => 'result_title', 'h1' => 'result_h1', 'meta_description' => 'result_meta_des'],
        'images-videos' => ['content' => 'images', 'title' => 'gallery_title', 'h1' => 'gallery_h1', 'meta_description' => 'gallery_meta_des'] //todo
    ];

    public static $allEntities = [
        'college' => 'College',
    ];

    public static $facilities = [
        'computer-labs' => 'Computer labs',
        'hostel' => 'Hostel',
        'medical' => 'Medical Hospital',
        'cafeteria' => 'Cafeteria',
        'gym' => 'Gym',
        'laboratory' => 'Laboratory',
        'transport' => 'Transport',
        'library' => 'Library',
        'sports' => 'Sports',
        'transport' => 'Bus',
        'auditorium' => 'Auditorium',
        'swimming-pool' => 'Swimming pool',
        'classrooms' => 'Classrooms',
        'canteen' => 'Canteen',
        'banks' => 'Banks',
        'security' => 'Security',
        'wi-fi' => 'wi-fi',
        'e-classroom' => 'e-classroom',
        'communication' => 'Communication',
        'centre' => 'Centre',
        'campus' => 'Campus',
        'playground' => 'Playground',
        'theatre' => 'Theatre',
        'conference-hall' => 'Conference hall',
        'health-center' => 'Health Center',
        'gym' => 'gym',
        'indoor-stadium' => 'Indoor stadium',
        'common-room' => 'Common room',
        'guest-house' => 'Guest House',
    ];

    /**
     *
     * Reviews category
     */
    public static $reviewCategory = [
        '1' => 'Academics & Faculty',
        '2' => 'International Exposure',
        '3' => 'Fees and Scholarship',
        '4' => 'Placements & Internships',
        '5' => 'College Infrastructure',
        '6' => 'Hostels & Food',
        '7' => 'Clubs & Associations',
        '8' => 'Social Atmosphere',
        '9' => 'Admission',
        '10' => 'others'
    ];

    /**
     * Using in getCollegeCategoryRating reviewservice()
     * Reviews category rating
     */
    public static $reviewCategoryRatingValue = [
        '1' => '2.0',
        '2' => '0.8',
        '3' => '2.0',
        '4' => '0.8',
        '5' => '2.0',
        '6' => '0.4'
    ];

    /**
     * Discipline slug for search page
     */
    public static $searchPageDisciplineSlug = [
        'agriculture' => 'agriculture',
        'animation' => 'animation',
        'architecture' => 'architecture',
        'arts' => 'arts',
        'aviation' => 'aviation',
        'commerce' => 'commerce',
        'computer' => 'computer',
        'dental' => 'dental',
        'design' => 'design',
        'education' => 'education',
        'engineering' => 'engineering',
        'hotel-management' => 'hotel-management',
        'law' => 'law',
        'management' => 'management',
        'mass-communication' => 'mass-communication',
        'medical' => 'medical',
        'paramedical' => 'paramedical',
        'pharmacy' => 'pharmacy',
        'science' => 'science',
        'veterinary' => 'veterinary',
        'vocational-courses' => 'vocational-courses',
    ];

    /**
     *  Get User Rating on review
     * @param array| []
     * @return int
     */
    public static function getUserRating($params)
    {
        $revSum = [];
        $revcount = [];
        $total = 0;
        foreach ($params as $para) {
            $data = explode('@***@', $para);
            if ($data[1] != 0) {
                if (array_key_exists($data[0], CollegeHelper::$reviewCategoryRatingValue)) {
                    $revSum[] =  $data[1] * CollegeHelper::$reviewCategoryRatingValue[$data[0]];
                    $revcount[] = CollegeHelper::$reviewCategoryRatingValue[$data[0]];
                }
            }
        }

        if (array_sum(array_values($revSum)) != 0) {
            $total = array_sum(array_values($revSum)) / array_sum(array_values($revcount));

            if ($total * 0.5 < 1) {
                return substr($total, 0, 3) ?? '';
            } else {
                return round($total * 0.5, 1) ?? '';
            }
        }
    }

    /**
     *
     * @ Remove the Space, special character, convert to slug
     * @param string
     * @return string | ''
     */
    public static function clean($string)
    {
        $text = explode('@', $string);
        return Inflector::slug(preg_replace('/[^A-Za-z\-]/', '', $text[0])); // Removes special chars.
    }

    /**
     * @ Setting userName Anonymous
     * @param string as name and email
     */
    public static function getUserName($name, $email)
    {
        if (isset($name) && !empty($name)) {
            return ucfirst($name);
        } else if (!empty($email)) {
            if (!empty(self::clean($email))) {
                return ucfirst(self::clean($email));
            } else {
                return 'Anonymous';
            }
        } else {
            return 'Anonymous';
        }
    }

    /**
     * Get Anonymous User
     * @param int| $userId User ID
     * @return   array|[]
     *
     * will be moved
     */
    public static function getUserAnonymous($userID)
    {
        $query = new Query();
        $query->select(['sbc_user_id'])
            ->from('gmu_anonymous_users')
            ->where(['sbc_user_id' => $userID]);

        $anonymous = $query->one(\Yii::$app->gmudb);
        return $anonymous;
    }

    /**
     * Get Reviewed User details
     * @param int | $userID
     *
     *  @return   array
     *  will be moved
     */
    public static function getReviewUserDetail($userId)
    {
        $key = md5(__CLASS__ . __FUNCTION__ . json_encode(func_get_args()));
        $data = Yii::$app->cache->get($key);
        if ($data == false) {
            $query = new Query();
            $query->distinct();
            $query->select(['reviewed_by', 'first_name', 'last_name', 'email'])
                ->from('gmu_college_reviewer_summary')
                ->where(['reviewed_by' => $userId]);

            $data = $query->one(\Yii::$app->gmudb);
            Yii::$app->cache->set($key, $data, 60 * 60 * 2);

            return $data;
        }

        return $data;
    }

    /**
     *  check Anonymous Uer
     * @param int | $userId
     * @return   string
     *  will be moved
     */

    public static function checkAnonymous($userId)
    {
        $anonymus = self::getUserAnonymous($userId);
        $userData = self::getReviewUserDetail($userId);

        if (!empty($anonymus)) {
            return $userId . '-student';
        } else if (isset($userData['first_name']) && isset($userData['last_name']) &&
            !empty($userData['first_name']) && !empty($userData['last_name'])
        ) {
            return $userId . '-' . strtolower(self::clean($userData['first_name']) . '-' . self::clean($userData['last_name']));
        } else if (isset($userData['email']) && !empty($userData['email'])) {
            $text = self::clean($userData['email']);
            if (!empty($text)) {
                return $userId . '-' . $text;
            } else {
                return $userId . '-student';
            }
        } else {
            return $userId . '-student';
        }
    }

    /**
     * Parse content to replace hash tags
     *
     * @param $content string
     * @return string
     */
    public static function parseContent(string $content): string
    {
        $data = [];

        foreach (self::$hashTagLists as $value) {
            if ($value == '#placement_year' || $value == '#result_year' || $value == '#cutoff_year') {
                $data[$value] = self::PREVIOUS_YEAR;
            } else {
                $data[$value] = self::YEAR;
            }
        }

        return strtr(html_entity_decode($content), $data);
    }

    private static function divider($digit)
    {

        $tens = '1';

        if ($digit > 8) {
            return 10000000;
        }

        while (($digit - 1) > 0) {
            $tens .= '0';
            $digit--;
        }
        return $tens;
    }

    public static function yearsFormat($months)
    {
        $months = (int) $months;
        if (is_int($months) && $months > 0) {
            $monthCaluculation = ($months % 12 == 1) ? ($months % 12) . ' Month' : ($months % 12) . ' Months';
            $year = floor($months / 12);
            if ($year == 0) {
                $result = $monthCaluculation;
            } else {
                if (($months % 12) != 0) {
                    if ($year == 1) {
                        $result = $year . ' Year' . $monthCaluculation;
                    } else {
                        $result = $year . ' Years' . ' ' . $monthCaluculation;
                    }
                } else {
                    $result = ($year == 1) ? $year . ' Year' : $year . ' Years';
                }
            }
        }
        return $result ?? '';
    }
      

    public static function feesFormat($num, $page = '')
    {
        $num = (int) $num;
        $ext = '';
        $digit = strlen($num);

        if ($digit > 5) {
            if ($digit % 2 != 0) {
                $divider = self::divider($digit - 1);
            } else {
                $divider = self::divider($digit);
            }
        } else {
            $divider = 1;
        }

        $fraction = $num / $divider;
       
        $fraction = str_replace('.00', '', number_format($fraction, 2));

        if (!empty($page) && $page == College::ENTITY_COLLEGE_COMPARE) {
            if ($digit == 5) {
                $fraction = number_format($num / 100000, 2);
                $ext = 'LPA';
            }
        }

        if ($digit == 6 || $digit == 7) {
            $ext = 'LPA';
        }
        if ($digit == 8 || $digit == 9) {
            $ext = 'Cr';
        }
        if ($digit == 4 && $page == 'all-colleges') {
            $ext = 'K';
            if ($digit == 4) {
                $fraction = number_format($num / 1000, 0);
            }
        }

        if ($digit == 5 && !empty($page) && $page == 'all-colleges') {
            if ($digit == 5) {
                $fraction = number_format($num / 1000, 0);
            }
            $ext = 'K';
        }

        return $fraction . ' ' . $ext;
    }

    public static function feesFormatListing($num, $page = '')
    {
        $num = (int) $num;
        $ext = '';
        $digit = strlen($num);

        if ($digit > 5) {
            if ($digit % 2 != 0) {
                $divider = self::divider($digit - 1);
            } else {
                $divider = self::divider($digit);
            }
        } else {
            $divider = 1;
        }

        $fraction = $num / $divider;
       
        $fraction = str_replace('.00', '', number_format($fraction, 2));

        if (!empty($page) && $page == College::ENTITY_COLLEGE_COMPARE) {
            if ($digit == 5) {
                $fraction = number_format($num / 100000, 2);
                $ext = 'L';
            }
        }

        if ($digit == 6 || $digit == 7) {
            $ext = 'L';
        }
        if ($digit == 8 || $digit == 9) {
            $ext = 'Cr';
        }
        if ($digit == 4 && $page == 'all-colleges') {
            $ext = 'K';
            if ($digit == 4) {
                $fraction = number_format($num / 1000, 0);
            }
        }

        if ($digit == 5 && !empty($page) && $page == 'all-colleges') {
            if ($digit == 5) {
                $fraction = number_format($num / 1000, 0);
            }
            $ext = 'K';
        }

        return $fraction . ' ' . $ext;
    }


    public static $galleryCategory = [
        'infrastucture' => 'Infrastructure',
        'exterior-images' => 'Campus',
        'others' => 'Other',
        'events' => 'Event',
        'student-life' => 'Student Life',
        'lectures' => 'Learning and Faculty',
        'videos' => 'Videos'
    ];

    public static $feeTypes = [
        'tuition_fees' => 'Tuition Fees',
        'admission_fee' => 'Admission Fee',
        'hostel_fee' => 'Hostel Fee',
        'mess_fee' => 'Mess Fee',
        'other_fee' => 'Other Fee',
        'one_time_payment' => 'One Time Payment'
    ];

    public static $durationType = [
        '' => '--select--',
        0 => 'Year',
        1 => 'Semester',
        2 => 'One Time Fee'
    ];

    public static $collegeHighlights = [
        'Institution Type' => 'heighlightsIcons1',
        'Faculty Student Ratio' => 'heighlightsIcons7',
        'Total Faculty' => 'heighlightsIcons6',
        'Total Area (In Acre)' => 'heighlightsIcons5',
        'Student Enrollment' => 'heighlightsIcons4',
        'Genders Accepted' => 'heighlightsIcons3',
        'Year of Establishment' => 'heighlightsIcons2',
        'Construction Area (Sq. M)' => 'heighlightsIcons1',
        // 'Year of Affliation' => 'heighlightsIcons2',
        'Fellowship' => 'heighlightsIcons2'
    ];

    public static $collegeHighlightsCollegeCompare = [
        'institution-type' => 'Type of Institute',
        'year-of-establishment' => 'Year of Establishment',
        'campus-area' => 'Total Area (In Acre)',
        'total-faculty' => 'Total Faculty',
        'genders-accepted' => 'Genders Accepted',
        'student-enrollments' => 'Student Enrollment',
    ];

    public static $facilitiesCompareCollege = [
        'computer-labs' => 'Computer labs',
        'hostel' => 'Hostel',
        'medical' => 'Medical Hospital',
        'cafeteria' => 'Cafeteria',
        'gym' => 'Gym',
        'laboratory' => 'Laboratory',
        'transport' => 'Transport',
        'library' => 'Library',
        'auditorium' => 'Auditorium',
        'sports' => 'Sports',
    ];
    /**
     * Get the university of name
     * @param int | $collegeId College Id
     * @return array | []
     */
    // will move
    public static function getUniversity($collegeId)
    {
        $query = new Query();
        $query->from('college')
            ->where(['id' => $collegeId]);

        if ($data = $query->one()) {
            return $data;
        }

        return [];
    }

    /**
     * Get College Sceham
     * @param object , array  | $college College Object,  $rating review rating array
     * @return object | JSON Obeject
     */
    public static function collegeSchema($college, $categoryRating, $reviewCount = '')
    {
        $rating = !empty($categoryRating) ? CollegeHelper::getTotalRating($categoryRating) : '';

        if (!empty($rating)) {
            return \yii\helpers\Json::encode([[
                '@context' => 'http://schema.org',
                '@type' => 'CollegeOrUniversity',
                'name' => $college->name,
                'url' => Url::toDomain() . 'college/' . $college->slug,
                'logo' => DataHelper::s3Path(null, 'college_logo', 'path') . '/' . $college->slug . '.jpg',
                'aggregateRating' => [
                    '@type' => 'AggregateRating',
                    'ratingValue' => $rating ?? '',
                    'reviewCount' => !empty($reviewCount) ? $reviewCount : '',
                    'bestRating' => 5,
                    'worstRating' => 1
                ],
                'address' => [
                    '@type' => 'PostalAddress',
                    'streetAddress' => $college->address
                ]
            ]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * Get College Sceham
     * @param object | $faq FAQ array Object
     * @return object | JSON Obeject
     */
    public static function faqSchema($faqs)
    {
        $loadFaq = [];

        foreach ($faqs as $faq) {
            $loadFaq[] = [
                '@type' => 'Question',
                'name' => ContentHelper::htmlDecode($faq->question, true),
                'acceptedAnswer' => [
                    '@type' => 'Answer',
                    'text' => ContentHelper::htmlDecode($faq->answer, false)
                ]
            ];
        }

        return \yii\helpers\Json::encode([[
            '@context' => 'http://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => $loadFaq
        ]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    /**
     *  Get College Page URL formate
     *  @param $slug | College Slug
     *  @param $page | College Sub Page Slug
     *  return string | emty string
     */
    public static function collegeUrlFormate($slug, $page, $programCourseSlug = '')
    {
        $arr = ['admission', 'reviews', 'qna'];
        
        if ($page == 'info') {
            $url = $slug;
        } else if (in_array($page, $arr)) {
            $url = $slug . '/' . $page;
        } else if ($page == 'pi') {
            $url = $slug . '-courses-fees' . '/' . $programCourseSlug . '-' . $page;
        } else if ($page == 'ci') {
            $url = $slug . '-courses-fees' . '/' . $programCourseSlug . '/' . $page;
        } else {
            $url = $slug . '-' . $page;
        }

        return $url ?? '';
    }

    public static function collegeDropDownUrlFormate($slug, $dropDown, $sub_page)
    {
        if ($dropDown == 'UG' || $dropDown == 'PG') {
            return $slug . '-' . strtolower($dropDown) . '-' . $sub_page;
        } else {
            if ($sub_page == 'syllabus' || $sub_page == 'cut-off') {
                return $slug . '-' . $sub_page . '/' . $dropDown;
            }
        }
        return $url ?? '';
    }

    /*
     * Total Star Count.
     * @param array| $stars.
     * @param int| $fullStars.
     * @param int| $halfStars.
     * @param int| $greyStars.
     *
     * @return string| html for stars.
     **/
    public static function getTotalStars($stars)
    {
        $fullStars = floor($stars);
        $halfStars = $stars - $fullStars;
        $greyStars = floor(5 - $stars);

        for ($i = 0; $i < $fullStars; $i++) {
            $data[] = '<i class="spriteIcon full-star"></i>';
        }

        for ($i = 0; $i < $halfStars; $i++) {
            $data[] = '<i class="spriteIcon half-star"></i>';
        }

        for ($i = 0; $i < $greyStars; $i++) {
            $data[] = '<i class="spriteIcon empty-star"></i>';
        }

        $data = implode('', $data);

        return $data;
    }

    /**
     * Get the total rating of college
     * @param $rating | Category rating array []
     * return int| 0
     */
    public static function getTotalRating($rating)
    {
        $ratingNum = 0;
        $ratingNumIterationCount = 0;
        foreach ($rating as $key => $val) {
            if (empty($val) || $val == 0) {
                continue;
            }
            $ratingNum += $val;
            $ratingNumIterationCount++;
        }
        if ($ratingNum != 0 && $ratingNumIterationCount != 0) {
            $ratingT = substr($ratingNum / $ratingNumIterationCount, 0, 3);
        }

        return $ratingT ?? 0;
    }


    public static function getCourseBrochure($courseId, $collegeId)
    {
        $brochure = Brochure::find()->select(['id', 'pdf', 'course_id', 'college_id', 'file_name', 'entity'])
            ->where(['entity' => 'course'])
            ->andWhere(['course_id' => $courseId])
            ->andWhere(['college_id' => $collegeId])
            ->andWhere(['status' => Brochure::STATUS_ACTIVE])
            ->one();

        return $brochure ?? [];
    }

    //looping through text nodes until it reaches the certain number of words, and then removing everything after that.
    public static function contentFormat($content)
    {
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($content);
        $xpath = new DOMXPath($dom);
        $all_text_nodes = $xpath->query('//text()');
        $total_words = 50;
        foreach ($all_text_nodes as $text_node) {
            $text = $text_node->textContent;
            $words = explode(' ', $text);
            $word_count = count($words);
            if ($word_count < $total_words) {
                $total_words -= $word_count;
                continue;
            }
            $arrayValue = implode(' ', array_slice($words, 0, $total_words));
            $text_node->textContent = $arrayValue;
            $final_value = $text_node;
            while ($final_value->parentNode) {
                while ($final_value->nextSibling) {
                    $final_value->parentNode->removeChild($final_value->nextSibling);
                }
                $final_value = $final_value->parentNode;
            }
            break;
        }
        $output = substr($dom->saveHTML($dom->getElementsByTagName('body')->item(0)), strlen('<body>'), -strlen('</body>'));

        return $output ?? [];
    }

    //page level sticky cta location and naming
    public static $pageLevelCTALocation = [
        'info' => [
            'name' => 'Check College Details',
            'title' => 'Register to get college details'
        ],
        'courses-fees' => [
            'name' => 'Check Course Details',
            'title' => 'Register to get course details',
        ],
        'admission' => [
            'name' => 'Check Admission ' . self::YEAR,
            'title' => 'Register to get admission details'
        ],
        'cut-off' => [
            'name' => 'Cutoff Details <span class="spriteIcon whiteDownloadIcon"></span>',
            'title' => 'Register to get cutoff details'
        ],
        'placements' => [
            'name' => 'Placement Report <span class="spriteIcon whiteDownloadIcon"></span>',
            'title' => 'Register to get placement details'
        ],
        'facilities' => [
            'name' => 'Facilities Report <span class="spriteIcon whiteDownloadIcon"></span>',
            'title' => 'Register to get infrastructure details'
        ],
        'images-videos' => [
            'name' => 'Talk to Experts',
            'title' => 'Register to get free counselling'
        ],
        'scholarships' => [
            'name' => 'Scholarship List <span class="spriteIcon whiteDownloadIcon"></span>',
            'title' => 'Register to get scholarship details'
        ],
        'reviews' => [
            'name' => 'College Reviews <span class="spriteIcon whiteDownloadIcon"></span>',
            'title' => 'Regoster to get college reviews'
        ],
        'result' => [
            'name' => 'Result Details <span class="spriteIcon whiteDownloadIcon"></span>',
            'title' => 'Register to get result details'
        ],
        'program' => [
            'name' => 'Talk to Experts',
            'title' => 'Register for free counselling'
        ],
        'ci' => [
            'name' => 'Talk to Experts',
            'title' => 'Register for free counselling'
        ]
    ];

    //returning non empty eligibility and fees records
    public static function getNonEmptyCourse(array $arr, $page = null)
    {
        if (empty($arr)) {
            return [];
        }

        $items = [];
        foreach ($arr as $ar) {
            if (empty($ar['avgFees']) && empty($ar['duration']) && in_array($page, ['info', 'courses-fees', 'facilities'])) {
                continue;
            }

            if (empty($ar['exams']) && $page == 'admission') {
                continue;
            }

            if (empty($ar['exams'])  && empty($ar['avgFees']) && $page == 'scholarships') {
                continue;
            }

            $items[$ar['name']] = $ar;
        }

        return $items;
    }

    public static $collegePageCi = [
        'courses-fees' => 'Fees Structure',
        'cut-off' => 'Cutoff',
        'admission' => 'Admission',
        'facilities' => 'Infrastructure',
        'result' => 'Result',
        'scholarships' => 'Scholarships',
        'placements' => 'Placements',
        'images-videos' => 'Images & Videos',
        'ranking' => 'Ranking',
        'alumni' => 'Alumni',
        'hostel' => 'Hostel',
        'application-form' => 'Application Form',
        'syllabus' => 'Syllabus',
        'verdict' => 'Verdict'
    ];

    public static $removeCollegePageOnCi = ['info', 'reviews', 'qna', 'compare', 'images-videos'];

    public static function sortReviewContentBasedOnPage($arr, $category = 5)
    {
        $items = [];
        $data = [];
        foreach ($arr as $content) {
            $reviewContent = !empty($content) ? explode('@@@', $content) : '';
            if (isset($reviewContent[1])) {
                $items[$reviewContent[0]] = $reviewContent[1];
            }
        }
        if (isset($items[$category])) {
            $data[$category] = $items[$category];
            unset($items[$category]);
        }
        return $data + $items;
    }

    //tempory result will moved once filter
    public static function getDataBeforelogin($str, $category = 5)
    {
        $items = [];
        $strArr = explode('###', $str);
        $data = self::sortReviewContentBasedOnPage($strArr, $category);
        foreach ($data as $k => $v) {
            $items[] = $k . '@@@' . $v;
        }

        return implode('###', $items);
    }

    public static $cutOffType = [
        'AI' => 1,
        'GO' => 2,
        'HS' => 3,
        'JK' => 4,
        'LA' => 5,
        'OS' => 6
    ];

    public static $cutoffGender = [
        'All' => 1,
        'Female' => 2,
        'Gender Neutral' => 3,
        'Male' => 4
    ];

    public static function formateDate(array $arr)
    {
        $currentDate = Carbon::now()->toDateString();
        $examDateList = DataHelper::examDateList();
        $items = [];
        foreach ($arr as $ar) {
            if (!empty($ar['date'])) {
                $items[$ar['slug']]['name'] = $ar['display_name'];
                $items[$ar['slug']]['slug'] = $ar['slug'];
                $items[$ar['slug']]['exam_id'] = $ar['exam_id'];
                foreach ($ar['date'] as $k => $v) {
                    $dateArr = explode('|', $v);
                    if (!empty($dateArr[0])) {
                        if ($dateArr[0] <= $currentDate) {
                            $items[$ar['slug']]['experied'][] = [
                                'name' => isset($examDateList[str_replace('', '-', $k)]) ? $examDateList[str_replace('', '-', $k)] : '',
                                'startDate' => $dateArr[0],
                                'endDate' => $dateArr[1]
                            ];
                        } else if ($dateArr[0] >= $currentDate) {
                            $items[$ar['slug']]['current'][] = [
                                'name' => isset($examDateList[str_replace('', '-', $k)]) ? $examDateList[str_replace('', '-', $k)] : '',
                                'startDate' => $dateArr[0],
                                'endDate' => $dateArr[1]
                            ];
                        }
                    }
                }
                // if (!isset($items[$ar['slug']]['current'])) {
                //     unset($items[$ar['slug']]);
                // }
            }
        }

        return $items;
    }

    /** Numer with suffix with letter */
    public static function numberSufixWithAlphebet($number)
    {
        $ends = ['th', 'st', 'nd', 'rd', 'th', 'th', 'th', 'th', 'th', 'th'];
        if ((($number % 100) >= 11) && (($number % 100) <= 13)) {
            return $number . 'th';
        } else {
            return $number . $ends[$number % 10];
        }
    }

    public static $type = [
        'full_time' => 'Full Time',
        'part_time' => 'Part Time',
        'distance_learning' => 'Distance',
    ];

    public static function getUserId()
    {
        return Yii::$app->user->getId();
    }

    //CTA Nomenature for program Page
    public static $leadCtaTextLocation = [
        '17' => [
            'cta_text' => 'Download Eligibility Report <span class="spriteIcon whiteDownloadIcon"></span>',
            'ctaLocationMobile' => 'colleges_program_information_wap_lead_center_mtf_cta1',
            'ctaLocationDesktop' => 'colleges_program_information_web_lead_center_mtf_cta1',
            'title' => 'REGISTER NOW TO GET DETAILED ELIGIBILITY',
        ],
        '18' => [
            'cta_text' => 'Download Fees Report <span class="spriteIcon whiteDownloadIcon"></span>',
            'ctaLocationMobile' => 'colleges_program_information_wap_lead_center_mtf_cta2',
            'ctaLocationDesktop' => 'colleges_program_information_web_lead_center_mtf_cta2',
            'title' => 'REGISTER TO COMPLETE FEES DETAILS',
        ],
        '19' => [
            'cta_text' => 'Download Placement Report <span class="spriteIcon whiteDownloadIcon"></span>',
            'ctaLocationMobile' => 'colleges_program_information_wap_lead_center_mtf_cta3',
            'ctaLocationDesktop' => 'colleges_program_information_web_lead_center_mtf_cta3',
            'title' => 'REGISTER TO GET PLACEMENT REPORT',
        ]
    ];

    //can be removed later
    public static $filterGroupMissingCourses = [
        'post-graduate-programme-in-food-and-agri-business-management-pgp-fabm',
        'fellow-programme-in-management-fpm',
        'executive-mba',
        'executive-post-graduate-programme-in-management-epgp',
        'mba-business-analytics',
        'bhsp',
        'pgp-abm',
    ];

    public static $programDurationType = [
        1 => 'Months',
        2 => 'Days',
        3 => 'Hours',
    ];

    public static function yearsFormatCourses($months, $durationType)
    {

        if ($durationType !== 'Months') {
            $result = $months . ' ' . $durationType;
        } else {
            $months = (int) $months;
            if (is_int($months) && $months > 0) {
                $monthCaluculation = ($months % 12 == 1) ? ($months % 12) . ' Month' : ($months % 12) . ' Months';
                $year = floor($months / 12);
                if ($year == 0) {
                    $result = $monthCaluculation;
                } else {
                    if (($months % 12) != 0) {
                        if ($year == 1) {
                            $result = $year . ' Year' . $monthCaluculation;
                        } else {
                            $result = $year . ' Years' . ' ' . $monthCaluculation;
                        }
                    } else {
                        $result = ($year == 1) ? $year . ' Year' : $year . ' Years';
                    }
                }
            }
        }

        return $result ?? '';
    }

    public static $collegeType = [
        1 => 'University',
        2 => 'Private Un-Aided',
        3 => 'Private Aided',
        4 => 'State Government',
        5 => 'Local Body',
        6 => 'Central Government',
        7 => 'Deemed University-Government Aided',
        8 => 'Deemed University-Private',
        9 => 'Institute of National Importance',
        10 => 'State Public University',
        11 => 'State Private University',
        12 => 'Central University',
        13 => 'Institute under State Legislature Act'
    ];

    public static $LocationType = [
        1 => 'Urban',
        2 => 'Rural'
    ];

    public static $hostelType = [
        1 => 'Boys Hostel',
        2 => 'Girls Hostel',
        3 => 'Others'
    ];

    public static $casteCategory = [
        1 => 'All',
        2 => 'General',
        3 => 'PWD',
        4 => 'SC',
        5 => 'ST',
        6 => 'OBC',
        7 => 'Muslim',
        8 => 'PWD ST',
        9 => 'PWD SC',
        10 => 'PWD OBC',
        11 => 'PWD General',
        12 => 'Other Minority',
        13 => 'Other Minority SC',
        14 => 'Other Minority ST',
        15 => 'Other Minority OBC',
        16 => 'Other Minority General',
        17 => 'Muslim Minority ST',
        18 => 'Muslim Minority SC',
        19 => 'Muslim Minority OBC',
        20 => 'Muslim Minority General'
    ];

    public static $enrollementType = [
        1 => 'Student Enrolment',
        2 => 'Teaching Staff',
    ];

    public static $_hashTagLists = [
        '{accredation}',
        '{accredation_body}',
        '{accredation_value}',
        '{affiliat_university}',
        '{approval_body}',
        '{area_in_acre}',
        '{boys_hostel_count}',
        '{city}',
        '{college_name}',
        '{college_short_name}',
        '{college_type}',
        '{count_of_colleges_affiliated}',
        '{course}',
        '{course_count}',
        '{course_level}',
        '{course_stream}',
        '{courses}',
        '{enrollment_count}',
        '{enrollment_year}',
        '{exam}',
        '{tagged_exams}',
        '{college_facilities}',
        '{girls_hostel_count}',
        '{placement_companies}',
        '{rank}',
        '{rank_type}',
        '{ranking}',
        '{ranking_body}',
        '{ranking_type}',
        '{ranking_year}',
        '{recognition_body}',
        '{state}',
        '{stream}',
        '{stream_of_course}',
        '{total_student_enrollment}',
        '{student_teacher_ratio}',
        '{total_enrollment}',
        '{type}',
        '{year_of_establishment}',
        '{count_of_program}',
        '{level_of_course}',
        '{undergraduate_course_count}',
        '{postgraduate_course_count}',
        '{program_name}',
        '{program_stream}',
        '{program_duration}',
        '{program_level}',
        '{total_tuition_fees}',
        '{college_full_name}',
        '{salary}',
        '{program_specialization}'
    ];
}
