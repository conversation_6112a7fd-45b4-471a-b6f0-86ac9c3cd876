<?php

namespace common\helpers;

use common\models\Board;
use common\models\BoardContent;
use yii\db\Query;
use Yii;
use yii\helpers\Inflector;

class BoardHelper
{
    const YEAR = '2025';

    public static $subjects = [
        'bangla' => 'Bangla',
        'advanced-maths' => 'Advanced Maths',
        'maths' => 'Maths',
        'hindi' => 'Hindi',
        'english' => 'English',
        'home-science-optional' => 'Home Science (Optional)',
        'science' => 'Science',
        'social-science' => 'Social Science',
        'dance-kathak' => 'Dance (Kathak)',
        'urdu' => 'Urdu',
        'sanskrit' => 'Sanskrit',
        'physics' => 'Physics',
        'chemistry' => 'Chemistry',
        'biology' => 'Biology',
        'agriculture' => 'Agriculture',
        'computer-science-cse' => 'Computer Science (CSE)',
        'multimedia-and-web-technology-mwt' => 'Multimedia and Web Technology (MWT)',
        'entrepreneurship' => 'Entrepreneurship',
        'accountancy' => 'Accountancy',
        'english-communicative' => 'English Communicative',
        'english-language-literature' => 'English Language & Literature',
        'telugu' => 'Telugu',
        'french' => 'French',
        'arabic' => 'Arabic',
        'mathematics' => 'Mathematics',
        'botany' => 'Botany',
        'zoology' => 'Zoology',
        'commerce' => 'Commerce',
        'ecomonics' => 'Ecomonics',
        'civics' => 'Civics',
        'history' => 'History',
        'geography' => 'Geography',
        'geology' => 'Geology',
        'home-science' => 'Home Science',
        'logic' => 'Logic',
        'music' => 'Music',
        'public-administration' => 'Public Administration',
        'sociology' => 'Sociology',
        'psychology' => 'Psychology',
        'physical-science' => 'Physical Science',
        'bioligy' => 'Bioligy',
        'social-studies' => 'Social Studies',
        'assamese' => 'Assamese',
        'bengali' => 'Bengali',
        'manipuri' => 'Manipuri',
        'computer-science' => 'Computer Science',
        'philosophy' => 'Philosophy',
        'political-science' => 'Political Science',
        'environmental-science' => 'Environmental Science',
        'general-english' => 'General English',
        'environmental-study' => 'Environmental Study',
        'elements-of-science' => 'Elements of Science',
        'crop-production-and-horticulture' => 'Crop production and horticulture',
        'konkani' => 'Konkani',
        'marathi' => 'Marathi',
        'economics' => 'Economics',
        'cost-accounting' => 'Cost Accounting',
        'business-studies' => 'Business Studies',
        'gujarati' => 'Gujarati',
        'health-and-physical-education' => 'Health and Physical Education',
        'physical-education' => 'Physical Education',
        'business-study' => 'Business Study',
        'education' => 'Education',
        'kannada' => 'Kannada',
        'malayalam' => 'Malayalam',
        'computer-application' => 'Computer Application',
        'geometry' => 'Geometry',
        'odia' => 'Odia',
        'punjabi' => 'Punjabi',
        'tamil' => 'Tamil',
        'home-management' => 'Home Management',
        'nutrition' => 'Nutrition',
        'statistics' => 'Statistics',
        'book-keeping-and-accounting' => 'Book Keeping and Accounting',
        'business-organization' => 'Business Organization',
        'economics-and-commercial-geography' => 'Economics and Commercial Geography',
        'gurmukhi' => 'Gurmukhi',
        'life-science' => 'Life Science',
        'informatics-practices' => 'Informatics Practices',
        'multimedia-and-web-technology' => 'Multimedia and Web Technology',
        'alternative-english' => 'Alternative English',
        'ao' => 'AO',
        'environmental-education' => 'Environmental Education',
        'information-technology' => 'Information Technology',
        'iit' => 'IIT',
        'lotha' => 'Lotha',
        'sumi' => 'Sumi',
        'tenyide' => 'Tenyide',
        'capital-market' => 'Capital Market',
        'derivative-markets' => 'Derivative Markets',
        'fundamental-of-business-mathematics' => 'Fundamental of Business Mathematics',
        'tenyidie' => 'Tenyidie',
        'garo' => 'Garo',
        'health-education' => 'Health Education',
        'khasi' => 'Khasi',
        'mizo' => 'Mizo',
        'nepali' => 'Nepali',
        'science-and-technology' => 'Science and Technology',
        'anthropology' => 'Anthropology',
        'computer-technique' => 'Computer Technique',
        'foundation-course' => 'Foundation Course',
        'hindi-10th' => 'Hindi (10th)',
        'english-10th' => 'English (10th)',
        'kannada-10th' => 'Kannada (10th)',
        'sanskrit-10th' => 'Sanskrit (10th)',
        'punjabi-10th' => 'Punjabi (10th)',
        'mathematics-10th' => 'Mathematics (10th)',
        'science-and-technology-10th' => 'Science and Technology (10th)',
        'social-science-10th' => 'Social Science (10th)',
        'economics-10th' => 'Economics (10th)',
        'business-studies-10th' => 'Business Studies (10th)',
        'home-science-10th' => 'Home Science (10th)',
        'psychology-10th' => 'Psychology (10th)',
        'indian-culture-and-heritage-10th' => 'Indian Culture and Heritage (10th)',
        'accountancy-10th' => 'Accountancy (10th)',
        'painting-10th' => 'Painting (10th)',
        'assamese-10th' => 'Assamese (10th)',
        'data-entry-operation-10th' => 'Data Entry Operation (10th)',
        'nepali-10th' => 'Nepali (10th)',
        'arabic-10th' => 'Arabic (10th)',
        'persian-10th' => 'Persian (10th)',
        'tamil-10th' => 'Tamil (10th)',
        'hindi-12th' => 'Hindi (12th)',
        'english-12th' => 'English (12th)',
        'bengali-12th' => 'Bengali (12th)',
        'tamil-12th' => 'Tamil (12th)',
        'odia-12th' => 'Odia (12th)',
        'gujarati-12th' => 'Gujarati (12th)',
        'sanskrit-12th' => 'Sanskrit (12th)',
        'punjabi-12th' => 'Punjabi (12th)',
        'sociology-12th' => 'Sociology (12th)',
        'physics-12th' => 'Physics (12th)',
        'chemistry-12th' => 'Chemistry (12th)',
        'biology-12th' => 'Biology (12th)',
        'history-12th' => 'History (12th)',
        'geography-12th' => 'Geography (12th)',
        'political-science-12th' => 'Political Science (12th)',
        'economics-12th' => 'Economics (12th)',
        'business-studies-12th' => 'Business Studies (12th)',
        'accountancy-12th' => 'Accountancy (12th)',
        'home-science-12th' => 'Home Science (12th)',
        'psychology-12th' => 'Psychology (12th)',
        'computer-science-12th' => 'Computer Science (12th)',
        'painting-12th' => 'Painting (12th)',
        'environmental-science-12th' => 'Environmental Science (12th)',
        'mass-communication-12th' => 'Mass Communication (12th)',
        'entrepreneurship-12th' => 'Entrepreneurship (12th)',
        'spanish' => 'Spanish',
        'chinese' => 'Chinese',
        'development-studies' => 'Development Studies',
        'dutch' => 'Dutch',
        'german' => 'German',
        'japanese' => 'Japanese',
        'korean' => 'Korean',
        'portuguese' => 'Portuguese',
        'bangla-10th' => 'Bangla (10th)',
        'bhojpuri-10th' => 'Bhojpuri (10th)',
        'farsi-10th' => 'Farsi (10th)',
        'physical-education-10th' => 'Physical Education (10th)',
        'science-10th' => 'Science (10th)',
        'urdu-10th' => 'Urdu (10th)',
        'bangla-12th' => 'Bangla (12th)',
        'bhojpuri-12th' => 'Bhojpuri (12th)',
        'mathematics-12th' => 'Mathematics (12th)',
        'philosophy-12th' => 'Philosophy (12th)',
        'physical-education-12th' => 'Physical Education (12th)',
        'commercial-studies' => 'Commercial Studies',
        'mass-communication' => 'Mass Communication',
        'painting' => 'Painting',
        'zoology-12th' => 'Zoology (12th)',
    ];

    public static $pages = [
        'Overview' => 'Overview',
        'Date Sheet' => 'Date Sheet',
        'Time Table' => 'Time Table',
        'Routine' => 'Routine',
        'Registration Form' => 'Registration Form',
        'Application Form' => 'Application Form',
        'Registration card' => 'Registration card',
        'Admit Card' => 'Admit Card',
        'Hall Ticket' => 'Hall Ticket',
        'Syllabus' => 'Syllabus',
        'Deleted Syllabus' => 'Deleted Syllabus',
        'Exam Centres' => 'Exam Centres',
        'Marking Scheme' => 'Marking Scheme',
        'Results' => 'Results',
        'Supplementary' => 'Supplementary',
        'Sample Papers' => 'Sample Papers',
        'Previous Year Question Papers' => 'Previous Year Question Papers',
        'Solved Question Papers' => 'Solved Question Papers',
        'Reference Books' => 'Reference Books',
        'Exam Pattern' => 'Exam Pattern',
        'Preparation' => 'Preparation',
        'Answer Key' => 'Answer Key',
        'Books' => 'Books',
        'Toppers' => 'Toppers',
        'Notes' => 'Notes',
        'Question Paper' => 'Question Paper',
        'Passing Marks' => 'Passing Marks',
        'Grading System' => 'Grading System',
        'Marksheet' => 'Marksheet',
        'Practical Exam Datesheet' => 'Practical Exam Datesheet',
        'Roll Number Finder' => 'Roll Number Finder'
    ];

    public static $syllabusSubPagesDropDown = [
        ['name' => 'Mathematics', 'value' => 'Mathematics'],
        ['name' => 'Physics', 'value' => 'Physics'],
        ['name' => 'Chemistry', 'value' => 'Chemistry'],
        ['name' => 'Biology', 'value' => 'Biology'],
        ['name' => 'Computer Science', 'value' => 'Computer Science'],
        ['name' => 'Economics', 'value' => 'Economics'],
        ['name' => 'English', 'value' => 'English'],
        ['name' => 'Business Studies', 'value' => 'Business Studies'],
        ['name' => 'Accountancy', 'value' => 'Accountancy'],
        ['name' => 'Hindi', 'value' => 'Hindi'],
        ['name' => 'Home Science', 'value' => 'Home Science'],
        ['name' => 'Physical Education', 'value' => 'Physical Education'],
        ['name' => 'History', 'value' => 'History'],
        ['name' => 'Geography', 'value' => 'Geography'],
        ['name' => 'Political Science', 'value' => 'Political Science'],
        ['name' => 'Psychology', 'value' => 'Psychology'],
        ['name' => 'Sanskrit', 'value' => 'Sanskrit'],
        ['name' => 'Sociology', 'value' => 'Sociology'],
        ['name' => 'Social Science', 'value' => 'Social Science'],
        ['name' => 'Science', 'value' => 'Science']
    ];

    public static $supplementarySubPagesDropDown = [
        ['name' => 'Time Table', 'value' => 'Time Table'],
        ['name' => 'Date Sheet', 'value' => 'Date Sheet'],
        ['name' => 'Admit Card', 'value' => 'Admit Card'],
        ['name' => 'Hall Ticket', 'value' => 'Hall Ticket'],
        ['name' => 'Result', 'value' => 'Result']
    ];

    public static $subPages = [
        'overview' => 'Overview',
        'date-sheet' => 'Date Sheet',
        // 'time-table' => 'Time Table',
        // 'routine' => 'Routine',
        'registration-form' => 'Registration Form',
        'application-form' => 'Application Form',
        'registration-card' => 'Registration card',
        'admit-card' => 'Admit Card',
        // 'hall-ticket' => 'Hall Ticket',
        'syllabus' => 'Syllabus',
        'deleted-syllabus' => 'Deleted Syllabus',
        'exam-centres' => 'Exam Centres',
        'marking-scheme' => 'Marking Scheme',
        'results' => 'Results',
        'supplementary' => 'Supplementary',
        'sample-papers' => 'Sample Papers',
        'previous-year-question-papers' => 'Previous Year Question Papers',
        'solved-question-papers' => 'Solved Question Papers',
        // 'reference-books' => 'Reference Books',
        'exam-pattern' => 'Exam Pattern',
        'preparation' => 'Preparation',
        'answer-key' => 'Answer Key',
        'books' => 'Books',
        'toppers' => 'Toppers',
        'notes' => 'Notes',
        'question-paper' => 'Question Paper',
        'passing-marks' => 'Passing Marks',
        'grading-system' => 'Grading System',
        'marksheet' => 'Marksheet',
        'practical-exam-datesheet' => 'Practical Exam Datesheet',
        'roll-number-finder' => 'Roll Number Finder'
    ];

    public static $boardType = [
        '0' => 'National Boards',
        '1' => 'State Boards',
        '2' => 'Open Boards'
    ];

    public static $_hashTagYear = [
        '#year' => self::YEAR,
    ];

    public static $boardDefaultSeoInfo = [
        'overview' => [
            'h1' => '{board-name} Exam {year}',
            'title' => '{board-name} Exam {year} | Check Exam Date, Time Table, Admit Card, Syllabus, Result',
            'description' => 'Everything you need to know about the {board-name} examination {year}. Get latest updates on {board-name} syllabus, timetable, results, and more.'
        ],
        'date-sheet' => [
            'h1' => '{board-name} Time Table {year}',
            'title' => '{board-name} Time Table {year} | Check Updated Date Sheet Here',
            'description' => 'Check out the latest updates on {board-name} date sheet {year}. Students can view and download the official date sheet for the {year} exam here.'
        ],
        'syllabus' => [
            'h1' => '{board-name} Syllabus {year}: Detailed Term-Wise Topics, Important Chapters, Marking Scheme',
            'title' => '{board-name} Syllabus {year} | Detailed Term-Wise Topics, Important Chapters, Marking Scheme',
            'description' => 'Get {board-name} syllabus for all subjects. Find the latest syllabus issued by {board-name} and detailed term-wise topics, important chapters, and the marking scheme.'
        ],
        'registration-form' => [
            'h1' => '{board-name} Registration {year}',
            'title' => '{board-name} Registration {year} | Check {board-name} Registration {year} Dates, Process and Fees',
            'description' => 'Get updates on the {board-name} registration process and check out the detailed registration fee structure and other important information.'
        ],
        'admit-card' => [
            'h1' => '{board-name} Admit Card {year}',
            'title' => 'Download {board-name} Admit Card {year} | Check Release Date, Steps to download admit card',
            'description' => 'Get all updates on {board-name} admit card {year}. Check the release date and the procedure to collect {board-name} admit card {year}.'
        ],
        'exam-centres' => [
            'h1' => '{board-name} Exam Centre List {year}',
            'title' => '{board-name} Exam {year} Centres - Check List Here',
            'description' => '{board-name} updates the list of examination centres every year. Check out the important details regarding the {board-name} exam centres here.'
        ],
        'results' => [
            'h1' => '{board-name} Result {year}',
            'title' => 'Check {board-name} Result {year}  Release Date, Download Marksheet',
            'description' => 'Find out the {board-name} result release date and download your marksheet/scorecard. Check {board-name} exam results {year} using the direct link provided.'
        ],
        'sample-papers' => [
            'h1' => '{board-name} Sample Paper {year} - Download Free PDF',
            'title' => 'Download {board-name} Sample Paper {year} PDF',
            'description' => 'Start preparing for the {board-name} final exams by practising the sample papers. Download {board-name} sample question papers for free.'
        ],
        'previous-year-question-papers' => [
            'h1' => '{board-name} Previous Year Question Papers {year}',
            'title' => 'Download {board-name} Previous Year Question Papers {year} PDF',
            'description' => '<Boards Name> previous year papers will help you analyse the exam pattern and level of questions. Practice these previous year papers to score well.'
        ],
        'solved-question-papers' => [
            'h1' => '{board-name} Solved Question Papers {year}',
            'title' => 'Download {board-name} Solved Question Papers {year} PDF',
            'description' => 'Solving more questions will improve time management skills for exams. Download {board-name} solved question papers now and practise.'
        ],
        'application-form' => [
            'h1' => '{board-name} Application Form {year}',
            'title' => 'Check {board-name} Application Form {year} Dates, Process and Fees',
            'description' => 'Get updates on the {board-exam-year} application process and check out the details of the fee structure and other important information.'
        ],
        'hall-ticket' => [
            'h1' => '{board-name} Hall Ticket {year}',
            'title' => 'Download {board-name} Hall Ticket {year} | Check Release Date, Steps to download Hall Ticket',
            'description' => 'Get complete details on {board-name} hall ticket {year}. Check the release date and the procedure to collect {board-name} admit card {year}.'
        ],
        'marking-scheme' => [
            'h1' => '{board-name} {year} Marking Scheme/Grading Pattern',
            'title' => '{board-name} {year} Marking Scheme/Grading Pattern - Minimum Marks to Qualify',
            'description' => 'Get complete details about the {board-name} marking scheme {year}. Know the minimum marks to qualify according to the latest marking scheme.'
        ],
        'reference-books' => [
            'h1' => 'Best Reference Books for {board-name} Exam Praparation',
            'title' => 'Best Reference Books for {board-name} Exam Praparation',
            'description' => 'Get detailed information on {board-name} reference books for the {year} academic year. Download the textbooks and prepare for {board-name} examination {year}.'
        ],
        'routine' => [
            'h1' => '{board-name} Routine {year}',
            'title' => '{board-name} Routine {year} | Check Updated Routine Here',
            'description' => 'Check out the latest information on {board-name} routine {year}. Students can view and download the official routine for the {year} exam here.'
        ],
        'supplementary' => [
            'h1' => '{board-name} Supplementary Exam {year} - Date Sheet, Result {year}',
            'title' => '{board-name} Supplementary Exam {year} | Check Supplementary Exam Date Sheet, Admit Card, Result {year}',
            'description' => 'Get complete information on {board-name} supplementary exam {year}. Check out the updated syllabus, timetable, admit card, results, and more.'
        ],
        'time-table' => [
            'h1' => '{board-name} Time Table {year}',
            'title' => '{board-name} Time Table {year} | Check Updated Time Table Here',
            'description' => 'Check out the latest information on {board-name} time table {year}. Students can view and download the official timetable for the {year} exam here.'
        ],
        'toppers' => [
            'h1' => '{board-name} Class # Toppers List {year}: Rank, Marks, School Code',
            'title' => '{board-name} Class # Toppers List {year}: Rank, Marks, School Code',
            'description' => '{board-name} has released the topper’s list and pass percentage along with Class 12th result. Check out stream-wise toppers, pass percentage and result details here.'
        ],
        'notes' => [
            'h1' => '{board-name} english notes {year}: Download PDF',
            'title' => '{board-name} English Notes {year}: Download PDF',
            'description' => '{board-name} Notes includes chapter wise explanations for all the topic provided in the curriculum.'
        ],
        'question-paper' => [
            'h1' => '{board-name} Maths Question Paper {year}',
            'title' => '{board-name} 12 Maths Question Paper {year}',
            'description' => '{board-name} Question Paper {year} is provided in PDF format, which can be downloaded for free. Use the Question Paper {year} to enhance your preparation.'
        ],
        'books' => [
            'h1' => '{board-name} Textbooks: Download PDF',
            'title' => '{board-name} Textbooks: Download PDF',
            'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
        ],
        'syllabus-dropdown' => [
            'h1' => '{board-name} {subject} Syllabus 2023-24: Download PDF',
            'title' => '{board-name} {subject} Syllabus 2023-24: Download PDF',
            'description' => 'The {board-name} {subject} Syllabus 2023-24 consists of Probability, Linear Programming, Vector, Three Dimensional Geometry and more.'
        ],
        'supplementary-time-table' => [
            'h1' => '{board-name} Supplementary Time Table 2023',
            'title' => '{board-name} Supplementary Time Table 2023',
            'description' => '{board-name} 2023 Compartment Date Sheet is released on May 30, 2023. Check the {board-name} latest exam pattern, syllabus, important dates.'
        ],
        'supplementary-date-sheet' => [
            'h1' => '{board-name} Supplementary Time Table 2023',
            'title' => '{board-name} Supplementary Time Table 2023',
            'description' => '{board-name} 2023 Compartment Date Sheet is released on May 30, 2023. Check the {board-name} latest exam pattern, syllabus, important dates.'
        ],
        'supplementary-admit-card' => [
            'h1' => '{board-name} Compartment Exam Admit Card 2023',
            'title' => '{board-name} Compartment Exam Admit Card 2023',
            'description' => 'Check out the release date of {board-name} Compartment Admit Card 2023 for the exam scheduled on July 17, 2023 '
        ],
        'supplementary-hall-ticket' => [
            'h1' => '{board-name} Compartment Exam Admit Card 2023',
            'title' => '{board-name} Compartment Exam Admit Card 2023',
            'description' => 'Check out the release date of {board-name} Compartment Admit Card 2023 for the exam scheduled on July 17, 2023'
        ],
        'supplementary-result' => [
            'h1' => '{board-name} Compartment Result 2023: Steps to Check',
            'title' => '{board-name} Compartment Result 2023: Steps to Check',
            'description' => '{board-name} Supplementary Result 2023 has been declared on 20th June 2023. The direct link to check Supplementary Result 2023 is shared here as the result has been announced'
        ],
        'answer-key-dropdown' => [
            'h1' => '{board-name} {subject} Answer Key 2023, Set 1,2,3 Paper Solution: Download PDF',
            'title' => '{board-name} {subject} Answer Key 2023, Set 1,2,3 Paper Solution: Download PDF',
            'description' => '{board-name} {subject} Answer Key 2023 and Paper Solution of Both the papers Basic and Standard. Check the {subject} Answer Key 2023 for Questions and Answers '
        ],
        'notes-dropdown' => [
            'h1' => '{board-name} {subject} Notes 2023: Download PDF',
            'title' => '{board-name} {subject} notes 2023: Download PDF',
            'description' => '{board-name} {subject} Notes includes chapter wise explanations for all the topic provided in the curriculum.'
        ],
        'question-paper-dropdown' => [
            'h1' => '{board-name} {subject} Maths Question Paper 2023',
            'title' => '{board-name} {subject} Maths Question Paper 2023',
            'description' => '{board-name} {subject} Question Paper 2023 is provided in PDF format, which can be downloaded for free. Use the Question Paper 2023 to enhance your preparation.'
        ],
        'books-dropdown' => [
            'h1' => '{board-name} {subject} Textbook: Download PDF',
            'title' => '{board-name} {subject} Textbook: Download PDF',
            'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
        ],
        'passing-marks' => [
            'h1' => '{board-name} Passing Marks {year} - Check {board-name} Theory, Practical, Minimum, Maximum Marks',
            'title' => '{board-name} Passing Marks {year} - Check {board-name} Theory, Practical, Minimum, Maximum Marks',
            'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
        ],
        'grading-system' => [
            'h1' => '{board-name} Grading System {year} - Check {board-name} Grading System and Passing Marks',
            'title' => '{board-name} Grading System {year} - Check {board-name} Grading System and Passing Marks',
            'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
        ],
        'marksheet' => [
            'h1' => '{board-name} Marksheet {year} - Download {board-name} Original Mark Sheet Here, Direct Link',
            'title' => '{board-name} Marksheet {year} - Download {board-name} Original Mark Sheet Here, Direct Link',
            'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
        ],
        'practical-exam-datesheet' => [
            'h1' => '{board-name} Practical Exam Date Sheet {year} - Check {board-name} Practical Date Sheet',
            'title' => '{board-name} Practical Exam Date Sheet {year} - Check {board-name} Practical Date Sheet',
            'description' => 'Download the {board-name} Books Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
        ],
        'deleted-syllabus' => [
            'h1' => '{board-name} Deleted Syllabus {year} - Download Subject Wise Deleted Syllabus Here',
            'title' => '{board-name} Deleted Syllabus {year} - Download Subject Wise Deleted Syllabus Here',
            'description' => 'Download the {board-name} Deleted Syllabus Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
        ],
        'deleted-syllabus-dropdown' => [
            'h1' => '{board-name} {subject} Deleted Syllabus {year} - Check {board-name} {subject} Deleted Syllabus',
            'title' => '{board-name} {subject} Deleted Syllabus {year} - Check {board-name} {subject} Deleted Syllabus',
            'description' => 'Download the {board-name} Deleted Syllabus Subject wise for Mathematics, Social Science and Science, Accounting, Psychology, Syllabus, Model Papers and so on.'
        ]
    ];

    public static $boardDates = [
        '-' => '---Select Date For---',
        'exam-date' => 'Exam Date',
        'result-date' => 'Result Date'
    ];

    public static function getBoardDefaultSeoInfo($boardName, $page)
    {
        $year =  '';
        $seoInfo = self::$boardDefaultSeoInfo[$page] ?? [];

        if (empty($seoInfo)) {
            return [];
        }

        $data = [];
        foreach ($seoInfo as $key => $value) {
            $data[$key] = strtr($value, [
                '{board-name}' => $boardName,
                '{year}' => self::YEAR,
            ]);
        }
        return $data;
    }

    public static function getBoardDefaultSubPageSeoInfo($boardName, $page, $type = null)
    {
        $year =  '';
        if ($type != null) {
            if ($type == 'supplementary') {
                $tempPage = $type . '-' . $page;
                $seoInfo = self::$boardDefaultSeoInfo[$tempPage] ?? [];
                $page = ucwords(strtolower(str_replace('-', ' ', $page)));
            } else {
                $tempPage = $type . '-dropdown';
                $seoInfo = self::$boardDefaultSeoInfo[$tempPage] ?? [];
                $page = ucwords(strtolower(str_replace('-', ' ', $page)));
            }
        } else {
            $seoInfo = self::$boardDefaultSeoInfo[$page] ?? [];
        }

        if (empty($seoInfo)) {
            return [];
        }

        $data = [];
        foreach ($seoInfo as $key => $value) {
            $data[$key] = strtr($value, [
                '{board-name}' => $boardName,
                '{year}' => self::YEAR,
                '{subject}' => $page,
            ]);
        }
        return $data;
    }

    /**
     * Parse content to replace hash tags
     * @param $content string
     * @return string
     */
    public static function parseBoardContent(string $content, $year = self::YEAR): string
    {
        $data = [];
        foreach (self::$_hashTagYear as $key => $value) {
            if ($key) {
                $data[$key] = $year;
            }
        }

        return strtr(html_entity_decode($content), $data);
    }

    public static $redirectBoards = [
        'jharkhand-jac-board-12th' => 'jac-12th',
        'cgbse-10th-chhattisgarh-board' => 'cgbse-10th'
    ];


    public static function parseBoardUrl(string $content, string $boardSlug): string
    {

        $board = Board::find()->select(['slug', 'old_slug'])->where(['slug' => $boardSlug])->one();

        // if(!empty($board->old_slug)){
        //     $slug = $board->slug;
        // }

        $data = [];

        $urls = [
            '{board_slug}/b' => 'boards/{board_slug}',
            '{board_slug}-time-table/b' => 'boards/{board_slug}-date-sheet',
            '{board_slug}-registration-form/b' => 'boards/{board_slug}-registration-form',
            '{board_slug}-admit-card/b' => 'boards/{board_slug}-admit-card',
            '{board_slug}-syllabus/b' => 'boards/{board_slug}-syllabus',
            '{board_slug}-deleted-syllabus/b' => 'boards/{board_slug}-deleted-syllabus',
            '{board_slug}-results/b' => 'boards/{board_slug}-results',
            '{board_slug}-supplementary/b' => 'boards/{board_slug}-supplementary',
            '{board_slug}-sample-papers/b' => 'boards/{board_slug}-sample-papers',
            '{board_slug}-previous-year-question-papers/b' => 'boards/{board_slug}-previous-year-question-papers',
            '{board_slug}-exam-pattern/b' => 'boards/{board_slug}-exam-pattern',
            '{board_slug}-toppers/b' => 'boards/{board_slug}-toppers',
            '{board_slug}-registration-form/b' => 'boards/{board_slug}-registration-form',
            '{board_slug}-preparation/b' => 'boards/{board_slug}-preparation',
            '{board_slug}-answer-key/b' => 'boards/{board_slug}-answer-key',
            '{board_slug}-books/b' => 'boards/{board_slug}-books',
            '{board_slug}-routine/b' => 'boards/{board_slug}-date-sheet',
            '{board_slug}-preparation/b' => 'boards/{board_slug}-preparation',
            '{board_slug}-application-form/b' => 'boards/{board_slug}-application-form',
            '{board_slug}-hall-ticket/b' => 'boards/{board_slug}-admit-card',
            '{board_slug}-biology-answer-key/b' => 'boards/{board_slug}-biology-answer-key',
            '{board_slug}-geography-answer-key/b' => 'boards/{board_slug}-geography-answer-key',
            '{board_slug}-reference-books/b' => 'boards/{board_slug}-books',
            '{board_slug}-supplementary-admit-card-smy/b' => 'boards/{board_slug}-supplementary-admit-card',
            '{board_slug}-supplementary-time-table-smy/b' => 'boards/{board_slug}-supplementary-time-table',
            '{board_slug}-supplementary-result-smy/b' => 'boards/{board_slug}-supplementary-result',
            '{board_slug}-supplementary-data-sheet-smy/b' => 'boards/{board_slug}-supplementary-data-sheet',
            '{board_slug}-supplementary-hall-ticket-smy/b' => 'boards/{board_slug}-supplementary-hall-ticket',
            '{board_slug}-mathematics-question-paper/b' => 'boards/{board_slug}-mathematics-question-paper',
            '{board_slug}-physics-question-paper/b' => 'boards/{board_slug}-physics-question-paper',
            '{board_slug}-chemistry-question-paper/b' => 'boards/{board_slug}-chemistry-question-paper',
            '{board_slug}-biology-question-paper/b' => 'boards/{board_slug}-biology-question-paper',
            '{board_slug}-computer-science-question-paper/b' => 'boards/{board_slug}-computer-science-question-paper',
            '{board_slug}-economics-question-paper/b' => 'boards/{board_slug}-economics-question-paper',
            '{board_slug}-english-question-paper/b' => 'boards/{board_slug}-english-question-paper',
            '{board_slug}-business-studies-question-paper/b' => 'boards/{board_slug}-business-studies-question-paper',
            '{board_slug}-accountancy-question-paper/b' => 'boards/{board_slug}-accountancy-question-paper',
            '{board_slug}-hindi-question-paper/b' => 'boards/{board_slug}-hindi-question-paper',
            '{board_slug}-home-science-question-paper/b' => 'boards/{board_slug}-home-science-question-paper',
            '{board_slug}-physical-education-question-paper/b' => 'boards/{board_slug}-physical-education-question-paper',
            '{board_slug}-history-question-paper/b' => 'boards/{board_slug}-history-question-paper',
            '{board_slug}-geography-question-paper/b' => 'boards/{board_slug}-geography-question-paper',
            '{board_slug}-political-science-question-paper/b' => 'boards/{board_slug}-political-science-question-paper',
            '{board_slug}-psychology-question-paper/b' => 'boards/{board_slug}-psychology-question-paper',
            '{board_slug}-sanskrit-question-paper/b' => 'boards/{board_slug}-sanskrit-question-paper',
            '{board_slug}-sociology-question-paper/b' => 'boards/{board_slug}-sociology-question-paper',
            '{board_slug}-social-science-question-paper/b' => 'boards/{board_slug}-social-science-question-paper',
            '{board_slug}-mathematics-syllabus/b' => 'boards/{board_slug}-mathematics-syllabus',

            '{board_slug}-physics-syllabus/b' => 'boards/{board_slug}-physics-syllabus',
            '{board_slug}-chemistry-syllabus/b' => 'boards/{board_slug}-chemistry-syllabus',
            '{board_slug}-biology-syllabus/b' => 'boards/{board_slug}-biology-syllabus',
            '{board_slug}-computer-science-syllabus/b' => 'boards/{board_slug}-computer-science-syllabus',
            '{board_slug}-economics-syllabus/b' => 'boards/{board_slug}-economics-syllabus',
            '{board_slug}-english-syllabus/b' => 'boards/{board_slug}-english-syllabus',
            '{board_slug}-business-studies-syllabus/b' => 'boards/{board_slug}-business-studies-syllabus',
            '{board_slug}-accountancy-syllabus/b' => 'boards/{board_slug}-accountancy-syllabus',
            '{board_slug}-hindi-syllabus/b' => 'boards/{board_slug}-hindi-syllabus',
            '{board_slug}-home-science-syllabus/b' => 'boards/{board_slug}-home-science-syllabus',
            '{board_slug}-physical-education-syllabus/b' => 'boards/{board_slug}-physical-education-syllabus',
            '{board_slug}-history-syllabus/b' => 'boards/{board_slug}-history-syllabus',
            '{board_slug}-geography-syllabus/b' => 'boards/{board_slug}-geography-syllabus',
            '{board_slug}-political-science-syllabus/b' => 'boards/{board_slug}-political-science-syllabus',
            '{board_slug}-psychology-syllabus/b' => 'boards/{board_slug}-psychology-syllabus',
            '{board_slug}-sanskrit-syllabus/b' => 'boards/{board_slug}-sanskrit-syllabus',
            '{board_slug}-sociology-syllabus/b' => 'boards/{board_slug}-sociology-syllabus',
            '{board_slug}-social-science-syllabus/b' => 'boards/{board_slug}-social-science-syllabus',

            '{board_slug}-mathematics-answer-key/b' => 'boards/{board_slug}-mathematics-answer-key',
            '{board_slug}-physics-answer-key/b' => 'boards/{board_slug}-physics-answer-key',
            '{board_slug}-chemistry-answer-key/b' => 'boards/{board_slug}-chemistry-answer-key',
            '{board_slug}-biology-answer-key/b' => 'boards/{board_slug}-biology-answer-key',
            '{board_slug}-computer-science-answer-key/b' => 'boards/{board_slug}-computer-science-answer-key',
            '{board_slug}-economics-answer-key/b' => 'boards/{board_slug}-economics-answer-key',
            '{board_slug}-english-answer-key/b' => 'boards/{board_slug}-english-answer-key',
            '{board_slug}-business-studies-answer-key/b' => 'boards/{board_slug}-business-studies-answer-key',
            '{board_slug}-accountancy-answer-key/b' => 'boards/{board_slug}-accountancy-answer-key',
            '{board_slug}-hindi-answer-key/b' => 'boards/{board_slug}-hindi-answer-key',
            '{board_slug}-home-science-answer-key/b' => 'boards/{board_slug}-home-science-answer-key',
            '{board_slug}-physical-education-answer-key/b' => 'boards/{board_slug}-physical-education-answer-key',
            '{board_slug}-history-answer-key/b' => 'boards/{board_slug}-history-answer-key',
            '{board_slug}-geography-answer-key/b' => 'boards/{board_slug}-geography-answer-key',
            '{board_slug}-political-science-answer-key/b' => 'boards/{board_slug}-political-science-answer-key',
            '{board_slug}-psychology-answer-key/b' => 'boards/{board_slug}-psychology-answer-key',
            '{board_slug}-sanskrit-answer-key/b' => 'boards/{board_slug}-sanskrit-answer-key',
            '{board_slug}-sociology-answer-key/b' => 'boards/{board_slug}-sociology-answer-key',
            '{board_slug}-social-science-answer-key/b' => 'boards/{board_slug}-social-science-answer-key',

            '{board_slug}-science-deleted-syllabus/b' => 'boards/{board_slug}-science-deleted-syllabus',
            '{board_slug}-mathematics-deleted-syllabus/b' => 'boards/{board_slug}-mathematics-deleted-syllabus',
            '{board_slug}-physics-deleted-syllabus/b' => 'boards/{board_slug}-physics-deleted-syllabus',
            '{board_slug}-chemistry-deleted-syllabus/b' => 'boards/{board_slug}-chemistry-deleted-syllabus',
            '{board_slug}-biology-deleted-syllabus/b' => 'boards/{board_slug}-biology-deleted-syllabus',
            '{board_slug}-computer-science-deleted-syllabus/b' => 'boards/{board_slug}-computer-science-deleted-syllabus',
            '{board_slug}-economics-deleted-syllabus/b' => 'boards/{board_slug}-economics-deleted-syllabus',
            '{board_slug}-english-deleted-syllabus/b' => 'boards/{board_slug}-english-deleted-syllabus',
            '{board_slug}-business-studies-deleted-syllabus/b' => 'boards/{board_slug}-business-studies-deleted-syllabus',
            '{board_slug}-accountancy-deleted-syllabus/b' => 'boards/{board_slug}-accountancy-deleted-syllabus',
            '{board_slug}-hindi-deleted-syllabus/b' => 'boards/{board_slug}-hindi-deleted-syllabus',
            '{board_slug}-home-science-deleted-syllabus/b' => 'boards/{board_slug}-home-science-deleted-syllabus',
            '{board_slug}-physical-education-deleted-syllabus/b' => 'boards/{board_slug}-physical-education-deleted-syllabus',
            '{board_slug}-history-deleted-syllabus/b' => 'boards/{board_slug}-history-deleted-syllabus',
            '{board_slug}-geography-deleted-syllabus/b' => 'boards/{board_slug}-geography-deleted-syllabus',
            '{board_slug}-political-science-deleted-syllabus/b' => 'boards/{board_slug}-political-science-deleted-syllabus',
            '{board_slug}-psychology-deleted-syllabus/b' => 'boards/{board_slug}-psychology-deleted-syllabus',
            '{board_slug}-sanskrit-deleted-syllabus/b' => 'boards/{board_slug}-sanskrit-deleted-syllabus',
            '{board_slug}-sociology-deleted-syllabus/b' => 'boards/{board_slug}-sociology-deleted-syllabus',
            '{board_slug}-social-science-deleted-syllabus/b' => 'boards/{board_slug}-social-science-deleted-syllabus',
        ];

        $newArry = [];
        $slugArr = [];
        foreach ($urls as $key => $value) {
            // Replace the string in each value
            $newArry[str_replace('{board_slug}', $board->old_slug ?? $board->slug, $key)] = str_replace('{board_slug}', $board->slug, $value);
            // $newArry[str_replace('{board_slug}', , $key)] = str_replace('{board_slug}', $boardSlug, $value);
        }
        foreach (array_keys($newArry) as $url) {
            if ((strpos($content, $url) == true)) {
                $data[$url] = $newArry[$url];
            }
        }

        return strtr($content, $data);
    }

    public static $boardCorrecturlFormat =
    [
        'books' => [
            'page' => 'reference-books',
            'board_contains' => 'reference',
        ],
        'syllabus' => [
            'page' => 'deleted-syllabus',
            'board_contains' => 'deleted',
        ],
    ];
}
