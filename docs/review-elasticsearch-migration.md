# Review Landing Page Elasticsearch Migration

## Overview

The Review landing page has been migrated from MongoDB to Elasticsearch for improved search performance and scalability.

## Changes Made

### 1. New Files Created

- `frontend/models/ReviewElasticSearch.php` - New search model using Elasticsearch
- `common/models/ReviewElastic.php` - Elasticsearch ActiveRecord model for reviews
- `console/controllers/ReviewElasticController.php` - Console commands for indexing reviews

### 2. Modified Files

- `frontend/controllers/ReviewController.php` - Updated to use ReviewElasticSearch instead of ReviewSearch
- `common/models/Review.php` - Added automatic Elasticsearch indexing on save

### 3. Key Features

- **Elasticsearch Integration**: Uses Yii2 Elasticsearch extension for search operations
- **Faceted Search**: Supports filtering by state, city, course, stream, and batch
- **Sorting**: Supports sorting by rating (highest/lowest) and date (newest/oldest)
- **Pagination**: Maintains existing pagination functionality
- **Backward Compatibility**: Same interface as the original MongoDB implementation

## Setup Instructions

### 1. Create Elasticsearch Index

```bash
# Create the index and mapping
php yii review-elastic/create-index

# Or just update mapping if index exists
php yii review-elastic/update-mapping
```

### 2. Index Existing Reviews

```bash
# Index all approved reviews
php yii review-elastic/index-all

# Index a specific review
php yii review-elastic/index-review 123
```

### 3. Delete Review from Index

```bash
# Remove a review from Elasticsearch
php yii review-elastic/delete-review 123
```

## Data Structure

### Elasticsearch Document Fields

- `review_id` - Primary key
- `student_id`, `college_id`, `course_id` - Foreign keys
- `admission_year`, `batch` - Year information
- `status` - Review approval status
- `review_created_at`, `review_updated_at` - Timestamps
- `review_overall_rating` - Calculated overall rating
- `student_name`, `student_profile_pic` - Student information
- `college_name`, `college_slug`, `college_display_name` - College information
- `college_city_name`, `city_slug` - City information
- `college_state_name`, `state_slug` - State information
- `course_name`, `course_slug` - Course information
- `stream_name`, `stream_slug` - Stream information
- `review_slug`, `review_title`, `review_content` - Review content
- `category_ratings` - Nested array of category ratings

### Index Configuration

- **Index Name**: `reviews`
- **Type Name**: `review`
- **Mapping**: Defined in `ReviewElastic::mapping()`

## Search Functionality

### Filters Supported

- **State**: Filter by state slug(s)
- **City**: Filter by city slug(s)
- **Course**: Filter by course slug(s)
- **Stream**: Filter by stream slug(s)
- **Batch**: Filter by admission year(s)

### Sorting Options

- `newest_rating` - Newest reviews first (default)
- `oldest_rating` - Oldest reviews first
- `highest_rating` - Highest rated reviews first
- `lowest_rating` - Lowest rated reviews first

### Faceted Search

The implementation provides faceted search with counts for each filter option, allowing users to see how many results are available for each filter value.

## Automatic Indexing

Reviews are automatically indexed to Elasticsearch when:

1. A new review is created
2. An existing review is updated
3. Review status changes

This happens in the `Review::afterSave()` method, but only in production environment (`YII_ENV == 'prod'`).

## Performance Considerations

- **Batch Indexing**: Use `index-all` command for initial setup or bulk re-indexing
- **Real-time Updates**: Individual reviews are indexed automatically on save
- **Facet Queries**: Each facet (state, city, course, stream, batch) requires a separate Elasticsearch query
- **Caching**: Consider implementing caching for facet results if needed

## Troubleshooting

### Common Issues

1. **Index Not Found**: Run `php yii review-elastic/create-index`
2. **Mapping Conflicts**: Run `php yii review-elastic/update-mapping`
3. **Missing Data**: Run `php yii review-elastic/index-all` to re-index all reviews
4. **Search Not Working**: Check Elasticsearch connection and index status

### Debugging

- Enable Elasticsearch query logging in Yii2 configuration
- Use Elasticsearch HEAD plugin or Kibana to inspect index structure
- Check console output when running indexing commands

## Migration Notes

- The original MongoDB-based `ReviewSearch` model is preserved for rollback purposes
- The new implementation maintains the same API interface
- All existing view templates and JavaScript code continue to work without changes
- Filter URLs and pagination remain unchanged

## Future Enhancements

- Add full-text search capabilities
- Implement search suggestions/autocomplete
- Add more advanced filtering options
- Optimize facet queries with caching
- Add search analytics and logging
